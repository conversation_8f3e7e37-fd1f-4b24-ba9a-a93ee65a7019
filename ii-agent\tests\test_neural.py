#!/usr/bin/env python3
"""
Test RUV-FANN Neural Integration
"""
import sys
import os

# Add the src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_ruv_fann_core():
    """Test the RUV-FANN core directly"""
    print("🧠 Testing RUV-FANN Neural Core...")
    
    try:
        # Import the core directly
        from ii_agent.neural.ruv_fann_neural_core import RUVFANNNeuralSwarm, AgentType
        print("✅ RUV-FANN core imported successfully")
        
        # Create neural swarm
        swarm = RUVFANNNeuralSwarm()
        print("✅ Neural swarm created")
        
        # Create agents
        coder_agent = swarm.create_neural_agent(AgentType.CODER, "Python Specialist")
        researcher_agent = swarm.create_neural_agent(AgentType.RESEARCHER, "Research Specialist")
        print(f"✅ Created {len(swarm.agents)} neural agents")
        
        # Test pattern matching
        test_messages = [
            "I need help debugging a Python function",
            "Can you research the latest AI frameworks?", 
            "Help me analyze this dataset",
            "I need to design a user interface"
        ]
        
        print("\n🧠 Testing Cognitive Pattern Matching:")
        for msg in test_messages:
            pattern, score = swarm.cognitive_pattern_match(msg)
            print(f"   '{msg[:30]}...' → {pattern} ({score:.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ RUV-FANN test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_neural():
    """Test the SimpleNeuralAgent"""
    print("\n🧠 Testing SimpleNeuralAgent...")
    
    try:
        # Import through proper module path
        sys.path.insert(0, 'src')
        from ii_agent.neural.simple_neural import SimpleNeuralAgent
        
        print("✅ SimpleNeuralAgent imported successfully")
        
        # Create agent
        agent = SimpleNeuralAgent(enable_neural=True)
        print("✅ Neural agent created")
        
        # Test analysis
        test_msg = "I need help with machine learning algorithms"
        analysis = agent.get_neural_analysis(test_msg)
        
        print("✅ Neural analysis completed:")
        print(f"   Agent Type: {analysis.get('agent_type')}")
        print(f"   Confidence: {analysis.get('confidence', 0)*100:.1f}%")
        print(f"   Strategy: {analysis.get('strategy')}")
        print(f"   Reasoning: {analysis.get('reasoning')}")
        
        return True
        
    except Exception as e:
        print(f"❌ SimpleNeuralAgent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧠 RUV-FANN Neural Integration Test")
    print("=" * 50)
    
    # Test core functionality
    core_success = test_ruv_fann_core()
    
    # Test neural agent
    agent_success = test_simple_neural()
    
    print("\n" + "=" * 50)
    if core_success and agent_success:
        print("🎉 All tests passed! RUV-FANN integration is working.")
    else:
        print("❌ Some tests failed. Check the errors above.")
