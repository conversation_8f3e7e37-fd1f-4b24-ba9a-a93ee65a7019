#!/usr/bin/env python3
"""
Comprehensive Docker System Test
Tests the running Docker containers to verify the multi-agent system is truly working.
"""

import asyncio
import json
import logging
import requests
import time
import websockets
from typing import Dict, Any

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DockerSystemTest:
    """Comprehensive test suite for the Docker-deployed multi-agent system"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.ws_url = "ws://localhost:8000/ws"
        self.results = {
            "backend_connectivity": {},
            "websocket_connection": {},
            "multi_agent_coordination": {},
            "neural_processing": {},
            "monitoring_systems": {},
            "overall_status": "unknown"
        }
    
    async def run_comprehensive_test(self):
        """Run all tests to verify the system is truly working"""
        logger.info("🚀 Starting comprehensive Docker system test...")
        
        # Test 1: Backend connectivity
        await self.test_backend_connectivity()
        
        # Test 2: WebSocket connection
        await self.test_websocket_connection()
        
        # Test 3: Multi-agent coordination
        await self.test_multi_agent_coordination()
        
        # Test 4: Neural processing
        await self.test_neural_processing()
        
        # Test 5: Monitoring systems
        await self.test_monitoring_systems()
        
        # Generate final report
        self.generate_final_report()
    
    async def test_backend_connectivity(self):
        """Test backend API connectivity"""
        logger.info("🔌 Testing backend connectivity...")
        
        try:
            # Test root endpoint
            response = requests.get(f"{self.base_url}/", timeout=10)
            if response.status_code == 200:
                self.results["backend_connectivity"]["root_endpoint"] = "✅ WORKING"
            else:
                self.results["backend_connectivity"]["root_endpoint"] = f"❌ FAILED: {response.status_code}"
            
            # Test if backend is serving content
            if "text/html" in response.headers.get("content-type", ""):
                self.results["backend_connectivity"]["content_serving"] = "✅ WORKING"
            else:
                self.results["backend_connectivity"]["content_serving"] = "⚠️ PARTIAL"
            
        except Exception as e:
            self.results["backend_connectivity"]["connection"] = f"❌ FAILED: {e}"
    
    async def test_websocket_connection(self):
        """Test WebSocket connection and basic communication"""
        logger.info("🔗 Testing WebSocket connection...")
        
        try:
            async with websockets.connect(self.ws_url, timeout=10) as websocket:
                # Test connection
                self.results["websocket_connection"]["connection"] = "✅ CONNECTED"
                
                # Test basic message
                test_message = {
                    "type": "ping",
                    "data": {"message": "test connection"}
                }
                
                await websocket.send(json.dumps(test_message))
                
                # Wait for response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    self.results["websocket_connection"]["communication"] = "✅ WORKING"
                    self.results["websocket_connection"]["response_type"] = response_data.get("type", "unknown")
                except asyncio.TimeoutError:
                    self.results["websocket_connection"]["communication"] = "⚠️ NO_RESPONSE"
                
        except Exception as e:
            self.results["websocket_connection"]["connection"] = f"❌ FAILED: {e}"
    
    async def test_multi_agent_coordination(self):
        """Test multi-agent coordination through WebSocket"""
        logger.info("🤖 Testing multi-agent coordination...")
        
        try:
            async with websockets.connect(self.ws_url, timeout=15) as websocket:
                # Send multi-agent task
                coordination_message = {
                    "type": "coordinate_task",
                    "data": {
                        "task": "Test multi-agent coordination in Docker environment",
                        "strategy": "mixture_of_agents",
                        "agents": 3
                    }
                }
                
                await websocket.send(json.dumps(coordination_message))
                
                # Wait for coordination response
                start_time = time.time()
                coordination_complete = False
                agent_responses = []
                
                while time.time() - start_time < 30:  # 30 second timeout
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        response_data = json.loads(response)
                        
                        if response_data.get("type") == "agent_response":
                            agent_responses.append(response_data)
                        
                        if response_data.get("type") == "coordination_complete":
                            coordination_complete = True
                            self.results["multi_agent_coordination"]["completion"] = "✅ COMPLETED"
                            self.results["multi_agent_coordination"]["execution_time"] = response_data.get("execution_time_ms", 0)
                            break
                            
                    except asyncio.TimeoutError:
                        continue
                
                if not coordination_complete:
                    self.results["multi_agent_coordination"]["completion"] = "❌ TIMEOUT"
                
                self.results["multi_agent_coordination"]["agent_responses"] = len(agent_responses)
                
                if len(agent_responses) > 0:
                    self.results["multi_agent_coordination"]["agents_active"] = "✅ WORKING"
                else:
                    self.results["multi_agent_coordination"]["agents_active"] = "❌ NO_AGENTS"
                
        except Exception as e:
            self.results["multi_agent_coordination"]["error"] = f"❌ FAILED: {e}"
    
    async def test_neural_processing(self):
        """Test neural processing capabilities"""
        logger.info("🧠 Testing neural processing...")
        
        try:
            async with websockets.connect(self.ws_url, timeout=15) as websocket:
                # Send neural processing task
                neural_message = {
                    "type": "neural_analysis",
                    "data": {
                        "text": "Analyze this text using neural networks in the Docker environment",
                        "analysis_type": "comprehensive"
                    }
                }
                
                await websocket.send(json.dumps(neural_message))
                
                # Wait for neural response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                    response_data = json.loads(response)
                    
                    if "confidence" in response_data.get("data", {}):
                        self.results["neural_processing"]["confidence_scoring"] = "✅ WORKING"
                    
                    if "analysis" in response_data.get("data", {}):
                        self.results["neural_processing"]["analysis"] = "✅ WORKING"
                    
                    self.results["neural_processing"]["response_received"] = "✅ SUCCESS"
                    
                except asyncio.TimeoutError:
                    self.results["neural_processing"]["response_received"] = "❌ TIMEOUT"
                
        except Exception as e:
            self.results["neural_processing"]["error"] = f"❌ FAILED: {e}"
    
    async def test_monitoring_systems(self):
        """Test monitoring and metrics systems"""
        logger.info("📊 Testing monitoring systems...")
        
        # Test Prometheus
        try:
            response = requests.get("http://localhost:9090/api/v1/query?query=up", timeout=5)
            if response.status_code == 200:
                self.results["monitoring_systems"]["prometheus"] = "✅ WORKING"
            else:
                self.results["monitoring_systems"]["prometheus"] = f"❌ FAILED: {response.status_code}"
        except Exception as e:
            self.results["monitoring_systems"]["prometheus"] = f"❌ FAILED: {e}"
        
        # Test Grafana
        try:
            response = requests.get("http://localhost:3001/api/health", timeout=5)
            if response.status_code == 200:
                self.results["monitoring_systems"]["grafana"] = "✅ WORKING"
            else:
                self.results["monitoring_systems"]["grafana"] = f"❌ FAILED: {response.status_code}"
        except Exception as e:
            self.results["monitoring_systems"]["grafana"] = f"❌ FAILED: {e}"
        
        # Test cAdvisor
        try:
            response = requests.get("http://localhost:8080/api/v1.3/machine", timeout=5)
            if response.status_code == 200:
                self.results["monitoring_systems"]["cadvisor"] = "✅ WORKING"
            else:
                self.results["monitoring_systems"]["cadvisor"] = f"❌ FAILED: {response.status_code}"
        except Exception as e:
            self.results["monitoring_systems"]["cadvisor"] = f"❌ FAILED: {e}"
    
    def generate_final_report(self):
        """Generate comprehensive test report"""
        print("\n" + "="*80)
        print("🚀 DOCKER SYSTEM COMPREHENSIVE TEST REPORT")
        print("="*80)
        
        total_tests = 0
        passed_tests = 0
        
        for category, tests in self.results.items():
            if category == "overall_status":
                continue
            
            print(f"\n📂 {category.upper().replace('_', ' ')}:")
            
            for test_name, result in tests.items():
                total_tests += 1
                if "✅" in str(result):
                    passed_tests += 1
                print(f"   {test_name}: {result}")
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"\n🎯 FINAL VERDICT:")
        print(f"   Success Rate: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        if success_rate >= 90:
            print("   ✅ SYSTEM IS TRULY WORKING IN DOCKER!")
            self.results["overall_status"] = "fully_working"
        elif success_rate >= 70:
            print("   ⚠️ SYSTEM MOSTLY WORKING, MINOR ISSUES")
            self.results["overall_status"] = "mostly_working"
        elif success_rate >= 50:
            print("   ⚠️ SYSTEM PARTIALLY WORKING")
            self.results["overall_status"] = "partially_working"
        else:
            print("   ❌ SYSTEM HAS MAJOR ISSUES")
            self.results["overall_status"] = "not_working"
        
        print("="*80)

async def main():
    """Run the comprehensive Docker system test"""
    tester = DockerSystemTest()
    await tester.run_comprehensive_test()
    
    # Return exit code based on results
    if tester.results["overall_status"] in ["fully_working", "mostly_working"]:
        return 0
    else:
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
