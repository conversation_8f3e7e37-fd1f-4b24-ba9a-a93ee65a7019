# Docker Compose override for multi-agent testing
# Usage: docker-compose -f docker-compose.yaml -f docker-compose.multi-agent.yaml up

services:
  backend:
    build:
      context: .
      dockerfile: docker/backend/Dockerfile.multi-agent
    environment:
      # Enable multi-agent mode
      - II_AGENT_MULTI_AGENT=true
      # Existing environment variables
      - GOOGLE_APPLICATION_CREDENTIALS=/app/google-application-credentials.json
      - STATIC_FILE_BASE_URL=${STATIC_FILE_BASE_URL:-http://localhost:8000}
      - BASE_URL=${BASE_URL}
      - COMPOSE_PROJECT_NAME=${COMPOSE_PROJECT_NAME}
      - HOST_WORKSPACE_PATH=${HOME}/.ii_agent/workspace
      - CODE_SERVER_PORT=${CODE_SERVER_PORT:-9000}
      # Python path for multi-agent integration (add to existing path)
      - PYTHONPATH=/app/src:/app/ii_agent_integration:/app
    volumes:
      # Mount our multi-agent integration (correct path)
      - ./ii_agent_integration:/app/ii_agent_integration
      # Existing volumes
      - ${GOOGLE_APPLICATION_CREDENTIALS:-./docker/.dummy-credentials.json}:/app/google-application-credentials.json
      - ~/.ii_agent:/.ii_agent
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - ii

networks:
  ii:
