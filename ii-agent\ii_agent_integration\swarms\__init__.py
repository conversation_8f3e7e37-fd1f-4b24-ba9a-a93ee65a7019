"""
Swarms Framework Integration Package

Phase 3 implementation of Swarms framework integration with ii-agent ecosystem.
"""

from .swarms_integration import SwarmsFrameworkIntegration, SwarmsCoordinationPattern, CoordinationResult
from .backend_adapter import SwarmsBackendAdapter
from .coordination_patterns import Coordination<PERSON>atterns
from .neural_swarms_agent import NeuralSwarmsAgent
from .adapters.kyegomez_swarms_adapter import KyegomezSwarmsAdapter
from .enterprise_agent_pool import EnterpriseAgentPool
from .swarms_coordinator import SwarmsCoordinator

__all__ = [
    'SwarmsFrameworkIntegration',
    'SwarmsCoordinationPattern', 
    'CoordinationResult',
    'CoordinationPatterns',
    'NeuralSwarmsAgent',
    'EnterpriseAgentPool',
    'SwarmsCoordinator',
    'SwarmsBackendAdapter',
    'KyegomezSwarmsAdapter'
]
