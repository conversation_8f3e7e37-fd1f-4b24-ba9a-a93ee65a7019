#!/usr/bin/env python3
"""Fix indentation issues in TaskCoordinator"""

import re

# Read the file
file_path = r"c:\Users\<USER>\OneDrive\Desktop\ph\ii-agent\ii_agent_integration\agents\task_coordinator.py"

with open(file_path, 'r', encoding='utf-8') as f:
    content = f.read()

# Fix the pattern "        async with self._lock:" to "            async with self._lock:"
# This happens when it should be indented under a try block
lines = content.split('\n')
fixed_lines = []

for i, line in enumerate(lines):
    # If this line is "        async with self._lock:" and the previous line ends with ":"
    if line == "        async with self._lock:":
        # Check if previous non-empty line suggests this should be more indented
        prev_line_idx = i - 1
        while prev_line_idx >= 0 and lines[prev_line_idx].strip() == "":
            prev_line_idx -= 1
        
        if prev_line_idx >= 0:
            prev_line = lines[prev_line_idx]
            # If previous line is try: or similar, this should be indented more
            if prev_line.strip().endswith(":") and ("try:" in prev_line or "except" in prev_line):
                fixed_lines.append("            async with self._lock:")
                continue
    
    fixed_lines.append(line)

# Write back the fixed content
with open(file_path, 'w', encoding='utf-8') as f:
    f.write('\n'.join(fixed_lines))

print("Fixed TaskCoordinator indentation issues")
