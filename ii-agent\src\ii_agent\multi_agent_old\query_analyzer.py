"""
Query Analyzer - Phase 2 Multi-Agent Query Intelligence

Analyzes incoming queries to determine:
- Complexity level (simple, medium, complex, expert)
- Required capabilities
- Optimal coordination strategy (single, multi-agent, swarms)
- Resource requirements
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class QueryComplexity(Enum):
    """Query complexity levels"""
    SIMPLE = "simple"          # Single agent can handle
    MEDIUM = "medium"          # May benefit from multi-agent
    COMPLEX = "complex"        # Requires multi-agent coordination  
    EXPERT = "expert"          # Requires swarms coordination


class CoordinationStrategy(Enum):
    """Coordination strategies"""
    SINGLE_AGENT = "single_agent"
    MULTI_AGENT = "multi_agent"
    SWARMS_COORDINATION = "swarms_coordination"


@dataclass
class QueryAnalysis:
    """Complete query analysis result"""
    complexity: QueryComplexity
    coordination_strategy: CoordinationStrategy
    required_capabilities: List[str]
    confidence_score: float
    estimated_agents_needed: int
    processing_time_estimate: float
    reasoning: str
    metadata: Dict[str, Any]


class QueryAnalyzer:
    """Intelligent query analyzer for multi-agent coordination"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Complexity indicators
        self.simple_indicators = [
            "what is", "define", "explain", "how to", "show me",
            "list", "tell me", "help with", "quick", "simple"
        ]
        
        self.medium_indicators = [
            "analyze", "compare", "summarize", "process", "generate",
            "create", "build", "design", "implement", "optimize"
        ]
        
        self.complex_indicators = [
            "multi-step", "coordinate", "integrate", "comprehensive",
            "end-to-end", "workflow", "pipeline", "orchestrate",
            "complex analysis", "deep dive", "thorough investigation"
        ]
        
        self.expert_indicators = [
            "swarm", "distributed", "parallel processing", "large scale",
            "enterprise", "production", "fault tolerant", "high availability",
            "advanced coordination", "massive dataset"
        ]
        
        # Capability mapping
        self.capability_keywords = {
            "text_analysis": ["analyze text", "sentiment", "nlp", "language"],
            "data_processing": ["process data", "transform", "clean", "etl"],
            "code_generation": ["generate code", "programming", "implementation"],
            "research": ["research", "investigate", "gather information"],
            "planning": ["plan", "strategy", "roadmap", "timeline"],
            "coordination": ["coordinate", "orchestrate", "manage"],
            "visualization": ["chart", "graph", "visualize", "plot"],
            "testing": ["test", "validate", "verify", "qa"],
            "documentation": ["document", "write docs", "explain"],
            "optimization": ["optimize", "performance", "efficiency"]
        }
    
    async def analyze_query(self, query_text: str, context: Optional[Dict[str, Any]] = None) -> QueryAnalysis:
        """
        Analyze query to determine optimal coordination strategy.
        
        Args:
            query_text: The user's query
            context: Optional context information
            
        Returns:
            QueryAnalysis with coordination recommendations
        """
        try:
            query_lower = query_text.lower()
            
            # Analyze complexity
            complexity = self._analyze_complexity(query_lower)
            
            # Determine capabilities needed
            capabilities = self._identify_capabilities(query_lower)
            
            # Determine coordination strategy
            strategy = self._determine_coordination_strategy(complexity, capabilities, query_lower)
            
            # Estimate resource requirements
            agents_needed = self._estimate_agents_needed(complexity, capabilities)
            processing_time = self._estimate_processing_time(complexity, len(query_text))
            
            # Calculate confidence score
            confidence = self._calculate_confidence(query_lower, complexity, capabilities)
            
            # Generate reasoning
            reasoning = self._generate_reasoning(complexity, strategy, capabilities)
            
            analysis = QueryAnalysis(
                complexity=complexity,
                coordination_strategy=strategy,
                required_capabilities=capabilities,
                confidence_score=confidence,
                estimated_agents_needed=agents_needed,
                processing_time_estimate=processing_time,
                reasoning=reasoning,
                metadata={
                    "query_length": len(query_text),
                    "word_count": len(query_text.split()),
                    "has_code": "```" in query_text or "code" in query_lower,
                    "has_urls": "http" in query_text or "www." in query_text,
                    "context_provided": context is not None
                }
            )
            
            self.logger.info(f"Query analyzed: {complexity.value} complexity, {strategy.value} strategy")
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing query: {e}")
            # Return safe fallback
            return QueryAnalysis(
                complexity=QueryComplexity.SIMPLE,
                coordination_strategy=CoordinationStrategy.SINGLE_AGENT,
                required_capabilities=["general"],
                confidence_score=0.5,
                estimated_agents_needed=1,
                processing_time_estimate=10.0,
                reasoning="Fallback analysis due to error",
                metadata={"error": str(e)}
            )
    
    def _analyze_complexity(self, query_lower: str) -> QueryComplexity:
        """Analyze query complexity based on indicators"""
        
        # Count indicators for each complexity level
        simple_count = sum(1 for indicator in self.simple_indicators if indicator in query_lower)
        medium_count = sum(1 for indicator in self.medium_indicators if indicator in query_lower)
        complex_count = sum(1 for indicator in self.complex_indicators if indicator in query_lower)
        expert_count = sum(1 for indicator in self.expert_indicators if indicator in query_lower)
        
        # Additional complexity factors
        word_count = len(query_lower.split())
        has_multiple_steps = any(word in query_lower for word in ["then", "after", "next", "step"])
        has_conditions = any(word in query_lower for word in ["if", "when", "unless", "condition"])
        
        # Scoring system
        complexity_score = 0
        
        if expert_count > 0:
            complexity_score += 4 * expert_count
        if complex_count > 0:
            complexity_score += 3 * complex_count
        if medium_count > 0:
            complexity_score += 2 * medium_count
        if simple_count > 0:
            complexity_score += 1 * simple_count
            
        # Additional scoring
        if word_count > 50:
            complexity_score += 2
        if has_multiple_steps:
            complexity_score += 2
        if has_conditions:
            complexity_score += 1
            
        # Determine complexity level
        if complexity_score >= 8:
            return QueryComplexity.EXPERT
        elif complexity_score >= 5:
            return QueryComplexity.COMPLEX
        elif complexity_score >= 2:
            return QueryComplexity.MEDIUM
        else:
            return QueryComplexity.SIMPLE
    
    def _identify_capabilities(self, query_lower: str) -> List[str]:
        """Identify required capabilities from query"""
        capabilities = []
        
        for capability, keywords in self.capability_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                capabilities.append(capability)
        
        # Default capability if none detected
        if not capabilities:
            capabilities.append("general")
            
        return capabilities
    
    def _determine_coordination_strategy(self, complexity: QueryComplexity, 
                                       capabilities: List[str], query_lower: str) -> CoordinationStrategy:
        """Determine optimal coordination strategy"""
        
        # Expert complexity always uses swarms
        if complexity == QueryComplexity.EXPERT:
            return CoordinationStrategy.SWARMS_COORDINATION
        
        # Complex queries with multiple capabilities use multi-agent
        if complexity == QueryComplexity.COMPLEX or len(capabilities) > 2:
            return CoordinationStrategy.MULTI_AGENT
        
        # Check for explicit coordination keywords
        coordination_keywords = ["coordinate", "collaborate", "distribute", "parallel"]
        if any(keyword in query_lower for keyword in coordination_keywords):
            return CoordinationStrategy.MULTI_AGENT
        
        # Default to single agent for simple/medium queries
        return CoordinationStrategy.SINGLE_AGENT
    
    def _estimate_agents_needed(self, complexity: QueryComplexity, capabilities: List[str]) -> int:
        """Estimate number of agents needed"""
        base_agents = {
            QueryComplexity.SIMPLE: 1,
            QueryComplexity.MEDIUM: 1,
            QueryComplexity.COMPLEX: 2,
            QueryComplexity.EXPERT: 3
        }
        
        # Add agents based on capabilities
        capability_multiplier = max(1, len(capabilities) // 2)
        
        return min(5, base_agents[complexity] + capability_multiplier)
    
    def _estimate_processing_time(self, complexity: QueryComplexity, query_length: int) -> float:
        """Estimate processing time in seconds"""
        base_time = {
            QueryComplexity.SIMPLE: 5.0,
            QueryComplexity.MEDIUM: 15.0,
            QueryComplexity.COMPLEX: 30.0,
            QueryComplexity.EXPERT: 60.0
        }
        
        # Add time based on query length
        length_factor = min(2.0, query_length / 500)
        
        return base_time[complexity] * (1 + length_factor)
    
    def _calculate_confidence(self, query_lower: str, complexity: QueryComplexity, 
                            capabilities: List[str]) -> float:
        """Calculate confidence score for the analysis"""
        confidence = 0.7  # Base confidence
        
        # Increase confidence for clear indicators
        if any(indicator in query_lower for indicator in self.simple_indicators):
            confidence += 0.1
        if any(indicator in query_lower for indicator in self.complex_indicators):
            confidence += 0.1
        if len(capabilities) > 1:
            confidence += 0.1
            
        return min(1.0, confidence)
    
    def _generate_reasoning(self, complexity: QueryComplexity, strategy: CoordinationStrategy,
                          capabilities: List[str]) -> str:
        """Generate human-readable reasoning for the analysis"""
        reasoning_parts = []
        
        reasoning_parts.append(f"Query complexity assessed as {complexity.value}")
        reasoning_parts.append(f"Recommended {strategy.value} coordination")
        
        if len(capabilities) > 1:
            reasoning_parts.append(f"Multiple capabilities required: {', '.join(capabilities)}")
        else:
            reasoning_parts.append(f"Primary capability: {capabilities[0]}")
            
        return ". ".join(reasoning_parts) + "."
