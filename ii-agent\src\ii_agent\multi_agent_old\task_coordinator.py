"""TaskCoordinator - Multi-Agent Task Management

Production-grade task coordination and distribution system.
Manages task lifecycle, assignment, and execution tracking.
"""

import asyncio
import logging
import uuid
from typing import Dict, List, Any, Optional, Callable, Set, Union
from datetime import datetime, timezone, timedelta
from enum import Enum
from dataclasses import dataclass, asdict
import threading
import heapq


class TaskStatus(Enum):
    """Task status enumeration"""
    PENDING = "pending"
    ASSIGNED = "assigned"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class Task:
    """Task information structure"""
    id: str
    type: str
    data: Dict[str, Any]
    required_capabilities: List[str]
    priority: int
    status: str
    created_at: str
    assigned_to: Optional[str] = None
    assigned_at: Optional[str] = None
    completed_at: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    timeout_seconds: Optional[int] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)


class TaskCoordinator:
    """
    Coordinates task distribution and execution across multiple agents.
    
    Provides:
    - Task queue management with priority support
    - Intelligent agent selection and assignment
    - Task execution tracking and monitoring
    - Retry logic and failure handling
    - Performance optimization
    """
    
    def __init__(self, 
                 agent_registry_or_registry,
                 message_protocol=None,
                 default_timeout: int = 300,
                 max_concurrent_tasks: int = 1000):
        """
        Initialize TaskCoordinator.
        
        Args:
            agent_registry_or_registry: AgentRegistry instance (or just registry for backwards compatibility)
            message_protocol: MessageProtocol instance for communication (optional, will create if not provided)
            default_timeout: Default task timeout in seconds
            max_concurrent_tasks: Maximum concurrent tasks
        """
        # Handle backwards compatibility for tests
        if message_protocol is None:
            from .message_protocol import MessageProtocol
            message_protocol = MessageProtocol()
        
        self.agent_registry = agent_registry_or_registry
        self.message_protocol = message_protocol
        self.default_timeout = default_timeout
        self.max_concurrent_tasks = max_concurrent_tasks
        
        # Task storage
        self.tasks: Dict[str, Task] = {}
        self.task_queue: List[tuple] = []  # Priority queue: (priority, timestamp, task_id)
        self.active_tasks: Dict[str, Task] = {}  # Currently executing tasks
        
        # Agent assignment tracking
        self.agent_tasks: Dict[str, Set[str]] = {}  # agent_id -> set of task_ids
        
        # Performance metrics
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "average_completion_time": 0.0,
            "average_assignment_time": 0.0
        }
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Event handlers
        self.task_completion_handlers: List[Callable] = []
        self.task_failure_handlers: List[Callable] = []
        
        # Background processing
        self._processing_task: Optional[asyncio.Task] = None
        self._timeout_task: Optional[asyncio.Task] = None
        self._running = False
        
        # Logging
        self.logger = logging.getLogger(__name__)
    
    async def start(self) -> None:
        """Start the task coordinator"""
        self._running = True
        self._processing_task = asyncio.create_task(self._process_task_queue())
        self._timeout_task = asyncio.create_task(self._monitor_timeouts())
        self.logger.info("TaskCoordinator started")
    
    async def stop(self) -> None:
        """Stop the task coordinator"""
        self._running = False
        
        # Cancel background tasks
        for task in [self._processing_task, self._timeout_task]:
            if task:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        self.logger.info("TaskCoordinator stopped")
    
    async def submit_task(self,
                         task_type: str,
                         task_data: Dict[str, Any],
                         required_capabilities: List[str],
                         priority: Union[TaskPriority, int] = TaskPriority.NORMAL,
                         timeout_seconds: Optional[int] = None,
                         max_retries: int = 3) -> str:
        """
        Submit a new task for execution.
        
        Args:
            task_type: Type of task to execute
            task_data: Task-specific data
            required_capabilities: Required agent capabilities
            priority: Task priority level
            timeout_seconds: Task timeout (uses default if None)
            max_retries: Maximum retry attempts
            
        Returns:
            Task ID
        """
        task_id = str(uuid.uuid4())
        
        try:
            # Handle priority conversion
            if isinstance(priority, TaskPriority):
                priority_value = priority.value
            else:
                priority_value = priority
            
            with self._lock:
                # Check concurrent task limit
                if len(self.active_tasks) >= self.max_concurrent_tasks:
                    raise ValueError("Maximum concurrent tasks reached")
                
                # Create task
                task = Task(
                    id=task_id,
                    type=task_type,
                    data=task_data,
                    required_capabilities=required_capabilities,
                    priority=priority_value,
                    status=TaskStatus.PENDING.value,
                    created_at=datetime.now(timezone.utc).isoformat(),
                    timeout_seconds=timeout_seconds or self.default_timeout,
                    max_retries=max_retries
                )
                
                # Store task
                self.tasks[task_id] = task
                
                # Add to priority queue (negative priority for max-heap behavior)
                heapq.heappush(
                    self.task_queue,
                    (-priority_value, datetime.now().timestamp(), task_id)
                )
                
                # Update statistics
                self.stats["total_tasks"] += 1
                
                self.logger.info(f"Task {task_id} submitted with priority {priority_value}")
                return task_id
                
        except Exception as e:
            self.logger.error(f"Failed to submit task: {e}")
            raise
    
    async def _process_task_queue(self) -> None:
        """Background task to process the task queue"""
        while self._running:
            try:
                await self._assign_next_task()
                await asyncio.sleep(0.01)  # Small delay to prevent busy waiting
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error processing task queue: {e}")
                await asyncio.sleep(1)  # Wait before retrying
    
    async def _assign_next_task(self) -> bool:
        """
        Assign the next task from the queue to an available agent.
        
        Returns:
            True if task was assigned, False otherwise
        """
        assignment_start = datetime.now()
        
        try:
            with self._lock:
                if not self.task_queue:
                    return False
                
                # Get highest priority task
                _, _, task_id = heapq.heappop(self.task_queue)
                
                if task_id not in self.tasks:
                    return False
                
                task = self.tasks[task_id]
                
                # Find suitable agents
                suitable_agents = self.agent_registry.find_agents_by_capabilities(
                    task.required_capabilities
                )
                
                if not suitable_agents:
                    # No suitable agents available, put task back in queue
                    heapq.heappush(
                        self.task_queue,
                        (-task.priority, datetime.now().timestamp(), task_id)
                    )
                    return False
                
                # Select best agent (least busy)
                selected_agent = self._select_best_agent(suitable_agents)
                
                if not selected_agent:
                    # No available agents, put task back in queue
                    heapq.heappush(
                        self.task_queue,
                        (-task.priority, datetime.now().timestamp(), task_id)
                    )
                    return False
                
                # Assign task to agent
                await self._assign_task_to_agent(task, selected_agent["agent_id"])
                
                # Update assignment time statistics
                assignment_time = (datetime.now() - assignment_start).total_seconds() * 1000
                self._update_average_assignment_time(assignment_time)
                
                return True
                
        except Exception as e:
            self.logger.error(f"Error assigning task: {e}")
            return False
    
    def _select_best_agent(self, suitable_agents: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Select the best agent for task assignment.
        
        Args:
            suitable_agents: List of suitable agent dictionaries
            
        Returns:
            Selected agent dictionary or None
        """
        # Simple selection: choose agent with fewest assigned tasks
        best_agent = None
        min_tasks = float('inf')
        
        for agent in suitable_agents:
            agent_id = agent["agent_id"]
            task_count = len(self.agent_tasks.get(agent_id, set()))
            
            if task_count < min_tasks:
                min_tasks = task_count
                best_agent = agent
        
        return best_agent
    
    async def _assign_task_to_agent(self, task: Task, agent_id: str) -> None:
        """
        Assign a specific task to a specific agent.
        
        Args:
            task: Task to assign
            agent_id: Target agent ID
        """
        try:
            # Update task status
            task.status = TaskStatus.ASSIGNED.value
            task.assigned_to = agent_id
            task.assigned_at = datetime.now(timezone.utc).isoformat()
            
            # Move to active tasks
            self.active_tasks[task.id] = task
            
            # Track agent assignment
            if agent_id not in self.agent_tasks:
                self.agent_tasks[agent_id] = set()
            self.agent_tasks[agent_id].add(task.id)
            
            # Create assignment message
            message = self.message_protocol.create_task_assignment(
                from_agent="coordinator",
                to_agent=agent_id,
                task_data={
                    "id": task.id,
                    "type": task.type,
                    "data": task.data,
                    "timeout": task.timeout_seconds
                },
                priority=task.priority
            )
            
            # In full implementation, would send via WebSocket
            # For now, simulate assignment
            self.logger.info(f"Task {task.id} assigned to agent {agent_id}")
            
            # Simulate task execution start
            task.status = TaskStatus.IN_PROGRESS.value
            
        except Exception as e:
            self.logger.error(f"Failed to assign task {task.id} to agent {agent_id}: {e}")
            # Put task back in queue
            with self._lock:
                heapq.heappush(
                    self.task_queue,
                    (-task.priority, datetime.now().timestamp(), task.id)
                )
    
    async def complete_task(self, 
                          task_id: str, 
                          result: Dict[str, Any],
                          agent_id: Optional[str] = None) -> bool:
        """
        Mark a task as completed.
        
        Args:
            task_id: ID of completed task
            result: Task execution result
            agent_id: ID of agent that completed the task
            
        Returns:
            True if successful, False otherwise
        """
        completion_start = datetime.now()
        
        try:
            with self._lock:
                if task_id not in self.active_tasks:
                    self.logger.warning(f"Task {task_id} not found in active tasks")
                    return False
                
                task = self.active_tasks[task_id]
                
                # Update task
                task.status = TaskStatus.COMPLETED.value
                task.completed_at = datetime.now(timezone.utc).isoformat()
                task.result = result
                
                # Remove from active tasks
                del self.active_tasks[task_id]
                
                # Remove from agent assignment
                if task.assigned_to and task.assigned_to in self.agent_tasks:
                    self.agent_tasks[task.assigned_to].discard(task_id)
                
                # Update statistics
                self.stats["completed_tasks"] += 1
                
                # Calculate completion time
                if task.created_at:
                    created_time = datetime.fromisoformat(task.created_at.replace('Z', '+00:00'))
                    completion_time = (completion_start - created_time).total_seconds() * 1000
                    self._update_average_completion_time(completion_time)
                
                self.logger.info(f"Task {task_id} completed successfully")
                
                # Notify completion handlers
                for handler in self.task_completion_handlers:
                    try:
                        await handler(task)
                    except Exception as e:
                        self.logger.error(f"Error in completion handler: {e}")
                
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to complete task {task_id}: {e}")
            return False
    
    async def fail_task(self, 
                       task_id: str, 
                       error: str,
                       retry: bool = True) -> bool:
        """
        Mark a task as failed and optionally retry.
        
        Args:
            task_id: ID of failed task
            error: Error description
            retry: Whether to retry the task
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self._lock:
                if task_id not in self.active_tasks:
                    return False
                
                task = self.active_tasks[task_id]
                
                # Check if we should retry
                if retry and task.retry_count < task.max_retries:
                    task.retry_count += 1
                    task.status = TaskStatus.PENDING.value
                    task.assigned_to = None
                    task.assigned_at = None
                    
                    # Put back in queue with higher priority
                    heapq.heappush(
                        self.task_queue,
                        (-(task.priority + 1), datetime.now().timestamp(), task_id)
                    )
                    
                    self.logger.info(f"Task {task_id} queued for retry {task.retry_count}/{task.max_retries}")
                    return True
                
                # Mark as permanently failed
                task.status = TaskStatus.FAILED.value
                task.result = {"error": error}
                
                # Remove from active tasks
                del self.active_tasks[task_id]
                
                # Remove from agent assignment
                if task.assigned_to and task.assigned_to in self.agent_tasks:
                    self.agent_tasks[task.assigned_to].discard(task_id)
                
                # Update statistics
                self.stats["failed_tasks"] += 1
                
                self.logger.warning(f"Task {task_id} failed permanently: {error}")
                
                # Notify failure handlers
                for handler in self.task_failure_handlers:
                    try:
                        await handler(task, error)
                    except Exception as e:
                        self.logger.error(f"Error in failure handler: {e}")
                
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to fail task {task_id}: {e}")
            return False
    
    async def _monitor_timeouts(self) -> None:
        """Monitor and handle task timeouts"""
        while self._running:
            try:
                await self._check_task_timeouts()
                await asyncio.sleep(10)  # Check every 10 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error monitoring timeouts: {e}")
    
    async def _check_task_timeouts(self) -> None:
        """Check for timed out tasks"""
        now = datetime.now(timezone.utc)
        timed_out_tasks = []
        
        with self._lock:
            for task_id, task in self.active_tasks.items():
                if task.timeout_seconds and task.assigned_at:
                    assigned_time = datetime.fromisoformat(task.assigned_at.replace('Z', '+00:00'))
                    timeout_threshold = assigned_time + timedelta(seconds=task.timeout_seconds)
                    
                    if now > timeout_threshold:
                        timed_out_tasks.append(task_id)
        
        # Handle timeouts
        for task_id in timed_out_tasks:
            await self.fail_task(task_id, "Task timeout", retry=True)
    
    def get_task_status(self, task_id: str) -> Optional[str]:
        """Get status of a specific task"""
        with self._lock:
            if task_id in self.tasks:
                return self.tasks[task_id].status
            return None
    
    async def get_task_status_async(self, task_id: str) -> Optional[str]:
        """Async version of get_task_status for compatibility"""
        return self.get_task_status(task_id)
    
    def get_task_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get complete information about a task"""
        with self._lock:
            if task_id in self.tasks:
                return self.tasks[task_id].to_dict()
            return None
    
    def list_active_tasks(self) -> List[Dict[str, Any]]:
        """Get list of all active tasks"""
        with self._lock:
            return [task.to_dict() for task in self.active_tasks.values()]
    
    def list_pending_tasks(self) -> List[str]:
        """Get list of pending task IDs"""
        with self._lock:
            return [task_id for _, _, task_id in self.task_queue]
    
    async def get_pending_tasks(self) -> List[str]:
        """Async version of list_pending_tasks for compatibility"""
        return self.list_pending_tasks()
    
    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a pending or active task"""
        try:
            with self._lock:
                if task_id not in self.tasks:
                    return False
                
                task = self.tasks[task_id]
                
                # Update task status
                task.status = TaskStatus.CANCELLED.value
                
                # Remove from active tasks if present
                if task_id in self.active_tasks:
                    del self.active_tasks[task_id]
                
                # Remove from priority queue (reconstruct queue without this task)
                self.task_queue = [item for item in self.task_queue if item[2] != task_id]
                
                self.logger.info(f"Task {task_id} cancelled successfully")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to cancel task {task_id}: {e}")
            return False
    
    def add_completion_handler(self, handler: Callable) -> None:
        """Add task completion handler"""
        self.task_completion_handlers.append(handler)
    
    def add_failure_handler(self, handler: Callable) -> None:
        """Add task failure handler"""
        self.task_failure_handlers.append(handler)
    
    def _update_average_completion_time(self, new_time: float) -> None:
        """Update average completion time statistics"""
        current_avg = self.stats["average_completion_time"]
        completed_tasks = self.stats["completed_tasks"]
        
        if completed_tasks == 1:
            self.stats["average_completion_time"] = new_time
        else:
            self.stats["average_completion_time"] = (
                (current_avg * (completed_tasks - 1) + new_time) / completed_tasks
            )
    
    def _update_average_assignment_time(self, new_time: float) -> None:
        """Update average assignment time statistics"""
        current_avg = self.stats["average_assignment_time"]
        total_tasks = self.stats["total_tasks"]
        
        if total_tasks == 1:
            self.stats["average_assignment_time"] = new_time
        else:
            self.stats["average_assignment_time"] = (
                (current_avg * (total_tasks - 1) + new_time) / total_tasks
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get task coordinator statistics"""
        with self._lock:
            return {
                "stats": self.stats.copy(),
                "active_tasks": len(self.active_tasks),
                "pending_tasks": len(self.task_queue),
                "total_tasks": len(self.tasks),
                "agent_utilization": {
                    agent_id: len(task_ids)
                    for agent_id, task_ids in self.agent_tasks.items()
                }
            }
