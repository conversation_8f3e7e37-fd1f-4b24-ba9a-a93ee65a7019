"""
Simple ChatSession that avoids problematic multi-agent imports.
This is used as a fallback when the original ChatSession has import issues.
"""

import asyncio
import json
import logging
from typing import Optional, Dict, Any
from fastapi import WebSocket, WebSocketDisconnect

logger = logging.getLogger(__name__)

class SimpleChatSession:
    """Simple chat session without multi-agent dependencies."""
    
    def __init__(self, websocket: WebSocket, session_id: str, file_store=None, config=None):
        self.websocket = websocket
        self.session_id = session_id
        self.file_store = file_store
        self.config = config
        self.is_active = True
        
    async def handle_message(self, data: Dict[str, Any]):
        """Handle incoming WebSocket messages."""
        try:
            message_type = data.get("type", "unknown")
            
            if message_type == "message":
                content = data.get("content", "")
                response = {
                    "type": "response",
                    "content": f"Echo: {content}",
                    "session_id": self.session_id
                }
                await self.websocket.send_text(json.dumps(response))
                
            elif message_type == "ping":
                response = {
                    "type": "pong",
                    "timestamp": data.get("timestamp")
                }
                await self.websocket.send_text(json.dumps(response))
                
            else:
                response = {
                    "type": "error",
                    "content": f"Unknown message type: {message_type}"
                }
                await self.websocket.send_text(json.dumps(response))
                
        except Exception as e:
            logger.error(f"Error handling message: {e}")
            error_response = {
                "type": "error",
                "content": f"Internal error: {str(e)}"
            }
            await self.websocket.send_text(json.dumps(error_response))
    
    async def start_chat_loop(self):
        """Start the chat session."""
        logger.info(f"Starting simple chat session {self.session_id}")
        
        try:
            while self.is_active:
                try:
                    # Wait for messages
                    message = await self.websocket.receive_text()
                    data = json.loads(message)
                    await self.handle_message(data)
                    
                except WebSocketDisconnect:
                    logger.info(f"WebSocket disconnected for session {self.session_id}")
                    break
                except json.JSONDecodeError:
                    error_response = {
                        "type": "error",
                        "content": "Invalid JSON message"
                    }
                    await self.websocket.send_text(json.dumps(error_response))
                except Exception as e:
                    logger.error(f"Error in chat session: {e}")
                    break
                    
        finally:
            self.is_active = False
            logger.info(f"Chat session {self.session_id} ended")
    
    def cleanup(self):
        """Cleanup resources."""
        self.is_active = False
        logger.info(f"Cleaned up chat session {self.session_id}")
