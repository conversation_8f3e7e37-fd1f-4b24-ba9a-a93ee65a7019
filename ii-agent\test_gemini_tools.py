#!/usr/bin/env python3
"""Test updated Gemini tool calling locally."""

import os
import sys
from pathlib import Path

# Add the src directory to the Python path
project_root = Path(__file__).parent
src_dir = project_root / "src"
sys.path.insert(0, str(src_dir))

from ii_agent.llm.gemini import GeminiDirectClient
from ii_agent.core.config.llm_config import LLMConfig
from ii_agent.llm.base import ToolParam

def test_tool_calling():
    """Test Gemini tool calling with a simple mock tool."""
    
    # Check API key
    api_key = os.getenv("GOOGLE_AI_STUDIO_API_KEY")
    if not api_key:
        print("ERROR: GOOGLE_AI_STUDIO_API_KEY not set")
        return
    
    print(f"Using API key: {api_key[:10]}...")
    
    # Create config and client
    config = LLMConfig(model_name="gemini-2.0-flash-exp", temperature=0.0)
    client = GeminiDirectClient(config)
    
    print(f"Client model info: {client.get_model_info()}")
    
    # Define a simple test tool
    test_tool = ToolParam(
        name="get_current_time",
        description="Get the current time in UTC",
        input_schema={
            "type": "object",
            "properties": {
                "format": {
                    "type": "string",
                    "description": "Time format (e.g., 'iso', 'human')",
                    "enum": ["iso", "human"]
                }
            },
            "required": ["format"]
        }
    )
    
    # Simple messages (just user prompt requesting time)
    messages = [
        [{"text": "What is the current time in human-readable format? Use the get_current_time tool."}]
    ]
    
    print("\nTesting tool call with prompt about current time...")
    
    try:
        results, metadata = client.generate(
            messages=messages,
            max_tokens=1000,
            tools=[test_tool]
        )
        
        print(f"Results count: {len(results)}")
        print(f"Metadata: {metadata}")
        
        for i, result in enumerate(results):
            print(f"Result {i}: {type(result).__name__}")
            if hasattr(result, 'tool_name'):
                print(f"  Tool: {result.tool_name}")
                print(f"  Input: {result.tool_input}")
            elif hasattr(result, 'text'):
                print(f"  Text: {result.text[:200]}...")
        
        # Check if we got a ToolCall
        tool_calls = [r for r in results if hasattr(r, 'tool_name')]
        if tool_calls:
            print(f"\n✅ SUCCESS: Got {len(tool_calls)} tool call(s)")
            return True
        else:
            print(f"\n❌ FAILED: No tool calls, only text results")
            return False
            
    except Exception as e:
        print(f"\n❌ ERROR during generation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing updated Gemini tool calling...")
    success = test_tool_calling()
    sys.exit(0 if success else 1)
