"""
Test Suite for Phase 1: Real Neural Networks
===========================================

Following TDD principles with comprehensive testing of neural network implementation.
Tests real functionality (NO MOCKS) with actual neural computation.
"""

import pytest
import numpy as np
import sys
import os

# Add the neural module to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from phase1_real_neural_networks import (
    AgentType, ActivationFunction, NetworkConfig, SimpleNeuralNetwork,
    TextToVectorEncoder, RealNeuralAgent, NeuralSwarmCoordinator
)

class TestNetworkConfig:
    """Test neural network configuration validation"""
    
    def test_valid_config_creation(self):
        """Test creating a valid network configuration"""
        config = NetworkConfig(
            input_size=10,
            hidden_layers=[5, 3],
            output_size=2,
            activation_functions=[ActivationFunction.RELU, ActivationFunction.TANH, ActivationFunction.SIGMOID]
        )
        
        assert config.input_size == 10
        assert config.hidden_layers == [5, 3]
        assert config.output_size == 2
        assert len(config.activation_functions) == 3
    
    def test_invalid_activation_function_count(self):
        """Test that invalid activation function count raises error"""
        with pytest.raises(ValueError, match="Number of activation functions"):
            NetworkConfig(
                input_size=10,
                hidden_layers=[5, 3],
                output_size=2,
                activation_functions=[ActivationFunction.RELU]  # Should be 3, not 1
            )
    
    def test_negative_layer_sizes(self):
        """Test that negative layer sizes raise error"""
        with pytest.raises(ValueError, match="must be positive"):
            NetworkConfig(
                input_size=-1,
                hidden_layers=[5, 3],
                output_size=2,
                activation_functions=[ActivationFunction.RELU, ActivationFunction.TANH, ActivationFunction.SIGMOID]
            )

class TestSimpleNeuralNetwork:
    """Test the actual neural network implementation"""
    
    def test_network_initialization(self):
        """Test that neural network initializes correctly"""
        config = NetworkConfig(
            input_size=4,
            hidden_layers=[3, 2],
            output_size=1,
            activation_functions=[ActivationFunction.RELU, ActivationFunction.TANH, ActivationFunction.SIGMOID]
        )
        
        network = SimpleNeuralNetwork(config)
        
        # Test that weights and biases are initialized
        assert len(network.weights) == 3  # input->hidden1, hidden1->hidden2, hidden2->output
        assert len(network.biases) == 3
        
        # Test shapes
        assert network.weights[0].shape == (4, 3)  # input(4) -> hidden1(3)
        assert network.weights[1].shape == (3, 2)  # hidden1(3) -> hidden2(2)
        assert network.weights[2].shape == (2, 1)  # hidden2(2) -> output(1)
    
    def test_forward_propagation(self):
        """Test forward propagation produces valid outputs"""
        config = NetworkConfig(
            input_size=3,
            hidden_layers=[4, 2],
            output_size=1,
            activation_functions=[ActivationFunction.RELU, ActivationFunction.TANH, ActivationFunction.SIGMOID]
        )
        
        network = SimpleNeuralNetwork(config)
        
        # Test with sample input
        inputs = np.array([[1.0, 0.5, -0.5]])
        output = network.forward(inputs)
        
        # Check output shape and range
        assert output.shape == (1, 1)
        assert 0 <= output[0, 0] <= 1  # Sigmoid output should be in [0, 1]
    
    def test_predict_single_input(self):
        """Test single input prediction"""
        config = NetworkConfig(
            input_size=2,
            hidden_layers=[3],
            output_size=1,
            activation_functions=[ActivationFunction.RELU, ActivationFunction.SIGMOID]
        )
        
        network = SimpleNeuralNetwork(config)
        
        # Test prediction
        result = network.predict([1.0, -1.0])
        
        # Should return a single value
        assert isinstance(result, np.ndarray)
        assert len(result) == 1
        assert 0 <= result[0] <= 1
    
    def test_invalid_input_size(self):
        """Test that invalid input size raises error"""
        config = NetworkConfig(
            input_size=3,
            hidden_layers=[2],
            output_size=1,
            activation_functions=[ActivationFunction.RELU, ActivationFunction.SIGMOID]
        )
        
        network = SimpleNeuralNetwork(config)
        
        # Should raise error for wrong input size
        with pytest.raises(ValueError, match="Input length"):
            network.predict([1.0, 2.0])  # Only 2 inputs, expects 3
    
    def test_activation_functions(self):
        """Test all activation functions work correctly"""
        config = NetworkConfig(
            input_size=2,
            hidden_layers=[],
            output_size=1,
            activation_functions=[ActivationFunction.LINEAR]
        )
        
        network = SimpleNeuralNetwork(config)
        
        # Test each activation function
        test_values = np.array([[-5, -1, 0, 1, 5]])
        
        # Linear
        result = network._apply_activation(test_values, ActivationFunction.LINEAR)
        np.testing.assert_array_equal(result, test_values)
        
        # Sigmoid
        result = network._apply_activation(test_values, ActivationFunction.SIGMOID)
        assert np.all(result >= 0) and np.all(result <= 1)
        
        # ReLU
        result = network._apply_activation(test_values, ActivationFunction.RELU)
        expected = np.array([[0, 0, 0, 1, 5]])
        np.testing.assert_array_equal(result, expected)

class TestTextToVectorEncoder:
    """Test text encoding for neural network input"""
    
    def test_encoder_initialization(self):
        """Test text encoder initializes correctly"""
        encoder = TextToVectorEncoder(vector_size=128)
        
        assert encoder.vector_size == 128
        assert len(encoder.vocabulary) > 0
        assert AgentType.CODER in encoder.feature_keywords
    
    def test_text_encoding(self):
        """Test text is encoded to correct vector size"""
        encoder = TextToVectorEncoder(vector_size=100)
        
        text = "I need help with Python code debugging"
        vector = encoder.encode_text(text)
        
        # Check vector properties
        assert len(vector) == 100
        assert all(isinstance(v, float) for v in vector)
        assert any(v > 0 for v in vector)  # Should have some non-zero features
    
    def test_different_texts_produce_different_vectors(self):
        """Test that different texts produce different feature vectors"""
        encoder = TextToVectorEncoder(vector_size=50)
        
        text1 = "Help me debug this Python code"
        text2 = "Design a user interface for mobile app"
        
        vector1 = encoder.encode_text(text1)
        vector2 = encoder.encode_text(text2)
        
        # Vectors should be different
        assert vector1 != vector2
    
    def test_empty_text_handling(self):
        """Test encoding empty text doesn't crash"""
        encoder = TextToVectorEncoder(vector_size=10)
        
        vector = encoder.encode_text("")
        
        assert len(vector) == 10
        assert all(isinstance(v, float) for v in vector)

class TestRealNeuralAgent:
    """Test the real neural agent implementation"""
    
    def test_agent_initialization(self):
        """Test agent initializes correctly"""
        agent = RealNeuralAgent(AgentType.CODER)
        
        assert agent.agent_type == AgentType.CODER
        assert agent.agent_id.startswith("coder_")
        assert agent.network is not None
        assert agent.text_encoder is not None
        assert agent.predictions_made == 0
    
    def test_input_analysis(self):
        """Test agent can analyze input and produce results"""
        agent = RealNeuralAgent(AgentType.CODER)
        
        result = agent.analyze_input("Help me fix this Python bug in my function")
        
        # Check result structure
        assert "agent_type" in result
        assert "confidence" in result
        assert "suitability" in result
        assert "prediction_vector" in result
        
        # Check value ranges
        assert 0 <= result["confidence"] <= 1
        assert 0 <= result["suitability"] <= 1
        assert result["agent_type"] == "coder"
    
    def test_performance_tracking(self):
        """Test agent tracks performance metrics"""
        agent = RealNeuralAgent(AgentType.RESEARCHER)
        
        # Make some predictions
        agent.analyze_input("Research machine learning algorithms")
        agent.analyze_input("Study neural networks")
        
        metrics = agent.get_performance_metrics()
        
        assert metrics["predictions_made"] == 2
        assert "average_confidence" in metrics
        assert metrics["agent_type"] == "researcher"
    
    def test_specialized_networks_for_different_agents(self):
        """Test that different agent types get different network architectures"""
        coder_agent = RealNeuralAgent(AgentType.CODER)
        researcher_agent = RealNeuralAgent(AgentType.RESEARCHER)
        data_scientist_agent = RealNeuralAgent(AgentType.DATA_SCIENTIST)
        
        # Networks should have different architectures
        coder_info = coder_agent.network.get_network_info()
        researcher_info = researcher_agent.network.get_network_info()
        data_scientist_info = data_scientist_agent.network.get_network_info()
        
        # Data scientist should have the most complex network
        assert data_scientist_info["total_parameters"] > coder_info["total_parameters"]
        assert len(data_scientist_info["hidden_layers"]) >= len(coder_info["hidden_layers"])

class TestNeuralSwarmCoordinator:
    """Test the neural swarm coordination system"""
    
    def test_coordinator_initialization(self):
        """Test swarm coordinator initializes all agents"""
        coordinator = NeuralSwarmCoordinator()
        
        assert len(coordinator.agents) == len(AgentType)
        assert all(isinstance(agent, RealNeuralAgent) for agent in coordinator.agents.values())
    
    def test_optimal_agent_selection(self):
        """Test coordinator selects appropriate agent for different tasks"""
        coordinator = NeuralSwarmCoordinator()
        
        # Test code-related input
        code_input = "Fix this Python function that has a bug in the loop"
        agent, analysis = coordinator.select_optimal_agent(code_input)
        
        assert isinstance(agent, RealNeuralAgent)
        assert "confidence" in analysis
        assert "suitability" in analysis
        
        # Test research-related input
        research_input = "Research the latest developments in quantum computing"
        agent2, analysis2 = coordinator.select_optimal_agent(research_input)
        
        # Should potentially select different agents for different tasks
        assert isinstance(agent2, RealNeuralAgent)
    
    def test_empty_input_handling(self):
        """Test coordinator handles empty input gracefully"""
        coordinator = NeuralSwarmCoordinator()
        
        with pytest.raises(ValueError, match="User input cannot be empty"):
            coordinator.select_optimal_agent("")
    
    def test_swarm_metrics(self):
        """Test swarm provides meaningful metrics"""
        coordinator = NeuralSwarmCoordinator()
        
        # Make some selections
        coordinator.select_optimal_agent("Test input 1")
        coordinator.select_optimal_agent("Test input 2")
        
        metrics = coordinator.get_swarm_metrics()
        
        assert metrics["total_agents"] == len(AgentType)
        assert metrics["total_selections"] == 2
        assert "agent_metrics" in metrics

class TestIntegrationScenarios:
    """Test real-world integration scenarios"""
    
    def test_coding_task_workflow(self):
        """Test complete workflow for coding task"""
        coordinator = NeuralSwarmCoordinator()
        
        coding_task = """
        I have a Python function that's supposed to calculate fibonacci numbers
        but it's running very slowly. Can you help me optimize it?
        Here's the code:
        def fib(n):
            if n <= 1:
                return n
            return fib(n-1) + fib(n-2)
        """
        
        agent, analysis = coordinator.select_optimal_agent(coding_task)
        
        # Should have reasonable confidence for this clear coding task
        assert analysis["confidence"] > 0.1
        assert analysis["suitability"] > 0.1
        
        # Agent should be able to provide meaningful analysis
        assert len(analysis["prediction_vector"]) > 0
    
    def test_research_task_workflow(self):
        """Test complete workflow for research task"""
        coordinator = NeuralSwarmCoordinator()
        
        research_task = """
        I need to research the current state of artificial intelligence
        in healthcare applications. Can you help me find recent academic
        papers and studies on this topic?
        """
        
        agent, analysis = coordinator.select_optimal_agent(research_task)
        
        assert analysis["confidence"] > 0.1
        assert analysis["suitability"] > 0.1
    
    def test_performance_consistency(self):
        """Test that neural analysis is consistent and reasonable"""
        coordinator = NeuralSwarmCoordinator()
        
        # Test same input multiple times
        test_input = "Help me design a database schema"
        
        results = []
        for _ in range(5):
            agent, analysis = coordinator.select_optimal_agent(test_input)
            results.append(analysis["confidence"])
        
        # Results should be consistent (not varying wildly)
        avg_confidence = sum(results) / len(results)
        assert all(abs(r - avg_confidence) < 0.5 for r in results)

def run_comprehensive_tests():
    """Run all tests and report results"""
    import subprocess
    import sys
    
    try:
        # Run pytest with detailed output
        result = subprocess.run([
            sys.executable, "-m", "pytest", __file__, "-v", "--tb=short"
        ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        print("=== PHASE 1 TEST RESULTS ===")
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        if result.returncode == 0:
            print("✅ ALL TESTS PASSED - Phase 1 neural networks are working correctly!")
        else:
            print("❌ TESTS FAILED - Need to fix issues before proceeding")
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False

if __name__ == "__main__":
    # Run tests when executed directly
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
