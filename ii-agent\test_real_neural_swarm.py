#!/usr/bin/env python3
"""
Test Real Neural Multi-Agent System
===================================

Test script to verify the real neural networks and coordination patterns work.
"""

import asyncio
import logging
import sys
import json
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def test_real_neural_agents():
    """Test individual neural agents"""
    logger.info("🧠 Testing Real Neural Agents...")
    
    try:
        from ii_agent_integration.neural.real_neural_networks import (
            RealNeuralAgent, AgentSpecialization, NeuralConfig
        )
        
        # Create a test agent
        config = NeuralConfig(input_size=512, hidden_sizes=[256, 128], output_size=32)
        agent = RealNeuralAgent("test_agent", AgentSpecialization.RESEARCHER, config)
        
        # Test analysis
        test_task = "Analyze the performance of machine learning models in production environments"
        result = await agent.analyze_task(test_task)
        
        logger.info(f"✅ Neural Agent Analysis Result:")
        logger.info(f"   Agent ID: {result['agent_id']}")
        logger.info(f"   Specialization: {result['specialization']}")
        logger.info(f"   Confidence: {result['confidence']:.3f}")
        logger.info(f"   Execution Time: {result['execution_time_ms']:.2f}ms")
        logger.info(f"   Analysis Keys: {list(result['analysis'].keys())}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Neural agent test failed: {e}")
        return False


async def test_real_coordination_patterns():
    """Test real coordination patterns"""
    logger.info("🤝 Testing Real Coordination Patterns...")
    
    try:
        from ii_agent_integration.swarms.real_coordination_patterns import RealCoordinationPatterns
        
        coordinator = RealCoordinationPatterns()
        
        # Test mixture of agents
        test_task = "Design a scalable microservices architecture"
        result = await coordinator.execute_mixture_of_agents(test_task)
        
        logger.info(f"✅ Mixture of Agents Result:")
        logger.info(f"   Success: {result['success']}")
        logger.info(f"   Strategy: {result['strategy']}")
        logger.info(f"   Consensus Score: {result['consensus_score']:.3f}")
        logger.info(f"   Execution Time: {result['execution_time_ms']:.2f}ms")
        logger.info(f"   Agent Responses: {len(result['agent_responses'])}")
        
        # Test consensus swarm
        consensus_result = await coordinator.execute_consensus_swarm(test_task)
        
        logger.info(f"✅ Consensus Swarm Result:")
        logger.info(f"   Success: {consensus_result['success']}")
        logger.info(f"   Consensus Score: {consensus_result['consensus_score']:.3f}")
        logger.info(f"   Byzantine Tolerance: {consensus_result['metadata'].get('fault_tolerance', 'N/A')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Coordination patterns test failed: {e}")
        return False


async def test_real_multi_agent_system():
    """Test the complete real multi-agent system"""
    logger.info("🚀 Testing Real Multi-Agent System...")
    
    try:
        from ii_agent_integration.swarms.real_multi_agent_system import (
            RealMultiAgentSystem, SwarmConfig
        )
        
        # Create swarm with real neural agents
        config = SwarmConfig(max_agents=5, enable_learning=True, enable_gpu=False)  # CPU for testing
        swarm = RealMultiAgentSystem(config)
        
        # Test coordination
        test_task = "Develop a comprehensive testing strategy for a distributed system"
        
        # Test different coordination strategies
        strategies = ["mixture_of_agents", "consensus_swarm", "hierarchical_swarm"]
        
        for strategy in strategies:
            logger.info(f"   Testing {strategy}...")
            result = await swarm.coordinate_task(test_task, strategy)
            
            logger.info(f"   ✅ {strategy} Result:")
            logger.info(f"      Success: {result['success']}")
            logger.info(f"      Execution Time: {result['execution_time_ms']:.2f}ms")
            logger.info(f"      Participating Agents: {result['participating_agents']}")
            
            if result['success']:
                coordination_result = result['result']
                logger.info(f"      Consensus Score: {coordination_result.get('consensus_score', 0):.3f}")
        
        # Get swarm status
        status = swarm.get_swarm_status()
        logger.info(f"✅ Swarm Status:")
        logger.info(f"   State: {status['swarm_state']}")
        logger.info(f"   Total Agents: {status['total_agents']}")
        logger.info(f"   Total Coordinations: {status['performance_metrics']['total_coordinations']}")
        logger.info(f"   Success Rate: {status['performance_metrics']['successful_coordinations']}/{status['performance_metrics']['total_coordinations']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Multi-agent system test failed: {e}")
        return False


async def test_swarms_integration():
    """Test the swarms integration with real neural coordination"""
    logger.info("🔗 Testing Swarms Integration with Real Neural Coordination...")
    
    try:
        from ii_agent_integration.swarms.swarms_integration import SwarmsFrameworkIntegration
        
        # Create integration (this should initialize real neural swarm)
        integration = SwarmsFrameworkIntegration(coordination_hub=None)
        
        # Test real neural coordination
        test_task = "Optimize database query performance for high-traffic applications"
        
        if hasattr(integration, '_real_swarm'):
            result = await integration.execute_real_neural_coordination(test_task, "mixture_of_agents")
            
            logger.info(f"✅ Real Neural Coordination Result:")
            logger.info(f"   Success: {result.success}")
            logger.info(f"   Strategy: {result.strategy}")
            logger.info(f"   Neural Coordination: {result.metadata.get('neural_coordination', False)}")
            logger.info(f"   Execution Time: {result.metadata.get('execution_time_ms', 0):.2f}ms")
            logger.info(f"   Consensus Score: {result.metadata.get('consensus_score', 0):.3f}")
            
            # Get swarm status
            swarm_status = await integration.get_real_swarm_status()
            logger.info(f"✅ Integration Swarm Status:")
            logger.info(f"   Swarm State: {swarm_status.get('swarm_state', 'unknown')}")
            logger.info(f"   Total Agents: {swarm_status.get('total_agents', 0)}")
            
        else:
            logger.warning("⚠️ Real neural swarm not available in integration")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Swarms integration test failed: {e}")
        return False


async def main():
    """Run all tests"""
    logger.info("🎯 Starting Real Neural Multi-Agent System Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Neural Agents", test_real_neural_agents),
        ("Coordination Patterns", test_real_coordination_patterns),
        ("Multi-Agent System", test_real_multi_agent_system),
        ("Swarms Integration", test_swarms_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running {test_name} Test...")
        try:
            success = await test_func()
            results[test_name] = success
            if success:
                logger.info(f"✅ {test_name} Test PASSED")
            else:
                logger.error(f"❌ {test_name} Test FAILED")
        except Exception as e:
            logger.error(f"💥 {test_name} Test CRASHED: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"   {test_name}: {status}")
    
    logger.info(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED! Real neural multi-agent system is working!")
        return True
    else:
        logger.error(f"💔 {total - passed} tests failed. System needs fixes.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
