"""BaseAgent - Core Agent Functionality

Production-grade base class for multi-agent systems.
Integrates with Phase 1 WebSocket infrastructure for communication.
"""

import asyncio
import logging
import time
import json
import uuid
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timezone
import websockets
from enum import Enum

# Simplified for ii-agent integration - removed infrastructure dependencies


class AgentStatus(Enum):
    """Agent status enumeration"""
    INITIALIZED = "initialized"
    REGISTERED = "registered" 
    CONNECTED = "connected"
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    SHUTDOWN = "shutdown"


class EventType(Enum):
    """Simple event types for agent communication"""
    MESSAGE = "message"
    STATUS = "status"
    ERROR = "error"


class WebSocketEvent:
    """Simple WebSocket event class"""
    def __init__(self, event_type: EventType, data: dict = None):
        self.type = event_type
        self.data = data or {}


class AgentStatus(Enum):
    """Agent status enumeration"""
    INITIALIZED = "initialized"
    REGISTERED = "registered"
    CONNECTED = "connected"
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    SHUTDOWN = "shutdown"


class BaseAgent:
    """
    Base class for multi-agent system participants.
    
    Provides core functionality:
    - Agent lifecycle management
    - WebSocket communication integration
    - Task execution framework
    - Message handling and routing
    - Capability-based task matching
    """
    
    def __init__(self, 
                 agent_id: str,
                 capabilities: Optional[List[str]] = None,
                 websocket_host: str = "localhost",
                 websocket_port: int = 8765,
                 max_retry_attempts: int = 3):
        """
        Initialize BaseAgent.
        
        Args:
            agent_id: Unique identifier for this agent
            capabilities: List of capabilities this agent provides
            websocket_host: WebSocket server host
            websocket_port: WebSocket server port
            max_retry_attempts: Maximum connection retry attempts
        """
        self.agent_id = agent_id
        self.capabilities = capabilities or []
        self.websocket_host = websocket_host
        self.websocket_port = websocket_port
        self.max_retry_attempts = max_retry_attempts
        
        # Status tracking
        self.status = AgentStatus.INITIALIZED.value
        self.status_history: List[Dict[str, Any]] = []
        self._add_status_history(AgentStatus.INITIALIZED.value)
        
        # Communication
        self.websocket_connection = None
        self.connection_id: Optional[str] = None
        self.message_handler: Optional[Callable] = None
        self.received_messages: List[Dict[str, Any]] = []
        
        # Task execution
        self.current_task: Optional[Dict[str, Any]] = None
        self.task_history: List[Dict[str, Any]] = []
        
        # Setup logging
        self.logger = logging.getLogger(f"{__name__}.{agent_id}")
        
        # Lifecycle flags
        self._running = False
        self._shutdown_requested = False
    
    def _add_status_history(self, status: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Add status change to history"""
        self.status_history.append({
            'status': status,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'metadata': metadata or {}
        })
    
    async def register(self) -> bool:
        """Register agent with the system"""
        try:
            # For now, just update status
            # In full implementation, this would register with AgentRegistry
            self.set_status(AgentStatus.REGISTERED.value)
            self.logger.info(f"Agent {self.agent_id} registered successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to register agent {self.agent_id}: {e}")
            self.set_status(AgentStatus.ERROR.value, {"error": str(e)})
            return False
    
    async def connect(self) -> bool:
        """Connect to WebSocket infrastructure"""
        try:
            uri = f"ws://{self.websocket_host}:{self.websocket_port}"
            
            # Simulate WebSocket connection for testing
            # In full implementation, this would connect to real WebSocket server
            self.websocket_connection = MockWebSocketConnection(uri)
            self.connection_id = str(uuid.uuid4())
            
            self.set_status(AgentStatus.CONNECTED.value)
            self.logger.info(f"Agent {self.agent_id} connected to {uri}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect agent {self.agent_id}: {e}")
            self.set_status(AgentStatus.ERROR.value, {"error": str(e)})
            return False
    
    def set_message_handler(self, handler: Callable) -> None:
        """Set custom message handler"""
        self.message_handler = handler
        self.logger.debug(f"Message handler set for agent {self.agent_id}")
    
    async def handle_message(self, message: Dict[str, Any]) -> None:
        """Handle incoming message"""
        try:
            self.received_messages.append(message)
            
            if self.message_handler:
                await self.message_handler(message)
            else:
                # Default message handling
                await self._default_message_handler(message)
                
        except Exception as e:
            self.logger.error(f"Error handling message in agent {self.agent_id}: {e}")
    
    async def _default_message_handler(self, message: Dict[str, Any]) -> None:
        """Default message handling logic"""
        message_type = message.get("type")
        
        if message_type == "task_assignment":
            await self._handle_task_assignment(message)
        elif message_type == "coordination_request":
            await self._handle_coordination_request(message)
        else:
            self.logger.debug(f"Unhandled message type: {message_type}")
    
    async def _handle_task_assignment(self, message: Dict[str, Any]) -> None:
        """Handle task assignment message"""
        task = message.get("data", {})
        task_id = task.get("id", message.get("task_id"))
        
        if task_id:
            self.logger.info(f"Received task assignment: {task_id}")
            # Execute task in background
            asyncio.create_task(self.execute_task(task))
    
    async def _handle_coordination_request(self, message: Dict[str, Any]) -> None:
        """Handle coordination request message"""
        self.logger.info(f"Received coordination request from {message.get('from_agent')}")
        # Handle coordination logic here
    
    async def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute assigned task"""
        task_id = task.get("id", str(uuid.uuid4()))
        
        try:
            self.set_status(AgentStatus.BUSY.value, {"task_id": task_id})
            self.current_task = task
            
            # Simulate task execution
            await asyncio.sleep(0.01)  # Minimal delay for realistic behavior
            
            # Create result
            result = {
                "status": "completed",
                "task_id": task_id,
                "agent_id": self.agent_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "result_data": {"processed": True}
            }
            
            # Add to task history
            self.task_history.append({
                "task": task,
                "result": result,
                "completed_at": datetime.now(timezone.utc).isoformat()
            })
            
            self.current_task = None
            self.set_status(AgentStatus.IDLE.value)
            
            self.logger.info(f"Task {task_id} completed successfully")
            return result
            
        except Exception as e:
            error_result = {
                "status": "failed",
                "task_id": task_id,
                "agent_id": self.agent_id,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            self.logger.error(f"Task {task_id} failed: {e}")
            self.set_status(AgentStatus.ERROR.value, {"task_id": task_id, "error": str(e)})
            return error_result
    
    def can_handle_task(self, required_capabilities: List[str]) -> bool:
        """Check if agent can handle task based on capabilities"""
        agent_capabilities = set(self.capabilities)
        required_capabilities_set = set(required_capabilities)
        
        # Agent can handle task if it has all required capabilities
        return required_capabilities_set.issubset(agent_capabilities)
    
    def set_status(self, status: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Update agent status"""
        old_status = self.status
        self.status = status
        self._add_status_history(status, metadata)
        
        self.logger.debug(f"Agent {self.agent_id} status changed: {old_status} -> {status}")
    
    def get_status_history(self) -> List[Dict[str, Any]]:
        """Get agent status history"""
        return self.status_history.copy()
    
    def get_received_messages(self) -> List[Dict[str, Any]]:
        """Get list of received messages"""
        return self.received_messages.copy()
    
    async def send_message(self, message: Dict[str, Any]) -> bool:
        """Send message through WebSocket connection"""
        try:
            if not self.websocket_connection:
                self.logger.error("No WebSocket connection available")
                return False
            
            # Add agent metadata to message
            enhanced_message = {
                **message,
                "from_agent": self.agent_id,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Simulate message sending
            await self.websocket_connection.send(json.dumps(enhanced_message))
            
            self.logger.debug(f"Message sent from agent {self.agent_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send message from agent {self.agent_id}: {e}")
            return False
    
    async def shutdown(self) -> bool:
        """Gracefully shutdown agent"""
        try:
            self._shutdown_requested = True
            
            # Complete current task if any
            if self.current_task:
                self.logger.info(f"Completing current task before shutdown: {self.current_task.get('id')}")
                # Give task time to complete
                await asyncio.sleep(0.1)
            
            # Close WebSocket connection
            if self.websocket_connection:
                await self.websocket_connection.close()
                self.websocket_connection = None
                self.connection_id = None
            
            self.set_status(AgentStatus.SHUTDOWN.value)
            self.logger.info(f"Agent {self.agent_id} shutdown successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error during agent {self.agent_id} shutdown: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get agent statistics"""
        return {
            "agent_id": self.agent_id,
            "status": self.status,
            "capabilities": self.capabilities,
            "connection_id": self.connection_id,
            "tasks_completed": len(self.task_history),
            "messages_received": len(self.received_messages),
            "current_task": self.current_task.get("id") if self.current_task else None,
            "uptime": len(self.status_history)
        }


class MockWebSocketConnection:
    """Mock WebSocket connection for testing"""
    
    def __init__(self, uri: str):
        self.uri = uri
        self.connected = True
    
    async def send(self, message: str) -> None:
        """Mock send method"""
        # In testing, just log the message
        pass
    
    async def close(self) -> None:
        """Mock close method"""
        self.connected = False
