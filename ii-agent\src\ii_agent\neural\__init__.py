"""
Neural Core Module
=================

This module provides the core neural processing capabilities for the hybrid agent.
It includes the RUV-FANN neural network integration and session management.
"""

from .ruv_fann_neural_core import RUVFANNNeuralSwarm, NeuralAgent, AgentType
from .session_management_core import Session<PERSON>torage, SessionMessage, SessionInfo
from .simple_neural import SimpleNeuralAgent, SimpleNeuralCore, RuvFANNNeuralAgent, unlock_ruv_fann_potential
from .neural_agent import NeuralEnhancedAgent

__all__ = [
    "RUVFANNNeuralSwarm",
    "NeuralAgent",
    "AgentType",
    "SessionStorage",
    "SessionMessage", 
    "SessionInfo",
    "SimpleNeuralAgent",
    "SimpleNeuralCore",
    "RuvFANNNeuralAgent",
    "unlock_ruv_fann_potential",
    "NeuralEnhancedAgent"
]

__version__ = "1.0.0"
