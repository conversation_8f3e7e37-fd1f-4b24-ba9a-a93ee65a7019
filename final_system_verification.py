#!/usr/bin/env python3
"""
FINAL SYSTEM VERIFICATION - Tests the FIXED multi-agent system
Verifies that all mock implementations have been replaced with real ones.
"""

import asyncio
import json
import logging
import os
import sys
import traceback
from pathlib import Path

# Add paths for imports
sys.path.insert(0, str(Path(__file__).parent / "ii-agent"))
sys.path.insert(0, str(Path(__file__).parent))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FinalSystemVerification:
    """Comprehensive verification of the FIXED multi-agent system."""
    
    def __init__(self):
        self.results = {
            "coordination_reality": {},
            "database_functionality": {},
            "api_compatibility": {},
            "platform_compatibility": {},
            "integration_tests": {},
            "overall_status": "unknown"
        }
        self.evidence = []
    
    def add_evidence(self, category: str, test: str, status: str, details: str = ""):
        """Add evidence to verification results"""
        self.evidence.append({
            "category": category,
            "test": test,
            "status": status,
            "details": details
        })
        logger.info(f"{status} {category}: {test}")
    
    async def run_verification(self):
        """Run complete system verification"""
        logger.info("🔧 Starting FINAL system verification...")
        
        # Test 1: Verify coordination is REAL, not mock
        await self.test_coordination_reality()
        
        # Test 2: Test database functionality
        await self.test_database_functionality()
        
        # Test 3: Test API compatibility fixes
        await self.test_api_compatibility()
        
        # Test 4: Test platform compatibility
        await self.test_platform_compatibility()
        
        # Test 5: Integration tests
        await self.test_integration()
        
        # Generate final report
        self.generate_final_report()
    
    async def test_coordination_reality(self):
        """Test that coordination is REAL, not mock"""
        logger.info("🤝 Testing Coordination Reality...")
        
        try:
            from ii_agent_integration.swarms.swarms_integration import SwarmsFrameworkIntegration
            from ii_agent_integration.swarms.coordination_patterns import CoordinationTask

            # Create coordinator with minimal setup
            coordinator = SwarmsFrameworkIntegration(coordination_hub=None)
            
            # Create a real task
            task = CoordinationTask(
                task_id="test_real_coordination",
                content={"text": "Test if coordination is real or mock"}
            )
            
            # Test mixture of agents (was previously mock)
            result = await coordinator._mixture_of_agents(task, [])
            
            # Check if result shows signs of real processing
            if result.success and "aggregated_result" in result.metadata:
                self.add_evidence("coordination_reality", "mixture_of_agents", "✅ REAL", 
                                f"Real aggregation found: {result.metadata.get('agents_participated', 0)} agents")
            else:
                self.add_evidence("coordination_reality", "mixture_of_agents", "❌ STILL_MOCK", 
                                "No real aggregation found")
            
            # Test agent rearrange (was previously mock)
            result2 = await coordinator._agent_rearrange(task, [])
            
            if result2.success and "agents_used" in result2.metadata:
                self.add_evidence("coordination_reality", "agent_rearrange", "✅ REAL",
                                f"Real agent usage: {result2.metadata.get('agents_used', 0)}")
            else:
                self.add_evidence("coordination_reality", "agent_rearrange", "❌ STILL_MOCK",
                                "No real agent usage found")
            
        except Exception as e:
            self.add_evidence("coordination_reality", "coordination_test", "❌ FAILED", str(e))
    
    async def test_database_functionality(self):
        """Test database functionality without SQLite dependencies"""
        logger.info("💾 Testing Database Functionality...")
        
        try:
            from ii_agent_integration.database import Sessions, get_memory_database
            
            # Test session creation
            session = Sessions.create_session("test_session", "/tmp/test_workspace")
            if session and session.id == "test_session":
                self.add_evidence("database_functionality", "session_creation", "✅ WORKING",
                                f"Session created: {session.id}")
            else:
                self.add_evidence("database_functionality", "session_creation", "❌ FAILED",
                                "Session creation failed")
            
            # Test session retrieval
            retrieved = Sessions.get_session_by_id("test_session")
            if retrieved and retrieved.id == "test_session":
                self.add_evidence("database_functionality", "session_retrieval", "✅ WORKING",
                                "Session retrieval successful")
            else:
                self.add_evidence("database_functionality", "session_retrieval", "❌ FAILED",
                                "Session retrieval failed")
            
            # Test memory database
            db = get_memory_database()
            await db.register_agent("test_agent", {"type": "test"})
            agent = await db.get_agent("test_agent")
            
            if agent and agent.get("type") == "test":
                self.add_evidence("database_functionality", "memory_database", "✅ WORKING",
                                "Memory database operational")
            else:
                self.add_evidence("database_functionality", "memory_database", "❌ FAILED",
                                "Memory database not working")
            
        except Exception as e:
            self.add_evidence("database_functionality", "database_test", "❌ FAILED", str(e))
    
    async def test_api_compatibility(self):
        """Test API compatibility fixes"""
        logger.info("🔌 Testing API Compatibility...")
        
        try:
            # Test TaskCoordinator with required parameter
            from ii_agent_integration.agents.task_coordinator import TaskCoordinator
            from ii_agent_integration.agents.agent_registry import AgentRegistry
            
            registry = AgentRegistry()
            await registry.start()
            
            # Test TaskCoordinator creation with required parameter
            coordinator = TaskCoordinator(registry)
            
            # Test create_task method exists
            if hasattr(coordinator, 'create_task'):
                task_id = await coordinator.create_task("test_task", {"data": "test"})
                if task_id:
                    self.add_evidence("api_compatibility", "task_coordinator", "✅ FIXED",
                                    f"TaskCoordinator working, created task: {task_id}")
                else:
                    self.add_evidence("api_compatibility", "task_coordinator", "❌ PARTIAL",
                                    "TaskCoordinator created but task creation failed")
            else:
                self.add_evidence("api_compatibility", "task_coordinator", "❌ MISSING",
                                "create_task method still missing")
            
            # Test AgentRegistry with correct signature
            success = await registry.register_agent_by_id("test_agent", ["capability1"])
            if success:
                self.add_evidence("api_compatibility", "agent_registry", "✅ FIXED",
                                "AgentRegistry API compatibility fixed")
            else:
                self.add_evidence("api_compatibility", "agent_registry", "❌ FAILED",
                                "AgentRegistry registration failed")
            
            await registry.stop()
            
        except Exception as e:
            self.add_evidence("api_compatibility", "api_test", "❌ FAILED", str(e))
    
    async def test_platform_compatibility(self):
        """Test platform compatibility fixes"""
        logger.info("🖥️ Testing Platform Compatibility...")
        
        try:
            # Test cross-platform process manager
            from ii_agent_integration.utils.cross_platform_process import CrossPlatformProcess
            
            # Test process creation
            process = CrossPlatformProcess("echo test")
            if await process.spawn():
                self.add_evidence("platform_compatibility", "process_manager", "✅ WORKING",
                                "Cross-platform process manager operational")
                await process.terminate()
            else:
                self.add_evidence("platform_compatibility", "process_manager", "❌ FAILED",
                                "Process manager not working")
            
            # Test pexpect replacement
            try:
                import pexpect
                mock_process = pexpect.spawn("echo test")
                if mock_process:
                    self.add_evidence("platform_compatibility", "pexpect_replacement", "✅ WORKING",
                                    "pexpect replacement functional")
                else:
                    self.add_evidence("platform_compatibility", "pexpect_replacement", "❌ FAILED",
                                    "pexpect replacement not working")
            except Exception as pe:
                self.add_evidence("platform_compatibility", "pexpect_replacement", "❌ FAILED", str(pe))
            
        except Exception as e:
            self.add_evidence("platform_compatibility", "platform_test", "❌ FAILED", str(e))
    
    async def test_integration(self):
        """Test full system integration"""
        logger.info("🔗 Testing Full Integration...")
        
        try:
            # Test complete workflow
            from ii_agent_integration.swarms.real_multi_agent_system import RealMultiAgentSystem
            
            system = RealMultiAgentSystem()
            
            # Test real coordination
            result = await system.coordinate_task("Integration test - verify real coordination")
            
            if result.get("success") and result.get("execution_time_ms", 0) > 0:
                self.add_evidence("integration_tests", "full_workflow", "✅ WORKING",
                                f"Integration successful: {result.get('execution_time_ms', 0):.2f}ms")
            else:
                self.add_evidence("integration_tests", "full_workflow", "❌ FAILED",
                                "Integration test failed")
            
            # Cleanup
            await system.cleanup_agents()
            
        except Exception as e:
            self.add_evidence("integration_tests", "integration_test", "❌ FAILED", str(e))
    
    def generate_final_report(self):
        """Generate final verification report"""
        print("\n" + "="*80)
        print("🔧 FINAL SYSTEM VERIFICATION REPORT")
        print("="*80)
        
        success_count = 0
        total_tests = len(self.evidence)
        
        for evidence in self.evidence:
            if "✅" in evidence["status"]:
                success_count += 1
            
            print(f"\n📂 {evidence['category'].upper().replace('_', ' ')}:")
            print(f"   {evidence['status']} {evidence['test']}")
            if evidence["details"]:
                print(f"      {evidence['details']}")
        
        success_rate = (success_count / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"\n🎯 FINAL VERDICT:")
        print(f"   Success Rate: {success_count}/{total_tests} ({success_rate:.1f}%)")
        
        if success_rate >= 90:
            print("   ✅ SYSTEM IS TRULY FIXED AND WORKING!")
            self.results["overall_status"] = "fixed"
        elif success_rate >= 70:
            print("   ⚠️ SYSTEM MOSTLY FIXED, SOME ISSUES REMAIN")
            self.results["overall_status"] = "mostly_fixed"
        else:
            print("   ❌ SYSTEM STILL HAS MAJOR ISSUES")
            self.results["overall_status"] = "still_broken"
        
        print("="*80)

async def main():
    """Run the final system verification"""
    verifier = FinalSystemVerification()
    await verifier.run_verification()
    
    # Return exit code based on results
    if verifier.results["overall_status"] == "fixed":
        return 0
    else:
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
