"""
Real Multi-Agent System Integration
==================================

Production-ready multi-agent system that combines real neural networks
with real coordination patterns for genuine swarm intelligence.
"""

import asyncio
import logging
import time
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
import re
import hashlib

logger = logging.getLogger(__name__)

# Input validation
try:
    from pydantic import BaseModel, validator
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False
    logger.warning("Pydantic not available, using basic input validation")

# Import real implementations
from .real_coordination_patterns import RealCoordinationPatterns, AgentResponse
from ..neural.real_neural_networks import RealNeuralAgent, AgentSpecialization, NeuralConfig

# Import GPU-accelerated implementations
try:
    from ..neural.gpu_neural_networks import GPUAcceleratedAgent, GPUNeuralConfig
    from ..gpu.real_gpu_acceleration import <PERSON><PERSON><PERSON><PERSON><PERSON>, GPUConfig, get_gpu_manager, is_gpu_available
    GPU_ACCELERATION_AVAILABLE = True
    logger.info("✅ GPU acceleration available")
except ImportError as e:
    GPU_ACCELERATION_AVAILABLE = False
    logger.warning(f"GPU acceleration not available: {e}")
    GPUAcceleratedAgent = None
    GPUNeuralConfig = None

# Import numpy for performance calculations
import numpy as np


class TaskValidationError(Exception):
    """Custom exception for task validation errors"""
    pass


class TaskRequest:
    """Input validation for coordination tasks"""

    def __init__(self, task: str, strategy: str = "mixture_of_agents", max_agents: int = 10):
        self.task = self._validate_task(task)
        self.strategy = self._validate_strategy(strategy)
        self.max_agents = self._validate_max_agents(max_agents)

    def _validate_task(self, task: str) -> str:
        """Validate task input"""
        if not task or not isinstance(task, str):
            raise TaskValidationError("Task must be a non-empty string")

        task = task.strip()
        if len(task) == 0:
            raise TaskValidationError("Task cannot be empty")

        if len(task) > 10000:
            raise TaskValidationError("Task too long (max 10000 characters)")

        # Prevent injection attacks
        forbidden_patterns = [
            r'<script[^>]*>',  # Script tags
            r'javascript:',     # JavaScript URLs
            r'on\w+\s*=',      # Event handlers
            r'<iframe[^>]*>',  # Iframe tags
            r'<object[^>]*>',  # Object tags
            r'<embed[^>]*>',   # Embed tags
        ]

        for pattern in forbidden_patterns:
            if re.search(pattern, task, re.IGNORECASE):
                raise TaskValidationError(f"Task contains forbidden pattern: {pattern}")

        # Check for suspicious characters
        forbidden_chars = ['<', '>', '"', "'", '`', '\x00', '\r']
        for char in forbidden_chars:
            if char in task:
                raise TaskValidationError(f"Task contains forbidden character: {repr(char)}")

        return task

    def _validate_strategy(self, strategy: str) -> str:
        """Validate coordination strategy"""
        if not strategy or not isinstance(strategy, str):
            raise TaskValidationError("Strategy must be a non-empty string")

        allowed_strategies = [
            "mixture_of_agents",
            "consensus_swarm",
            "hierarchical_swarm"
        ]

        if strategy not in allowed_strategies:
            raise TaskValidationError(f"Invalid strategy '{strategy}'. Allowed: {allowed_strategies}")

        return strategy

    def _validate_max_agents(self, max_agents: int) -> int:
        """Validate max agents parameter"""
        if not isinstance(max_agents, int):
            raise TaskValidationError("max_agents must be an integer")

        if max_agents < 1:
            raise TaskValidationError("max_agents must be at least 1")

        if max_agents > 100:
            raise TaskValidationError("max_agents cannot exceed 100")

        return max_agents


@dataclass
class SwarmConfig:
    """Configuration for the real multi-agent swarm"""
    max_agents: int = 10
    neural_config: NeuralConfig = field(default_factory=NeuralConfig)
    coordination_timeout_ms: int = 30000
    consensus_threshold: float = 0.7
    enable_learning: bool = True
    enable_gpu: bool = True
    gpu_device_id: int = 0
    enable_mixed_precision: bool = True
    gpu_memory_strategy: str = "balanced"
    parallel_inference: bool = True


class SwarmState(Enum):
    """Real swarm states"""
    INITIALIZING = "initializing"
    READY = "ready"
    COORDINATING = "coordinating"
    LEARNING = "learning"
    ERROR = "error"


class RealMultiAgentSystem:
    """Production multi-agent system with real neural networks and coordination"""
    
    def __init__(self, config: SwarmConfig = None):
        self.config = config or SwarmConfig()
        self.state = SwarmState.INITIALIZING

        # Check GPU availability and initialize GPU manager if needed
        self.gpu_manager = None
        self.gpu_available = False
        if self.config.enable_gpu and GPU_ACCELERATION_AVAILABLE:
            try:
                gpu_config = GPUConfig(
                    device_id=self.config.gpu_device_id,
                    enable_mixed_precision=self.config.enable_mixed_precision
                )
                self.gpu_manager = get_gpu_manager(gpu_config)
                self.gpu_available = self.gpu_manager.is_gpu_available()
                logger.info(f"🚀 GPU acceleration {'enabled' if self.gpu_available else 'attempted but failed'}")
            except Exception as e:
                logger.warning(f"GPU initialization failed: {e}")
                self.gpu_available = False

        # Real neural agents (GPU or CPU)
        self.agents: Dict[str, Any] = {}  # Can be RealNeuralAgent or GPUAcceleratedAgent

        # Real coordination patterns
        self.coordination_patterns = RealCoordinationPatterns()

        # Performance tracking
        self.coordination_history = []
        self.learning_history = []
        self.gpu_performance_history = []
        self.performance_metrics = {
            "total_coordinations": 0,
            "successful_coordinations": 0,
            "avg_consensus_score": 0.0,
            "avg_execution_time_ms": 0.0,
            "total_learning_episodes": 0,
            "gpu_accelerated": self.gpu_available,
            "avg_gpu_speedup": 0.0
        }

        # Initialize agents
        self._initialize_agents()

        self.state = SwarmState.READY
        agent_type = "GPU-accelerated" if self.gpu_available else "CPU-based"
        logger.info(f"Real multi-agent system initialized with {len(self.agents)} {agent_type} neural agents")
    
    def _initialize_agents(self):
        """Initialize real neural agents with GPU acceleration if available"""
        specializations = [
            AgentSpecialization.RESEARCHER,
            AgentSpecialization.CODER,
            AgentSpecialization.ANALYST,
            AgentSpecialization.COORDINATOR,
            AgentSpecialization.OPTIMIZER
        ]

        for i, specialization in enumerate(specializations):
            agent_id = f"neural_agent_{i}_{specialization.value}"

            if self.gpu_available and GPUAcceleratedAgent:
                # Create GPU-accelerated agent
                gpu_config = GPUNeuralConfig(
                    input_size=512,
                    hidden_sizes=[256, 128, 64],
                    output_size=32,
                    learning_rate=0.001,
                    enable_gpu=True,
                    gpu_device_id=self.config.gpu_device_id,
                    enable_mixed_precision=self.config.enable_mixed_precision,
                    parallel_inference=self.config.parallel_inference,
                    max_batch_size=64
                )
                agent = GPUAcceleratedAgent(agent_id, specialization, gpu_config)
                logger.info(f"🚀 Initialized GPU-accelerated agent: {agent_id}")
            else:
                # Create CPU-based agent
                neural_config = NeuralConfig(
                    input_size=512,
                    hidden_sizes=[256, 128, 64],
                    output_size=32,
                    learning_rate=0.001,
                    device="cpu"
                )
                agent = RealNeuralAgent(agent_id, specialization, neural_config)
                logger.info(f"Initialized CPU neural agent: {agent_id}")

            self.agents[agent_id] = agent

    async def cleanup_agents(self):
        """Cleanup all agents and release resources"""
        logger.info(f"Cleaning up {len(self.agents)} agents...")

        cleanup_tasks = []
        for agent_id, agent in self.agents.items():
            cleanup_tasks.append(self._cleanup_single_agent(agent_id, agent))

        # Cleanup all agents concurrently
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)

        # Clear agent dictionary
        self.agents.clear()

        # Clear GPU cache if available
        if self.gpu_manager and self.gpu_manager.is_gpu_available():
            self.gpu_manager.clear_gpu_cache()

        logger.info("Agent cleanup completed")

    async def _cleanup_single_agent(self, agent_id: str, agent: Any):
        """Cleanup a single agent and its resources"""
        try:
            # GPU agent cleanup
            if hasattr(agent, 'network') and hasattr(agent.network, 'gpu_manager'):
                if agent.network.gpu_manager.is_gpu_available():
                    # Clear GPU memory for this agent
                    agent.network.gpu_manager.clear_gpu_cache()

            # Save model if it has training history
            if hasattr(agent, 'save_model') and hasattr(agent, 'training_history'):
                if len(agent.training_history) > 0:
                    agent.save_model()

            # Close any open file handles or connections
            if hasattr(agent, 'cleanup'):
                await agent.cleanup()

            logger.debug(f"Agent {agent_id} cleaned up successfully")

        except Exception as e:
            logger.error(f"Failed to cleanup agent {agent_id}: {e}")

    def __del__(self):
        """Ensure cleanup on destruction"""
        if hasattr(self, 'agents') and self.agents:
            try:
                # Create cleanup task for event loop
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.cleanup_agents())
                else:
                    loop.run_until_complete(self.cleanup_agents())
            except Exception as e:
                logger.error(f"Failed to cleanup agents in destructor: {e}")

    async def coordinate_task(self, task: str, coordination_strategy: str = "mixture_of_agents") -> Dict[str, Any]:
        """Coordinate task execution across real neural agents with input validation"""

        # 🔐 SECURITY: Validate input before processing
        try:
            validated_request = TaskRequest(task=task, strategy=coordination_strategy)
            task = validated_request.task
            coordination_strategy = validated_request.strategy
        except TaskValidationError as e:
            logger.warning(f"Task validation failed: {e}")
            return {
                "success": False,
                "error": f"Input validation failed: {str(e)}",
                "task": task[:100] if isinstance(task, str) else str(task)[:100],  # Truncated for logging
                "strategy": coordination_strategy,
                "execution_time_ms": 0,
                "swarm_state": self.state.value
            }
        except Exception as e:
            logger.error(f"Unexpected validation error: {e}")
            return {
                "success": False,
                "error": f"Validation error: {str(e)}",
                "task": "INVALID_INPUT",
                "strategy": coordination_strategy,
                "execution_time_ms": 0,
                "swarm_state": self.state.value
            }

        if self.state != SwarmState.READY:
            raise RuntimeError(f"Swarm not ready for coordination. Current state: {self.state}")

        self.state = SwarmState.COORDINATING
        start_time = time.time()

        try:
            # Phase 1: Get neural agent responses
            agent_responses = await self._get_agent_responses(task)
            
            # Phase 2: Apply coordination pattern
            coordination_result = await self._apply_coordination_pattern(
                task, agent_responses, coordination_strategy
            )
            
            # Phase 3: Learn from coordination
            if self.config.enable_learning:
                await self._learn_from_coordination(task, agent_responses, coordination_result)
            
            # Update performance metrics
            execution_time = (time.time() - start_time) * 1000
            self._update_performance_metrics(coordination_result, execution_time)
            
            # Record coordination history
            self.coordination_history.append({
                "timestamp": time.time(),
                "task": task[:100],  # Truncated for storage
                "strategy": coordination_strategy,
                "success": coordination_result.get("success", False),
                "consensus_score": coordination_result.get("consensus_score", 0.0),
                "execution_time_ms": execution_time,
                "participating_agents": len(agent_responses)
            })
            
            self.state = SwarmState.READY
            
            return {
                "success": True,
                "task": task,
                "coordination_strategy": coordination_strategy,
                "result": coordination_result,
                "execution_time_ms": execution_time,
                "participating_agents": len(agent_responses),
                "swarm_state": self.state.value
            }
            
        except Exception as e:
            self.state = SwarmState.ERROR
            logger.error(f"Coordination failed: {e}")
            
            return {
                "success": False,
                "task": task,
                "error": str(e),
                "execution_time_ms": (time.time() - start_time) * 1000,
                "swarm_state": self.state.value
            }
    
    async def _get_agent_responses(self, task: str) -> List[AgentResponse]:
        """Get responses from all neural agents"""
        agent_tasks = []
        
        for agent_id, agent in self.agents.items():
            agent_tasks.append(self._get_single_agent_response(agent, task))
        
        # Execute all agent analyses concurrently
        agent_results = await asyncio.gather(*agent_tasks, return_exceptions=True)
        
        # Convert to AgentResponse objects
        agent_responses = []
        for i, result in enumerate(agent_results):
            if isinstance(result, Exception):
                logger.warning(f"Agent {list(self.agents.keys())[i]} failed: {result}")
                continue
            
            if result and "confidence" in result:
                agent_responses.append(AgentResponse(
                    agent_id=result["agent_id"],
                    response=result["analysis"],
                    confidence=result["confidence"],
                    reasoning=f"Neural analysis by {result['specialization']} agent",
                    execution_time_ms=result["execution_time_ms"],
                    metadata=result.get("metadata", {})
                ))
        
        return agent_responses
    
    async def _get_single_agent_response(self, agent: Any, task: str) -> Dict[str, Any]:
        """Get response from a single neural agent (GPU or CPU)"""
        try:
            # Check if it's a GPU-accelerated agent
            if hasattr(agent, 'analyze_task_gpu'):
                return await agent.analyze_task_gpu(task)
            else:
                return await agent.analyze_task(task)
        except Exception as e:
            logger.error(f"Agent {agent.agent_id} analysis failed: {e}")
            return {
                "agent_id": agent.agent_id,
                "analysis": {"error": str(e)},
                "confidence": 0.0,
                "execution_time_ms": 0.0,
                "specialization": agent.specialization.value,
                "gpu_accelerated": hasattr(agent, 'analyze_task_gpu')
            }
    
    async def _apply_coordination_pattern(self, task: str, agent_responses: List[AgentResponse], strategy: str) -> Dict[str, Any]:
        """Apply real coordination pattern to agent responses"""
        try:
            if strategy == "mixture_of_agents":
                return await self.coordination_patterns.execute_mixture_of_agents(task, agent_responses)
            elif strategy == "consensus_swarm":
                return await self.coordination_patterns.execute_consensus_swarm(task, agent_responses)
            elif strategy == "hierarchical_swarm":
                return await self.coordination_patterns.execute_hierarchical_swarm(task, agent_responses)
            else:
                # Default to mixture of agents
                logger.warning(f"Unknown strategy {strategy}, using mixture_of_agents")
                return await self.coordination_patterns.execute_mixture_of_agents(task, agent_responses)
                
        except Exception as e:
            logger.error(f"Coordination pattern {strategy} failed: {e}")
            return {
                "success": False,
                "strategy": strategy,
                "error": str(e),
                "agent_responses": [resp.to_dict() for resp in agent_responses]
            }
    
    async def _learn_from_coordination(self, task: str, agent_responses: List[AgentResponse], coordination_result: Dict[str, Any]):
        """Learn from coordination results to improve future performance"""
        if not coordination_result.get("success", False):
            return
        
        self.state = SwarmState.LEARNING
        
        try:
            # Calculate feedback scores based on coordination success
            consensus_score = coordination_result.get("consensus_score", 0.0)
            
            # Train agents based on their contribution to consensus
            for response in agent_responses:
                agent = self.agents.get(response.agent_id)
                if not agent:
                    continue
                
                # Calculate individual feedback score
                individual_score = response.confidence * consensus_score
                
                # Create target output (simplified - in production, use more sophisticated targets)
                target_output = np.random.normal(individual_score, 0.1, agent.config.output_size)
                target_tensor = torch.tensor(target_output, dtype=torch.float32)
                
                # Train agent
                await agent.train_on_feedback(task, target_tensor, individual_score)
            
            # Record learning episode
            self.learning_history.append({
                "timestamp": time.time(),
                "task": task[:50],
                "consensus_score": consensus_score,
                "agents_trained": len(agent_responses),
                "avg_confidence": np.mean([r.confidence for r in agent_responses])
            })
            
            self.performance_metrics["total_learning_episodes"] += 1
            
            logger.info(f"Learning completed for {len(agent_responses)} agents")
            
        except Exception as e:
            logger.error(f"Learning failed: {e}")
        finally:
            self.state = SwarmState.READY
    
    def _update_performance_metrics(self, coordination_result: Dict[str, Any], execution_time_ms: float):
        """Update swarm performance metrics"""
        self.performance_metrics["total_coordinations"] += 1
        
        if coordination_result.get("success", False):
            self.performance_metrics["successful_coordinations"] += 1
        
        # Update averages
        total = self.performance_metrics["total_coordinations"]
        
        # Update average consensus score
        current_consensus = coordination_result.get("consensus_score", 0.0)
        prev_avg_consensus = self.performance_metrics["avg_consensus_score"]
        self.performance_metrics["avg_consensus_score"] = (
            (prev_avg_consensus * (total - 1) + current_consensus) / total
        )
        
        # Update average execution time
        prev_avg_time = self.performance_metrics["avg_execution_time_ms"]
        self.performance_metrics["avg_execution_time_ms"] = (
            (prev_avg_time * (total - 1) + execution_time_ms) / total
        )
    
    def get_swarm_status(self) -> Dict[str, Any]:
        """Get comprehensive swarm status"""
        agent_metrics = {}
        for agent_id, agent in self.agents.items():
            agent_metrics[agent_id] = agent.get_performance_metrics()
        
        return {
            "swarm_state": self.state.value,
            "total_agents": len(self.agents),
            "performance_metrics": self.performance_metrics,
            "agent_metrics": agent_metrics,
            "recent_coordinations": self.coordination_history[-5:],  # Last 5 coordinations
            "recent_learning": self.learning_history[-5:],  # Last 5 learning episodes
            "config": {
                "max_agents": self.config.max_agents,
                "consensus_threshold": self.config.consensus_threshold,
                "learning_enabled": self.config.enable_learning,
                "gpu_enabled": self.config.enable_gpu
            }
        }
    
    async def benchmark_gpu_performance(self, test_task: str = "Analyze system performance", iterations: int = 10) -> Dict[str, Any]:
        """Benchmark GPU vs CPU performance across all agents"""
        if not self.gpu_available:
            return {"error": "GPU not available for benchmarking"}

        logger.info(f"🚀 Benchmarking GPU performance across {len(self.agents)} agents...")

        benchmark_results = {
            "test_task": test_task,
            "iterations": iterations,
            "agent_benchmarks": {},
            "overall_speedup": 0.0,
            "total_gpu_time_ms": 0.0,
            "total_cpu_time_ms": 0.0
        }

        total_speedups = []

        for agent_id, agent in self.agents.items():
            if hasattr(agent, 'benchmark_gpu_vs_cpu'):
                try:
                    agent_result = agent.benchmark_gpu_vs_cpu(test_task, iterations)
                    benchmark_results["agent_benchmarks"][agent_id] = agent_result

                    if agent_result.get("speedup", 0) > 0:
                        total_speedups.append(agent_result["speedup"])
                        benchmark_results["total_gpu_time_ms"] += agent_result.get("avg_gpu_time_ms", 0)
                        benchmark_results["total_cpu_time_ms"] += agent_result.get("avg_cpu_time_ms", 0)

                    logger.info(f"   Agent {agent_id}: {agent_result.get('speedup', 0):.2f}x speedup")

                except Exception as e:
                    logger.error(f"Benchmark failed for agent {agent_id}: {e}")
                    benchmark_results["agent_benchmarks"][agent_id] = {"error": str(e)}

        # Calculate overall speedup
        if total_speedups:
            benchmark_results["overall_speedup"] = np.mean(total_speedups)
            benchmark_results["min_speedup"] = np.min(total_speedups)
            benchmark_results["max_speedup"] = np.max(total_speedups)

            logger.info(f"🎯 Overall GPU Speedup: {benchmark_results['overall_speedup']:.2f}x")
            logger.info(f"   Range: {benchmark_results['min_speedup']:.2f}x - {benchmark_results['max_speedup']:.2f}x")

        # Update performance metrics
        self.performance_metrics["avg_gpu_speedup"] = benchmark_results.get("overall_speedup", 0.0)
        self.gpu_performance_history.append(benchmark_results)

        return benchmark_results

    def get_gpu_status(self) -> Dict[str, Any]:
        """Get comprehensive GPU status for the swarm"""
        status = {
            "gpu_available": self.gpu_available,
            "gpu_manager_initialized": self.gpu_manager is not None,
            "agents_using_gpu": 0,
            "total_agents": len(self.agents)
        }

        if self.gpu_manager:
            status.update(self.gpu_manager.get_gpu_status())

        # Count GPU-accelerated agents
        for agent in self.agents.values():
            if hasattr(agent, 'gpu_manager') and agent.gpu_manager.is_gpu_available():
                status["agents_using_gpu"] += 1

        # Add agent-specific GPU metrics
        agent_gpu_metrics = {}
        for agent_id, agent in self.agents.items():
            if hasattr(agent, 'get_gpu_performance_metrics'):
                try:
                    agent_gpu_metrics[agent_id] = agent.get_gpu_performance_metrics()
                except Exception as e:
                    agent_gpu_metrics[agent_id] = {"error": str(e)}

        status["agent_gpu_metrics"] = agent_gpu_metrics
        status["gpu_performance_history"] = len(self.gpu_performance_history)

        return status

    async def save_swarm_state(self, filepath: str):
        """Save swarm state for persistence"""
        try:
            # Save all agent models
            for agent in self.agents.values():
                if hasattr(agent, 'save_model'):
                    agent.save_model()
                elif hasattr(agent, 'network') and hasattr(agent.network, 'save_model'):
                    # For GPU agents, save the underlying network
                    pass  # GPU agents handle their own model saving

            # Save swarm metadata
            swarm_data = {
                "performance_metrics": self.performance_metrics,
                "coordination_history": self.coordination_history,
                "learning_history": self.learning_history,
                "gpu_performance_history": self.gpu_performance_history,
                "config": self.config.__dict__,
                "gpu_status": self.get_gpu_status()
            }

            with open(filepath, 'w') as f:
                json.dump(swarm_data, f, indent=2, default=str)  # default=str for non-serializable objects

            logger.info(f"Swarm state saved to {filepath}")

        except Exception as e:
            logger.error(f"Failed to save swarm state: {e}")


# Import torch here to avoid import errors if PyTorch is not installed
try:
    import torch
except ImportError:
    logger.warning("PyTorch not available. Neural networks will use fallback implementation.")
    torch = None
