{"validation_results": {"imports_working": true, "gpu_actually_available": true, "wasm_actually_working": true, "neural_networks_real": false, "performance_claims_accurate": false, "fallback_actually_works": true, "response_times_honest": [{"test": "Simple test", "actual_ms": 8.331060409545898, "claimed_ms": 7.777929306030273, "path": "ultimate_gpu", "utilization": 100.0, "honest": true}, {"test": "Medium complexity analysis wit", "actual_ms": 6.258964538574219, "claimed_ms": 6.258964538574219, "path": "ultimate_gpu", "utilization": 100.0, "honest": true}, {"test": "Complex algorithmic optimizati", "actual_ms": 6.005764007568359, "claimed_ms": 6.005764007568359, "path": "ultimate_gpu", "utilization": 100.0, "honest": true}], "actual_computations": [], "system_honesty_score": 80.0}, "performance_results": [{"complexity": "Simple", "input_length": 2, "avg_time": 5.6260426839192705, "claimed_time": 5.43967882792155, "path": "ultimate_gpu"}, {"complexity": "Medium", "input_length": 48, "avg_time": 6.230592727661133, "claimed_time": 5.711714426676433, "path": "ultimate_gpu"}, {"complexity": "Complex", "input_length": 164, "avg_time": 6.804148356119792, "claimed_time": 6.622473398844401, "path": "ultimate_gpu"}, {"complexity": "Extreme", "input_length": 675, "avg_time": 8.36491584777832, "claimed_time": 7.758776346842448, "path": "ultimate_gpu"}], "honesty_score": 80.0, "timestamp": 1754525320.675324}