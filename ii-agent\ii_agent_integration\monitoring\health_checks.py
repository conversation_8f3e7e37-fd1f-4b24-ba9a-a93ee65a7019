"""
Production Health Checks
========================

Comprehensive health monitoring for the multi-agent system including
GPU status, neural network health, coordination system status, and overall system health.
"""

import asyncio
import time
import logging
import psutil
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json

logger = logging.getLogger(__name__)

# Import system components
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

logger = logging.getLogger(__name__)

try:
    from ..gpu.real_gpu_acceleration import get_gpu_manager
    from ..swarms.real_multi_agent_system import RealMultiAgentSystem
    from ..neural.real_neural_networks import RealNeuralAgent
    GPU_SYSTEM_AVAILABLE = True
except ImportError:
    GPU_SYSTEM_AVAILABLE = False
    logger.warning("GPU system components not available for health checks")


class HealthStatus(Enum):
    """Health check status levels"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """Result of a health check"""
    component: str
    status: HealthStatus
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)
    response_time_ms: float = 0.0


class ProductionHealthMonitor:
    """Production-ready health monitoring system"""
    
    def __init__(self):
        self.health_history: List[HealthCheckResult] = []
        self.last_check_time = 0.0
        self.check_interval = 30.0  # 30 seconds
        self.monitoring_active = False
        
        logger.info("Production health monitor initialized")
    
    async def check_system_health(self) -> HealthCheckResult:
        """Check overall system health"""
        start_time = time.perf_counter()
        
        try:
            # CPU check
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 90:
                status = HealthStatus.CRITICAL
                message = f"High CPU usage: {cpu_percent:.1f}%"
            elif cpu_percent > 70:
                status = HealthStatus.WARNING
                message = f"Elevated CPU usage: {cpu_percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"CPU usage normal: {cpu_percent:.1f}%"
            
            # Memory check
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            if memory_percent > 90:
                if status != HealthStatus.CRITICAL:
                    status = HealthStatus.CRITICAL
                message += f", High memory usage: {memory_percent:.1f}%"
            elif memory_percent > 80:
                if status == HealthStatus.HEALTHY:
                    status = HealthStatus.WARNING
                message += f", Elevated memory usage: {memory_percent:.1f}%"
            
            # Disk check
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            if disk_percent > 95:
                status = HealthStatus.CRITICAL
                message += f", Disk space critical: {disk_percent:.1f}%"
            elif disk_percent > 85:
                if status == HealthStatus.HEALTHY:
                    status = HealthStatus.WARNING
                message += f", Disk space low: {disk_percent:.1f}%"
            
            response_time = (time.perf_counter() - start_time) * 1000
            
            return HealthCheckResult(
                component="system",
                status=status,
                message=message,
                details={
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory_percent,
                    "disk_percent": disk_percent,
                    "memory_available_gb": memory.available / (1024**3),
                    "disk_free_gb": disk.free / (1024**3)
                },
                response_time_ms=response_time
            )
            
        except Exception as e:
            response_time = (time.perf_counter() - start_time) * 1000
            return HealthCheckResult(
                component="system",
                status=HealthStatus.CRITICAL,
                message=f"System health check failed: {str(e)}",
                response_time_ms=response_time
            )
    
    async def check_gpu_health(self) -> HealthCheckResult:
        """Check GPU system health"""
        start_time = time.perf_counter()
        
        if not TORCH_AVAILABLE:
            return HealthCheckResult(
                component="gpu",
                status=HealthStatus.WARNING,
                message="PyTorch not available",
                response_time_ms=(time.perf_counter() - start_time) * 1000
            )
        
        try:
            cuda_available = torch.cuda.is_available()
            
            if not cuda_available:
                return HealthCheckResult(
                    component="gpu",
                    status=HealthStatus.WARNING,
                    message="CUDA not available, using CPU fallback",
                    details={"cuda_available": False, "device_count": 0},
                    response_time_ms=(time.perf_counter() - start_time) * 1000
                )
            
            # Check GPU status
            device_count = torch.cuda.device_count()
            current_device = torch.cuda.current_device()
            
            # Check GPU memory
            allocated = torch.cuda.memory_allocated(current_device)
            total = torch.cuda.get_device_properties(current_device).total_memory
            memory_percent = (allocated / total) * 100
            
            if memory_percent > 95:
                status = HealthStatus.CRITICAL
                message = f"GPU memory critical: {memory_percent:.1f}%"
            elif memory_percent > 85:
                status = HealthStatus.WARNING
                message = f"GPU memory high: {memory_percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"GPU healthy: {memory_percent:.1f}% memory used"
            
            # Test GPU operation
            try:
                test_tensor = torch.randn(100, 100, device=current_device)
                _ = torch.mm(test_tensor, test_tensor)
                torch.cuda.synchronize()
                gpu_operational = True
            except Exception as e:
                gpu_operational = False
                status = HealthStatus.CRITICAL
                message = f"GPU operation failed: {str(e)}"
            
            response_time = (time.perf_counter() - start_time) * 1000
            
            return HealthCheckResult(
                component="gpu",
                status=status,
                message=message,
                details={
                    "cuda_available": cuda_available,
                    "device_count": device_count,
                    "current_device": current_device,
                    "memory_allocated_gb": allocated / (1024**3),
                    "memory_total_gb": total / (1024**3),
                    "memory_percent": memory_percent,
                    "gpu_operational": gpu_operational
                },
                response_time_ms=response_time
            )
            
        except Exception as e:
            response_time = (time.perf_counter() - start_time) * 1000
            return HealthCheckResult(
                component="gpu",
                status=HealthStatus.CRITICAL,
                message=f"GPU health check failed: {str(e)}",
                response_time_ms=response_time
            )
    
    async def check_neural_network_health(self) -> HealthCheckResult:
        """Check neural network system health"""
        start_time = time.perf_counter()
        
        try:
            if not GPU_SYSTEM_AVAILABLE:
                return HealthCheckResult(
                    component="neural_networks",
                    status=HealthStatus.WARNING,
                    message="Neural network system not available",
                    response_time_ms=(time.perf_counter() - start_time) * 1000
                )
            
            # Test neural network creation and inference
            from ..neural.real_neural_networks import RealNeuralAgent, AgentSpecialization, NeuralConfig
            
            config = NeuralConfig(input_size=64, hidden_sizes=[32], output_size=16)
            test_agent = RealNeuralAgent("health_check_agent", AgentSpecialization.RESEARCHER, config)
            
            # Test inference
            test_result = await test_agent.analyze_task("Health check test task")
            
            if test_result.get("confidence", 0) > 0:
                status = HealthStatus.HEALTHY
                message = "Neural networks operational"
            else:
                status = HealthStatus.WARNING
                message = "Neural networks responding but low confidence"
            
            response_time = (time.perf_counter() - start_time) * 1000
            
            return HealthCheckResult(
                component="neural_networks",
                status=status,
                message=message,
                details={
                    "test_inference_time_ms": test_result.get("execution_time_ms", 0),
                    "test_confidence": test_result.get("confidence", 0),
                    "agent_specialization": test_result.get("specialization", "unknown")
                },
                response_time_ms=response_time
            )
            
        except Exception as e:
            response_time = (time.perf_counter() - start_time) * 1000
            return HealthCheckResult(
                component="neural_networks",
                status=HealthStatus.CRITICAL,
                message=f"Neural network health check failed: {str(e)}",
                response_time_ms=response_time
            )
    
    async def check_coordination_health(self) -> HealthCheckResult:
        """Check multi-agent coordination system health"""
        start_time = time.perf_counter()
        
        try:
            if not GPU_SYSTEM_AVAILABLE:
                return HealthCheckResult(
                    component="coordination",
                    status=HealthStatus.WARNING,
                    message="Coordination system not available",
                    response_time_ms=(time.perf_counter() - start_time) * 1000
                )
            
            # Test coordination system
            from ..swarms.real_multi_agent_system import RealMultiAgentSystem, SwarmConfig
            
            config = SwarmConfig(max_agents=3, enable_gpu=False)  # Use CPU for health check
            test_swarm = RealMultiAgentSystem(config)
            
            # Test coordination
            test_task = "Health check coordination test"
            coordination_result = await test_swarm.coordinate_task(test_task, "mixture_of_agents")
            
            if coordination_result.get("success", False):
                status = HealthStatus.HEALTHY
                message = "Coordination system operational"
            else:
                status = HealthStatus.WARNING
                message = "Coordination system responding but with issues"
            
            response_time = (time.perf_counter() - start_time) * 1000
            
            return HealthCheckResult(
                component="coordination",
                status=status,
                message=message,
                details={
                    "test_coordination_success": coordination_result.get("success", False),
                    "test_execution_time_ms": coordination_result.get("execution_time_ms", 0),
                    "participating_agents": coordination_result.get("participating_agents", 0),
                    "swarm_state": test_swarm.state.value
                },
                response_time_ms=response_time
            )
            
        except Exception as e:
            response_time = (time.perf_counter() - start_time) * 1000
            return HealthCheckResult(
                component="coordination",
                status=HealthStatus.CRITICAL,
                message=f"Coordination health check failed: {str(e)}",
                response_time_ms=response_time
            )
    
    async def run_comprehensive_health_check(self) -> Dict[str, Any]:
        """Run all health checks and return comprehensive status"""
        logger.info("Running comprehensive health check...")
        
        start_time = time.perf_counter()
        
        # Run all health checks concurrently
        health_checks = await asyncio.gather(
            self.check_system_health(),
            self.check_gpu_health(),
            self.check_neural_network_health(),
            self.check_coordination_health(),
            return_exceptions=True
        )
        
        total_time = (time.perf_counter() - start_time) * 1000
        
        # Process results
        results = {}
        overall_status = HealthStatus.HEALTHY
        critical_issues = []
        warnings = []
        
        for check_result in health_checks:
            if isinstance(check_result, Exception):
                component = "unknown"
                status = HealthStatus.CRITICAL
                message = f"Health check crashed: {str(check_result)}"
                check_result = HealthCheckResult(component, status, message)
            
            results[check_result.component] = {
                "status": check_result.status.value,
                "message": check_result.message,
                "details": check_result.details,
                "response_time_ms": check_result.response_time_ms,
                "timestamp": check_result.timestamp
            }
            
            # Update overall status
            if check_result.status == HealthStatus.CRITICAL:
                overall_status = HealthStatus.CRITICAL
                critical_issues.append(f"{check_result.component}: {check_result.message}")
            elif check_result.status == HealthStatus.WARNING and overall_status != HealthStatus.CRITICAL:
                overall_status = HealthStatus.WARNING
                warnings.append(f"{check_result.component}: {check_result.message}")
        
        # Store in history
        self.health_history.extend([r for r in health_checks if isinstance(r, HealthCheckResult)])
        
        # Keep only last 100 health checks
        if len(self.health_history) > 100:
            self.health_history = self.health_history[-100:]
        
        comprehensive_result = {
            "overall_status": overall_status.value,
            "total_check_time_ms": total_time,
            "timestamp": time.time(),
            "components": results,
            "summary": {
                "healthy_components": len([r for r in results.values() if r["status"] == "healthy"]),
                "warning_components": len([r for r in results.values() if r["status"] == "warning"]),
                "critical_components": len([r for r in results.values() if r["status"] == "critical"]),
                "total_components": len(results)
            },
            "issues": {
                "critical": critical_issues,
                "warnings": warnings
            }
        }
        
        logger.info(f"Health check completed in {total_time:.2f}ms - Status: {overall_status.value}")
        
        return comprehensive_result
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get quick health summary"""
        return {
            "monitoring_active": self.monitoring_active,
            "last_check_time": self.last_check_time,
            "check_interval": self.check_interval,
            "health_history_count": len(self.health_history),
            "torch_available": TORCH_AVAILABLE,
            "gpu_system_available": GPU_SYSTEM_AVAILABLE
        }


# Global health monitor instance
_health_monitor: Optional[ProductionHealthMonitor] = None

def get_health_monitor() -> ProductionHealthMonitor:
    """Get or create global health monitor"""
    global _health_monitor
    if _health_monitor is None:
        _health_monitor = ProductionHealthMonitor()
    return _health_monitor

async def quick_health_check() -> Dict[str, Any]:
    """Quick health check for API endpoints"""
    monitor = get_health_monitor()
    return await monitor.run_comprehensive_health_check()
