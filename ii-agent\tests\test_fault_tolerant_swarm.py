#!/usr/bin/env python3
import sys
import os
import asyncio
import pytest

THIS_DIR = os.path.dirname(os.path.abspath(__file__))
II_AGENT_ROOT = os.path.abspath(os.path.join(THIS_DIR, ".."))
if II_AGENT_ROOT not in sys.path:
    sys.path.insert(0, II_AGENT_ROOT)

from ii_agent_integration.swarms.swarms_integration import (
    SwarmsFrameworkIntegration,
    SwarmsCoordinationPattern,
)


class DummyTask:
    def __init__(self, task_id, content, agents, deterministic_agent_fn=None, replicas=2, retries=1, quorum=1):
        self.task_id = task_id
        self.content = content
        self.agents = agents
        self.deterministic_agent_fn = deterministic_agent_fn
        self.replicas = replicas
        self.retries = retries
        self.quorum = quorum


@pytest.mark.asyncio
async def test_fault_tolerant_quorum_success():
    # First attempt (a1) fails once then succeeds, a2 succeeds immediately; quorum=1
    attempts = {"a1": 0, "a2": 0}
    async def det_fn(agent, task):
        attempts[agent] += 1
        if agent == "a1" and attempts[agent] == 1:
            return {"success": False, "error": "transient"}
        return {"success": True, "output": f"ok-{agent}", "confidence": 0.8, "latency_ms": 5.0}

    task = DummyTask(
        task_id="t-ft-quorum",
        content="do work",
        agents=["a1", "a2"],
        deterministic_agent_fn=det_fn,
        replicas=2,
        retries=1,
        quorum=1,
    )

    integration = SwarmsFrameworkIntegration(coordination_hub=None)
    result = await integration.execute_swarms_coordination(task, SwarmsCoordinationPattern.FAULT_TOLERANT)

    assert result.success is True
    md = result.metadata or {}
    assert md.get("quorum") == 1
    assert md.get("successes") >= 1
    assert md.get("failures") >= 0
    assert md.get("primary_agent") in {"a1", "a2"}


@pytest.mark.asyncio
async def test_fault_tolerant_quorum_failure():
    # All attempts fail despite retries; quorum=2 cannot be met
    async def det_fn(agent, task):
        return {"success": False, "error": "permanent"}

    task = DummyTask(
        task_id="t-ft-fail",
        content="do work",
        agents=["b1", "b2"],
        deterministic_agent_fn=det_fn,
        replicas=2,
        retries=1,
        quorum=2,
    )

    integration = SwarmsFrameworkIntegration(coordination_hub=None)
    result = await integration.execute_swarms_coordination(task, SwarmsCoordinationPattern.FAULT_TOLERANT)

    assert result.success is False
    md = result.metadata or {}
    assert md.get("quorum") == 2
    assert md.get("successes") == 0
    assert md.get("failures") >= 1
