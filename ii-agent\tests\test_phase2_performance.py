"""
Phase 2 WASM Integration Performance Test
Tests the working integration and benchmarks WASM acceleration vs Phase 1 vs Fallback
"""

import sys
import os
import time
import statistics
from typing import List, Dict

# Add the parent directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from simple_neural import SimpleNeuralAgent, PHASE1_AVAILABLE, PHASE2_AVAILABLE
    print("✅ Successfully imported SimpleNeuralAgent")
except ImportError as e:
    print(f"❌ Import failed: {e}")
    sys.exit(1)

def benchmark_phase(agent: SimpleNeuralAgent, test_messages: List[str], phase_name: str) -> Dict:
    """Benchmark a specific phase configuration."""
    print(f"\n🏃‍♂️ Benchmarking {phase_name}...")
    
    times = []
    results = []
    
    for i, message in enumerate(test_messages):
        start_time = time.perf_counter()
        result = agent.get_neural_analysis(message)
        end_time = time.perf_counter()
        
        processing_time = (end_time - start_time) * 1000  # ms
        times.append(processing_time)
        results.append(result)
        
        print(f"  Test {i+1}: {processing_time:.2f}ms - {result['agent_type']} ({result['confidence']:.3f})")
    
    avg_time = statistics.mean(times)
    min_time = min(times)
    max_time = max(times)
    
    return {
        "phase": phase_name,
        "avg_time_ms": avg_time,
        "min_time_ms": min_time,
        "max_time_ms": max_time,
        "times": times,
        "results": results,
        "active_phase": agent.active_phase
    }

def main():
    """Run comprehensive Phase 2 integration and performance tests."""
    print("🧪 Phase 2 WASM Integration & Performance Test")
    print("=" * 50)
    
    print(f"Phase 1 Available: {PHASE1_AVAILABLE}")
    print(f"Phase 2 Available: {PHASE2_AVAILABLE}")
    
    # Test messages for different agent types
    test_messages = [
        "I need help implementing a machine learning algorithm in Python",
        "Research quantum computing applications in cryptography",
        "Analyze this dataset and create visualizations",
        "Debug this error in my React application",
        "Design a user-friendly interface for mobile app",
        "Set up CI/CD pipeline for deployment",
        "Perform security audit on web application",
        "Write unit tests for this function"
    ]
    
    benchmarks = []
    
    # Test Phase 2: WASM Acceleration (if available)
    if PHASE2_AVAILABLE:
        agent_wasm = SimpleNeuralAgent(enable_neural=True, enable_wasm=True)
        print(f"\n🚀 Phase 2 Agent Active Phase: {agent_wasm.active_phase}")
        
        if agent_wasm.active_phase == "phase2_wasm":
            wasm_bench = benchmark_phase(agent_wasm, test_messages, "Phase 2 WASM")
            benchmarks.append(wasm_bench)
        else:
            print("⚠️ Phase 2 WASM not activated, falling back to Phase 1")
    
    # Test Phase 1: Real Neural Networks (if available)
    if PHASE1_AVAILABLE:
        agent_phase1 = SimpleNeuralAgent(enable_neural=True, enable_wasm=False)
        print(f"\n🧠 Phase 1 Agent Active Phase: {agent_phase1.active_phase}")
        
        if agent_phase1.active_phase == "phase1_neural":
            phase1_bench = benchmark_phase(agent_phase1, test_messages, "Phase 1 Neural")
            benchmarks.append(phase1_bench)
        else:
            print("⚠️ Phase 1 Neural not activated, falling back")
    
    # Test Fallback: SimpleNeuralCore
    agent_fallback = SimpleNeuralAgent(enable_neural=False)
    print(f"\n🤖 Fallback Agent Active Phase: {agent_fallback.active_phase}")
    fallback_bench = benchmark_phase(agent_fallback, test_messages, "Fallback Core")
    benchmarks.append(fallback_bench)
    
    # Performance Analysis
    print("\n" + "=" * 60)
    print("📊 PERFORMANCE ANALYSIS")
    print("=" * 60)
    
    for bench in benchmarks:
        print(f"\n{bench['phase']} ({bench['active_phase']}):")
        print(f"  Average: {bench['avg_time_ms']:.2f}ms")
        print(f"  Range: {bench['min_time_ms']:.2f}ms - {bench['max_time_ms']:.2f}ms")
    
    # Calculate speedup ratios
    if len(benchmarks) >= 2:
        print(f"\n🏁 SPEEDUP ANALYSIS:")
        fallback_time = fallback_bench['avg_time_ms']
        
        for bench in benchmarks:
            if bench['phase'] != "Fallback Core":
                speedup = fallback_time / bench['avg_time_ms']
                print(f"  {bench['phase']}: {speedup:.2f}x faster than Fallback")
        
        # WASM vs Phase 1 comparison
        wasm_bench = next((b for b in benchmarks if "WASM" in b['phase']), None)
        phase1_bench = next((b for b in benchmarks if "Phase 1" in b['phase']), None)
        
        if wasm_bench and phase1_bench:
            wasm_speedup = phase1_bench['avg_time_ms'] / wasm_bench['avg_time_ms']
            print(f"  WASM vs Phase 1: {wasm_speedup:.2f}x speedup")
            
            if wasm_speedup >= 2.0:
                print("  ✅ WASM achieving expected 2-4x speedup!")
            else:
                print(f"  ⚠️ WASM speedup below expected 2x ({wasm_speedup:.2f}x)")
    
    # Test agent selection accuracy
    print(f"\n🎯 AGENT SELECTION ACCURACY:")
    expected_agents = ["coder", "researcher", "data_scientist", "coder", "designer", "devops", "security", "tester"]
    
    for bench in benchmarks:
        correct = 0
        for i, result in enumerate(bench['results']):
            if result['agent_type'] == expected_agents[i]:
                correct += 1
        
        accuracy = (correct / len(expected_agents)) * 100
        print(f"  {bench['phase']}: {accuracy:.1f}% ({correct}/{len(expected_agents)} correct)")
    
    # Performance info test
    if benchmarks:
        agent = SimpleNeuralAgent()
        perf_info = agent.get_performance_info()
        print(f"\n📋 SYSTEM CAPABILITIES:")
        print(f"  Active Phase: {perf_info['active_phase']}")
        print(f"  Phase 2 WASM Available: {perf_info['capabilities']['phase2_wasm_available']}")
        print(f"  Phase 1 Neural Available: {perf_info['capabilities']['phase1_neural_available']}")
        print(f"  WASM Enabled: {perf_info['capabilities']['wasm_enabled']}")
        print(f"  Neural Enabled: {perf_info['capabilities']['neural_enabled']}")
    
    print(f"\n✅ Phase 2 Integration Test Complete!")
    print(f"🚀 Total phases tested: {len(benchmarks)}")
    
    # Return summary for programmatic access
    return {
        "benchmarks": benchmarks,
        "phase1_available": PHASE1_AVAILABLE,
        "phase2_available": PHASE2_AVAILABLE,
        "test_passed": True
    }

if __name__ == "__main__":
    try:
        results = main()
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
