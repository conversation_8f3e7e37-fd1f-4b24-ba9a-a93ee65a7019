#!/usr/bin/env python3
"""
Quick test to verify the WebSocket NoneType fix
"""

import asyncio
import json
import websockets
import logging
import signal
import sys

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_websocket_with_timeout():
    """Test WebSocket connection with timeout"""
    try:
        logger.info("🔌 Attempting WebSocket connection...")
        
        # Connect with shorter timeout
        async with websockets.connect("ws://localhost:8000/ws", timeout=5) as websocket:
            logger.info("✅ WebSocket connected successfully!")
            
            # Send a simple test message
            test_message = {
                "type": "ping",
                "content": {"message": "test connection"}
            }
            
            await websocket.send(json.dumps(test_message))
            logger.info("📤 Sent test message")
            
            # Wait for response with timeout
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                response_data = json.loads(response)
                logger.info(f"📥 Received response: {response_data}")
                return True
            except asyncio.TimeoutError:
                logger.warning("⚠️ No response received within timeout")
                return False
                
    except Exception as e:
        logger.error(f"❌ WebSocket connection failed: {e}")
        return False

def signal_handler(sig, frame):
    logger.info("Test interrupted by user")
    sys.exit(0)

if __name__ == "__main__":
    # Handle Ctrl+C gracefully
    signal.signal(signal.SIGINT, signal_handler)
    
    logger.info("🚀 Starting WebSocket fix validation test...")
    
    try:
        result = asyncio.run(test_websocket_with_timeout())
        if result:
            print("✅ WebSocket test PASSED - Fix appears to be working!")
            sys.exit(0)
        else:
            print("❌ WebSocket test FAILED - Check backend logs")
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("Test interrupted")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Test failed with exception: {e}")
        sys.exit(1)
