"use client";

import { useState, useEffect, useMemo } from "react";
import { X, ChevronDown, RotateCcw, Settings2 } from "lucide-react";
import Cookies from "js-cookie";
import { motion } from "framer-motion";

import { Button } from "./ui/button";
import { Switch } from "./ui/switch";
import { Label } from "./ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { Tooltip, TooltipTrigger, TooltipContent } from "./ui/tooltip";
import { ToolSettings } from "@/typings/agent";
import { useAppContext } from "@/context/app-context";
import ApiKeysDialog from "./api-keys-dialog";

interface SettingsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onOpen: () => void;
}

const SettingsDrawer = ({ isOpen, onClose, onOpen }: SettingsDrawerProps) => {
  const { state, dispatch } = useAppContext();
  const [toolsExpanded, setToolsExpanded] = useState(true);
  const [reasoningExpanded, setReasoningExpanded] = useState(true);
  const [isApiKeysDialogOpen, setIsApiKeysDialogOpen] = useState(false);
  const [isModelConfigOpen, setIsModelConfigOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  const isClaudeModel = useMemo(
    () => state.selectedModel?.toLowerCase().includes("claude") || false,
    [state.selectedModel]
  );

  const handleToolToggle = (tool: keyof ToolSettings) => {
    dispatch({
      type: "SET_TOOL_SETTINGS",
      payload: {
        ...state.toolSettings,
        [tool]: !state.toolSettings[tool],
      },
    });
  };

  const resetSettings = () => {
    dispatch({
      type: "SET_TOOL_SETTINGS",
      payload: {
        deep_research: false,
        pdf: true,
        media_generation: true,
        audio_generation: true,
        browser: true,
        thinking_tokens: 10000,
        enable_reviewer: false,
        enable_neural: true,  // Default to enabled
      },
    });
    dispatch({
      type: "SET_SELECTED_MODEL",
      payload: state.availableModels?.[0],
    });
  };

  const handleReasoningEffortChange = (effort: string) => {
    dispatch({
      type: "SET_TOOL_SETTINGS",
      payload: {
        ...state.toolSettings,
        thinking_tokens: effort === "high" ? 10000 : 0,
      },
    });
  };

  useEffect(() => {
    if (state.selectedModel) {
      Cookies.set("selected_model", state.selectedModel, {
        expires: 365, // 1 year
        sameSite: "strict",
        secure: window.location.protocol === "https:",
      });

      // Reset thinking_tokens to 0 for non-Claude models
      if (!isClaudeModel && state.toolSettings.thinking_tokens > 0) {
        dispatch({
          type: "SET_TOOL_SETTINGS",
          payload: { ...state.toolSettings, thinking_tokens: 0 },
        });
      }
    }
  }, [state.selectedModel, isClaudeModel, state.toolSettings, dispatch]);

  useEffect(() => {
    if (state.toolSettings) {
      Cookies.set("tool_settings", JSON.stringify(state.toolSettings), {
        expires: 365, // 1 year
        sameSite: "strict",
        secure: window.location.protocol === "https:",
      });

      // Reset thinking_tokens to 0 for non-Claude models
      if (!isClaudeModel && state.toolSettings.thinking_tokens > 0) {
        dispatch({
          type: "SET_TOOL_SETTINGS",
          payload: { ...state.toolSettings, thinking_tokens: 0 },
        });
      }
    }
  }, [isClaudeModel, state.toolSettings, dispatch]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  return (
    <>
      {isOpen && (
        <div className="fixed inset-0 bg-black/50 z-40" onClick={onClose} />
      )}
      <motion.div
        className={`fixed top-0 right-0 h-full ${
          isOpen ? "w-[400px]" : "w-0"
        } bg-[#1e1f23] z-50 shadow-xl overflow-auto`}
        initial={{ x: "100%" }}
        animate={{ x: isOpen ? 0 : "100%" }}
        transition={{ type: "spring", damping: 30, stiffness: 300 }}
      >
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-white">Run settings</h2>
            <div className="flex items-center gap-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-full hover:bg-gray-700/50"
                    onClick={resetSettings}
                  >
                    <RotateCcw className="size-5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Reset Default Settings</TooltipContent>
              </Tooltip>
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full hover:bg-gray-700/50"
                onClick={onClose}
              >
                <X className="size-5" />
              </Button>
            </div>
          </div>

          <div className="space-y-6">
            {/* Model selector */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Select
                  value={state.selectedModel || ""}
                  onValueChange={(model) =>
                    dispatch({ type: "SET_SELECTED_MODEL", payload: model })
                  }
                >
                  <SelectTrigger className="w-full bg-[#35363a] border-[#ffffff0f]">
                    <SelectValue placeholder="Select model" />
                  </SelectTrigger>
                  <SelectContent className="bg-[#35363a] border-[#ffffff0f]">
                    {(state.availableModels || []).map((model) => (
                      <SelectItem key={model} value={model}>
                        {model}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-10 w-10 bg-[#35363a] border-[#ffffff0f]"
                      onClick={() => setIsModelConfigOpen(true)}
                    >
                      <Settings2 className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Configure Models</TooltipContent>
                </Tooltip>
              </div>
            </div>

           <p className="text-xs text-gray-400 mt-1">
             Tip: Default model is <span className="text-gray-200">gemini-2.0-flash</span>. You can change providers and models via “Configure Models”, and set API keys in the Settings → API Keys dialog.
           </p>

            {/* Reasoning Effort section - only for Claude models */}
            {isClaudeModel && (
              <div className="space-y-4 pt-4 border-t border-gray-700">
                <div
                  className="flex justify-between items-center cursor-pointer"
                  onClick={() => setReasoningExpanded(!reasoningExpanded)}
                >
                  <h3 className="text-lg font-medium text-white">
                    Reasoning Effort
                  </h3>
                  <ChevronDown
                    className={`size-5 transition-transform ${
                      reasoningExpanded ? "rotate-180" : ""
                    }`}
                  />
                </div>

                {reasoningExpanded && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label
                        htmlFor="reasoning-effort"
                        className="text-gray-300"
                      >
                        Effort Level
                      </Label>
                      <p className="text-xs text-gray-400 mb-2">
                        Controls how much effort the model spends on reasoning
                        before responding
                      </p>
                      <Select
                        value={
                          state.toolSettings?.thinking_tokens > 0
                            ? "high"
                            : "standard"
                        }
                        onValueChange={handleReasoningEffortChange}
                      >
                        <SelectTrigger className="w-full bg-[#35363a] border-[#ffffff0f]">
                          <SelectValue placeholder="Select effort level" />
                        </SelectTrigger>
                        <SelectContent className="bg-[#35363a] border-[#ffffff0f]">
                          <SelectItem value="standard">Standard</SelectItem>
                          <SelectItem value="high">High-effort</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Tools section */}
            <div className="space-y-4 pt-4 border-t border-gray-700">
              <div
                className="flex justify-between items-center cursor-pointer"
                onClick={() => setToolsExpanded(!toolsExpanded)}
              >
                <h3 className="text-lg font-medium text-white">Tools</h3>
                <ChevronDown
                  className={`size-5 transition-transform ${
                    toolsExpanded ? "rotate-180" : ""
                  }`}
                />
              </div>

              {toolsExpanded && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label htmlFor="deep-research" className="text-gray-300">
                        Deep Research
                      </Label>
                      <p className="text-xs text-gray-400">
                        Enable in-depth research capabilities
                      </p>
                    </div>
                    <Switch
                      id="deep-research"
                      checked={state.toolSettings.deep_research}
                      onCheckedChange={() => handleToolToggle("deep_research")}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label htmlFor="pdf" className="text-gray-300">
                        PDF Processing
                      </Label>
                      <p className="text-xs text-gray-400">
                        Extract and analyze PDF documents
                      </p>
                    </div>
                    <Switch
                      id="pdf"
                      checked={state.toolSettings.pdf}
                      onCheckedChange={() => handleToolToggle("pdf")}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label
                        htmlFor="media-generation"
                        className="text-gray-300"
                      >
                        Media Generation
                      </Label>
                      <p className="text-xs text-gray-400">
                        Generate images and videos
                      </p>
                    </div>
                    <Switch
                      id="media-generation"
                      checked={state.toolSettings.media_generation}
                      onCheckedChange={() =>
                        handleToolToggle("media_generation")
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label
                        htmlFor="audio-generation"
                        className="text-gray-300"
                      >
                        Audio Generation
                      </Label>
                      <p className="text-xs text-gray-400">
                        Generate and process audio content
                      </p>
                    </div>
                    <Switch
                      id="audio-generation"
                      checked={state.toolSettings.audio_generation}
                      onCheckedChange={() =>
                        handleToolToggle("audio_generation")
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label htmlFor="browser" className="text-gray-300">
                        Browser
                      </Label>
                      <p className="text-xs text-gray-400">
                        Enable web browsing capabilities
                      </p>
                    </div>
                    <Switch
                      id="browser"
                      checked={state.toolSettings.browser}
                      onCheckedChange={() => handleToolToggle("browser")}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label
                        htmlFor="enable_neural"
                        className="text-gray-300"
                      >
                        🧠 Neural Intelligence
                      </Label>
                      <p className="text-xs text-gray-400">
                        Enable RUV-FANN neural swarm with 84.8% accuracy
                      </p>
                    </div>
                    <Switch
                      id="enable_neural"
                      checked={state.toolSettings.enable_neural}
                      onCheckedChange={() =>
                        handleToolToggle("enable_neural")
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Label
                        htmlFor="enable_reviewer"
                        className="text-gray-300"
                      >
                        Review Agent
                      </Label>
                      <p className="text-xs text-gray-400">
                        Enable reviewer agent to analyze and improve outputs
                      </p>
                    </div>
                    <Switch
                      id="audio-enable_reviewer"
                      checked={state.toolSettings.enable_reviewer}
                      onCheckedChange={() =>
                        handleToolToggle("enable_reviewer")
                      }
                    />
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-4 pt-4 border-t border-gray-700">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-white">API Keys</h3>
                <Button
                  variant="outline"
                  onClick={() => setIsApiKeysDialogOpen(true)}
                  className="border-[#ffffff0f]"
                >
                  Configure
                </Button>
              </div>
              <p className="text-xs text-gray-400">
                Configure API keys for various services like OpenAI, Anthropic,
                and search providers.
              </p>
            </div>

            {/* API Keys Dialog */}
            <ApiKeysDialog
              isOpen={isApiKeysDialogOpen || isModelConfigOpen}
              onClose={() => {
                setIsApiKeysDialogOpen(false);
                setIsModelConfigOpen(false);
              }}
              onOpen={() => {
                setIsApiKeysDialogOpen(true);
                onOpen();
              }}
              initialTab={isModelConfigOpen ? "llm-config" : "search-config"}
            />
          </div>
        </div>
      </motion.div>
    </>
  );
};

export default SettingsDrawer;
