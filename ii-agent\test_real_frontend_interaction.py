#!/usr/bin/env python3
"""
Real Frontend Interaction Test
Tests the actual multi-agent system through the frontend WebSocket interface.
"""

import asyncio
import json
import logging
import websockets
import uuid
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealFrontendTest:
    """Test the real multi-agent system through frontend interface"""
    
    def __init__(self):
        self.device_id = str(uuid.uuid4())
        self.ws_url = f"ws://localhost:8000/ws?device_id={self.device_id}"
        self.results = {}
    
    async def test_real_multi_agent_system(self):
        """Test the real multi-agent system end-to-end"""
        logger.info("🚀 Testing REAL multi-agent system through frontend...")
        
        try:
            async with websockets.connect(self.ws_url, timeout=15) as websocket:
                logger.info("✅ Connected to WebSocket")
                
                # Send a real multi-agent task
                task_message = {
                    "type": "user_message",
                    "content": "Please coordinate multiple agents to analyze this request: Create a simple Python function that calculates the factorial of a number. Use multiple agents for planning, coding, and reviewing.",
                    "timestamp": int(time.time() * 1000)
                }
                
                logger.info("📤 Sending multi-agent task...")
                await websocket.send(json.dumps(task_message))
                
                # Listen for responses
                responses = []
                start_time = time.time()
                agent_activities = []
                
                while time.time() - start_time < 60:  # 60 second timeout
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        response_data = json.loads(response)
                        responses.append(response_data)
                        
                        # Log different types of responses
                        msg_type = response_data.get("type", "unknown")
                        
                        if msg_type == "agent_thinking":
                            agent_id = response_data.get("agent_id", "unknown")
                            thinking = response_data.get("content", "")
                            logger.info(f"🤔 Agent {agent_id} thinking: {thinking[:100]}...")
                            agent_activities.append(f"Agent {agent_id} thinking")
                        
                        elif msg_type == "agent_response":
                            agent_id = response_data.get("agent_id", "unknown")
                            content = response_data.get("content", "")
                            logger.info(f"🤖 Agent {agent_id} response: {content[:100]}...")
                            agent_activities.append(f"Agent {agent_id} responded")
                        
                        elif msg_type == "coordination_update":
                            status = response_data.get("status", "unknown")
                            logger.info(f"🔄 Coordination update: {status}")
                            agent_activities.append(f"Coordination: {status}")
                        
                        elif msg_type == "final_response":
                            content = response_data.get("content", "")
                            logger.info(f"✅ Final response received: {content[:100]}...")
                            self.results["final_response"] = "✅ RECEIVED"
                            self.results["response_length"] = len(content)
                            break
                        
                        elif msg_type == "error":
                            error_msg = response_data.get("content", "Unknown error")
                            logger.error(f"❌ Error: {error_msg}")
                            self.results["error"] = error_msg
                            break
                        
                    except asyncio.TimeoutError:
                        continue
                    except json.JSONDecodeError as e:
                        logger.warning(f"⚠️ JSON decode error: {e}")
                        continue
                
                # Analyze results
                self.results["total_responses"] = len(responses)
                self.results["agent_activities"] = len(agent_activities)
                self.results["execution_time"] = time.time() - start_time
                
                if len(agent_activities) > 0:
                    self.results["multi_agent_coordination"] = "✅ WORKING"
                else:
                    self.results["multi_agent_coordination"] = "❌ NO_COORDINATION"
                
                if "final_response" in self.results:
                    self.results["task_completion"] = "✅ COMPLETED"
                else:
                    self.results["task_completion"] = "❌ INCOMPLETE"
                
                logger.info(f"📊 Test completed: {len(responses)} responses, {len(agent_activities)} agent activities")
                
        except Exception as e:
            logger.error(f"❌ Test failed: {e}")
            self.results["connection_error"] = str(e)
    
    def generate_report(self):
        """Generate test report"""
        print("\n" + "="*70)
        print("🧪 REAL FRONTEND MULTI-AGENT TEST REPORT")
        print("="*70)
        
        for key, value in self.results.items():
            print(f"   {key}: {value}")
        
        # Determine overall status
        success_indicators = [
            self.results.get("multi_agent_coordination") == "✅ WORKING",
            self.results.get("task_completion") == "✅ COMPLETED",
            self.results.get("agent_activities", 0) > 0,
            "connection_error" not in self.results
        ]
        
        success_count = sum(success_indicators)
        success_rate = (success_count / len(success_indicators)) * 100
        
        print(f"\n🎯 OVERALL ASSESSMENT:")
        print(f"   Success Rate: {success_count}/{len(success_indicators)} ({success_rate:.1f}%)")
        
        if success_rate >= 75:
            print("   ✅ MULTI-AGENT SYSTEM IS TRULY WORKING!")
            overall_status = "working"
        elif success_rate >= 50:
            print("   ⚠️ MULTI-AGENT SYSTEM PARTIALLY WORKING")
            overall_status = "partial"
        else:
            print("   ❌ MULTI-AGENT SYSTEM NOT WORKING PROPERLY")
            overall_status = "not_working"
        
        print("="*70)
        return overall_status

async def main():
    """Run the real frontend test"""
    tester = RealFrontendTest()
    await tester.test_real_multi_agent_system()
    status = tester.generate_report()
    
    # Return exit code based on results
    return 0 if status == "working" else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
