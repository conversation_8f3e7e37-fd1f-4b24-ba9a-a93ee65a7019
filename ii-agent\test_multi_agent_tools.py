#!/usr/bin/env python3
"""Test multi-agent coordination with tool execution via WebSocket."""

import asyncio
import json
import websockets
import sys
from pathlib import Path

async def test_multi_agent_tools():
    """Test multi-agent system with tool execution via WebSocket."""
    
    print("🔌 Connecting to ii-agent WebSocket server...")
    
    try:
        uri = "ws://localhost:8000/ws"
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to WebSocket server")
            
            # Send initial handshake/auth if needed
            print("\n📤 Sending task with tool requirement...")
            
            # Create a task that should trigger tool usage
            task_message = {
                "type": "query",
                "content": {
                    "message": "List the files in the current directory. Use a file listing tool if available.",
                    "task_id": "test_tool_coordination",
                    "timestamp": "2025-08-30T20:55:00Z"
                }
            }
            
            await websocket.send(json.dumps(task_message))
            print(f"📤 Sent: {task_message}")
            
            # Collect responses for analysis
            responses = []
            tool_calls_detected = []
            error_messages = []
            
            print("\n📥 Listening for responses...")
            
            try:
                # Listen for up to 30 seconds or until completion
                timeout_task = asyncio.create_task(asyncio.sleep(30.0))
                message_task = asyncio.create_task(websocket.recv())
                
                while True:
                    done, pending = await asyncio.wait(
                        [timeout_task, message_task],
                        return_when=asyncio.FIRST_COMPLETED
                    )
                    
                    if timeout_task in done:
                        print("⏰ Test timeout reached")
                        break
                    
                    if message_task in done:
                        message = message_task.result()
                        
                        try:
                            data = json.loads(message)
                            responses.append(data)
                            
                            print(f"📥 Received: {data.get('type', 'unknown')} - {str(data)[:200]}...")
                            
                            # Check for tool-related activity
                            if "tool" in str(data).lower():
                                tool_calls_detected.append(data)
                                print(f"🔧 TOOL ACTIVITY: {data}")
                            
                            # Check for error patterns we're trying to eliminate
                            message_content = str(data).lower()
                            if "tool with name" in message_content and "not found" in message_content:
                                error_messages.append(data)
                                print(f"❌ TOOL ERROR: {data}")
                            
                            # Check for completion signals
                            if data.get("type") == "task_complete" or data.get("type") == "final_response":
                                print("✅ Task completion detected")
                                break
                                
                        except json.JSONDecodeError:
                            print(f"📥 Raw message: {message}")
                        
                        # Start listening for next message
                        message_task = asyncio.create_task(websocket.recv())
                    
                # Cancel any pending tasks
                for task in pending:
                    task.cancel()
                        
            except Exception as listen_error:
                print(f"❌ Listen error: {listen_error}")
            
            # Analysis
            print(f"\n📊 ANALYSIS:")
            print(f"   Total responses: {len(responses)}")
            print(f"   Tool calls detected: {len(tool_calls_detected)}")
            print(f"   Tool errors: {len(error_messages)}")
            
            # Check success criteria
            success = True
            
            if error_messages:
                print(f"❌ FAILED: Found {len(error_messages)} 'Tool with name X not found' errors")
                for err in error_messages:
                    print(f"    Error: {err}")
                success = False
            else:
                print("✅ SUCCESS: No 'Tool with name X not found' errors detected")
            
            if tool_calls_detected:
                print(f"✅ SUCCESS: Detected {len(tool_calls_detected)} tool-related activities")
                for tool in tool_calls_detected:
                    print(f"    Tool activity: {tool}")
            else:
                print("⚠️  WARNING: No explicit tool calls detected (may be expected if no tools available)")
            
            # Check if any responses contain evidence of successful tool execution
            gemini_responses = [r for r in responses if "gemini" in str(r).lower()]
            if gemini_responses:
                print(f"✅ SUCCESS: Found {len(gemini_responses)} Gemini-related responses")
            
            return success
            
    except Exception as e:
        print(f"❌ Connection/test failed: {e}")
        return False

async def main():
    print("🧪 Testing multi-agent coordination with tool execution")
    print("=" * 60)
    
    success = await test_multi_agent_tools()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 TEST PASSED: Multi-agent tool coordination working!")
        sys.exit(0)
    else:
        print("💥 TEST FAILED: Issues detected in multi-agent tool coordination")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
