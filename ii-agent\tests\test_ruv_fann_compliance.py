"""
Test Suite for RUV-FANN Compliant Neural Agent
==============================================

Tests the proper fallback hierarchy implementation according to ADR-004:
- Primary Path: WASM acceleration (<75ms)
- Stage 1 Fallback: Simpler In-WASM model
- Stage 2 Fallback: MCP Server offload  
- Stage 3 Fallback: Explicit failure with error codes

Following GitHub TDD principles with NO MOCKS and brutal honesty.
"""

import pytest
import time
import sys
import os

# Add the neural module to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from simple_neural_ruv_fann import (
    RuvFANNNeuralAgent, 
    BaseRuvFANNError,
    NeuroDivergentError,
    RuvSwarmError,
    CircuitBreakerState,
    MCPCircuitBreaker,
    WASM_PERFORMANCE_THRESHOLD_MS,
    MCP_EXTENDED_TIMEOUT_MS,
    WASM_AVAILABLE,
    SIMPLE_MODELS_AVAILABLE,
    MCP_SERVER_AVAILABLE,
    NEURAL_AVAILABLE
)

class TestRuvFANNCompliance:
    """Test RUV-FANN architecture compliance"""
    
    def test_ruv_fann_error_hierarchy(self):
        """Test RUV-FANN error hierarchy (ADR-006)"""
        # Test base error
        base_error = BaseRuvFANNError("Test error", details={"code": "TEST"})
        assert str(base_error) == "Test error"
        assert base_error.details["code"] == "TEST"
        
        # Test WASM error
        wasm_error = NeuroDivergentError("WASM failed")
        assert isinstance(wasm_error, BaseRuvFANNError)
        
        # Test MCP error
        mcp_error = RuvSwarmError("MCP failed")
        assert isinstance(mcp_error, BaseRuvFANNError)
    
    def test_performance_constraints(self):
        """Test C-1 performance constraint compliance"""
        assert WASM_PERFORMANCE_THRESHOLD_MS == 75  # C-1 constraint
        assert MCP_EXTENDED_TIMEOUT_MS == 5000  # Extended timeout for ruv-swarm
    
    def test_circuit_breaker_states(self):
        """Test MCP circuit breaker (ADR-003)"""
        cb = MCPCircuitBreaker(failure_threshold=3, recovery_timeout=1)
        
        # Initial state should be CLOSED
        assert cb.state == CircuitBreakerState.CLOSED
        assert cb.can_execute()
        
        # Record failures to open circuit
        for _ in range(3):
            cb.record_failure()
        
        assert cb.state == CircuitBreakerState.OPEN
        assert not cb.can_execute()
        
        # Wait for recovery timeout
        time.sleep(1.1)
        assert cb.can_execute()  # Should transition to HALF_OPEN
        
        # Record success to close circuit
        cb.record_success()
        assert cb.state == CircuitBreakerState.CLOSED

class TestRuvFANNFallbackHierarchy:
    """Test the proper fallback hierarchy implementation"""
    
    def test_agent_initialization(self):
        """Test RUV-FANN agent initializes correctly"""
        agent = RuvFANNNeuralAgent()
        
        assert agent.fallback_core is not None
        assert agent.mcp_circuit_breaker is not None
        assert agent.performance_metrics is not None
        assert "wasm_calls" in agent.performance_metrics
        assert "mcp_calls" in agent.performance_metrics
    
    def test_availability_hierarchy(self):
        """Test system reports correct availability"""
        agent = RuvFANNNeuralAgent()
        perf_info = agent.get_performance_info()
        
        availability = perf_info["availability"]
        assert "wasm_available" in availability
        assert "simple_models_available" in availability
        assert "mcp_server_available" in availability
        assert "neural_system_available" in availability
        
        # At least one should be available for the system to work
        assert availability["neural_system_available"] == NEURAL_AVAILABLE
    
    def test_ruv_fann_architecture_compliance(self):
        """Test compliance with RUV-FANN architecture"""
        agent = RuvFANNNeuralAgent()
        perf_info = agent.get_performance_info()
        
        compliance = perf_info["ruv_fann_compliance"]
        assert compliance["architecture_version"] == "ADR-004"
        assert compliance["fallback_hierarchy_active"] is True
        assert compliance["primary_path"] == "wasm_acceleration"
        assert "current_active_path" in compliance

class TestRuvFANNAnalysis:
    """Test RUV-FANN neural analysis functionality"""
    
    def test_basic_analysis(self):
        """Test basic neural analysis works"""
        agent = RuvFANNNeuralAgent()
        
        test_input = "Help me debug this Python function"
        result = agent.analyze_input(test_input)
        
        # Check required RUV-FANN fields
        assert "ruv_fann_path" in result
        assert "execution_time_ms" in result
        assert "performance_compliant" in result
        assert "fallback_stage" in result
        
        # Check execution time is recorded
        assert isinstance(result["execution_time_ms"], (int, float))
        assert result["execution_time_ms"] >= 0
    
    def test_fallback_stage_identification(self):
        """Test fallback stages are correctly identified"""
        agent = RuvFANNNeuralAgent()
        
        result = agent.analyze_input("Test analysis")
        
        # Should identify which stage was used
        valid_stages = ["primary", "stage1", "stage2", "stage3"]
        assert result["fallback_stage"] in valid_stages
        
        valid_paths = ["primary_wasm", "stage1_simple", "stage2_mcp", "stage3_fallback"]
        assert result["ruv_fann_path"] in valid_paths
    
    def test_performance_tracking(self):
        """Test performance metrics are tracked correctly"""
        agent = RuvFANNNeuralAgent()
        
        initial_metrics = agent.performance_metrics.copy()
        
        # Perform analysis
        agent.analyze_input("Test performance tracking")
        
        # Check metrics were updated
        final_metrics = agent.performance_metrics
        
        # At least one counter should have increased
        metrics_changed = any(
            final_metrics[key] > initial_metrics[key] 
            for key in initial_metrics.keys()
        )
        assert metrics_changed
    
    def test_error_handling_with_context(self):
        """Test error handling includes proper context"""
        agent = RuvFANNNeuralAgent()
        
        # This should work but may hit fallback paths
        result = agent.analyze_input("Complex analysis that might trigger fallbacks")
        
        # Even in fallback, should have proper structure
        assert isinstance(result, dict)
        assert "ruv_fann_path" in result
        
        # If system is degraded, should be noted
        if result.get("system_degraded"):
            assert "error_code" in result
            assert "circuit_breaker_state" in result

class TestRuvFANNIntegrationScenarios:
    """Test real-world RUV-FANN integration scenarios"""
    
    def test_coding_task_with_ruv_fann(self):
        """Test coding task through RUV-FANN system"""
        agent = RuvFANNNeuralAgent()
        
        coding_task = """
        I have a performance bottleneck in this Python function:
        
        def slow_function(data):
            result = []
            for item in data:
                if item > 0:
                    result.append(item * 2)
            return result
        
        Can you help optimize it?
        """
        
        result = agent.analyze_input(coding_task)
        
        # Should process successfully through some path
        assert result["execution_time_ms"] > 0
        assert "confidence" in result  # Should have neural analysis
        
        # Performance compliance depends on which path was used
        if result["ruv_fann_path"] == "primary_wasm":
            # Primary path should meet performance constraint
            assert result["execution_time_ms"] <= WASM_PERFORMANCE_THRESHOLD_MS or not result["performance_compliant"]
    
    def test_research_task_with_ruv_fann(self):
        """Test research task through RUV-FANN system"""
        agent = RuvFANNNeuralAgent()
        
        research_task = """
        I need to research the latest developments in neural architecture search
        and automated machine learning. Can you help me find relevant papers
        and summarize the key findings?
        """
        
        result = agent.analyze_input(research_task)
        
        # Should handle research tasks
        assert "agent_type" in result
        assert result["execution_time_ms"] > 0
        
        # Should identify this as appropriate agent type
        agent_type = result.get("agent_type", "").lower()
        assert agent_type in ["researcher", "analyst", "coder", "data_scientist"]
    
    def test_performance_degradation_handling(self):
        """Test system handles performance degradation gracefully"""
        agent = RuvFANNNeuralAgent()
        
        # Multiple rapid requests to test system under load
        results = []
        for i in range(5):
            result = agent.analyze_input(f"Performance test request {i}")
            results.append(result)
        
        # All requests should complete
        assert len(results) == 5
        
        # Performance metrics should be tracked
        perf_info = agent.get_performance_info()
        total_calls = sum(perf_info["performance_metrics"].values())
        assert total_calls >= 5  # At least 5 calls recorded
    
    def test_system_availability_reporting(self):
        """Test system correctly reports availability status"""
        agent = RuvFANNNeuralAgent()
        
        perf_info = agent.get_performance_info()
        
        # Should report which components are available
        availability = perf_info["availability"]
        
        # Check actual availability matches system constants
        assert availability["wasm_available"] == WASM_AVAILABLE
        assert availability["simple_models_available"] == SIMPLE_MODELS_AVAILABLE
        assert availability["mcp_server_available"] == MCP_SERVER_AVAILABLE
        assert availability["neural_system_available"] == NEURAL_AVAILABLE
        
        # If no fallback mechanisms are available, should be noted
        if not NEURAL_AVAILABLE:
            result = agent.analyze_input("Test with no systems available")
            assert result.get("system_degraded") is True
            assert "error_code" in result

def run_ruv_fann_compliance_tests():
    """Run comprehensive RUV-FANN compliance tests"""
    import subprocess
    
    try:
        # Run pytest with detailed output
        result = subprocess.run([
            sys.executable, "-m", "pytest", __file__, "-v", "--tb=short"
        ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        print("=== RUV-FANN COMPLIANCE TEST RESULTS ===")
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        if result.returncode == 0:
            print("✅ ALL RUV-FANN COMPLIANCE TESTS PASSED!")
            print("🚀 System follows ADR-004 fallback hierarchy correctly")
            print("🔧 Circuit breaker and error handling working")
            print("⚡ Performance constraints properly implemented")
        else:
            print("❌ RUV-FANN COMPLIANCE TESTS FAILED")
            print("🔧 System may not follow proper fallback hierarchy")
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Failed to run RUV-FANN compliance tests: {e}")
        return False

if __name__ == "__main__":
    # Run RUV-FANN compliance tests
    success = run_ruv_fann_compliance_tests()
    
    if success:
        print("\n=== RUV-FANN SYSTEM STATUS ===")
        
        # Test actual system
        agent = RuvFANNNeuralAgent()
        
        # Show system configuration
        perf_info = agent.get_performance_info()
        print(f"Architecture: {perf_info['ruv_fann_compliance']['architecture_version']}")
        print(f"Active Path: {perf_info['ruv_fann_compliance']['current_active_path']}")
        print(f"Fallback Hierarchy: {perf_info['ruv_fann_compliance']['fallback_hierarchy_active']}")
        
        # Show availability
        availability = perf_info['availability']
        print(f"\nAvailability:")
        print(f"  WASM (Primary): {availability['wasm_available']}")
        print(f"  Simple Models (Stage 1): {availability['simple_models_available']}")
        print(f"  MCP Server (Stage 2): {availability['mcp_server_available']}")
        print(f"  System Overall: {availability['neural_system_available']}")
        
        # Test analysis
        result = agent.analyze_input("Test RUV-FANN system analysis")
        print(f"\nTest Analysis:")
        print(f"  Path Used: {result['ruv_fann_path']}")
        print(f"  Execution Time: {result['execution_time_ms']:.2f}ms")
        print(f"  Performance Compliant: {result['performance_compliant']}")
        print(f"  Fallback Stage: {result['fallback_stage']}")
        
    sys.exit(0 if success else 1)
