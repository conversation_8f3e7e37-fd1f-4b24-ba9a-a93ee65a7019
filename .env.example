# =============================================================================
# Ph Agent System - Environment Configuration Template
# Copy this file to .env and fill in your actual values
# =============================================================================

# Application Environment
ENVIRONMENT=development
NODE_ENV=development

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_BACKEND_API_URL=http://localhost:8000
NEXT_PUBLIC_BACKEND_WS_URL=ws://localhost:8000/ws
NEXT_PUBLIC_VSCODE_URL=http://127.0.0.1:8080
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# Google Services
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# =============================================================================
# BACKEND SERVICES CONFIGURATION
# =============================================================================

# Database Configuration (PostgreSQL)
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=generate_secure_password_here
DATABASE_NAME=ph_agent_system
DATABASE_MAX_CONNECTIONS=20
DATABASE_MIN_CONNECTIONS=5

# Redis Configuration (Caching & Sessions)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=optional_redis_password
REDIS_DB=0
REDIS_MAX_CONNECTIONS=10

# Security Configuration
JWT_SECRET_KEY=generate_64_character_secret_key_here
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=60
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
ADMIN_PASSWORD=create_secure_admin_password_here

# =============================================================================
# FINANCIAL TRADING CONFIGURATION
# =============================================================================

# Alpaca Trading API - Get from: https://alpaca.markets/
ALPACA_API_KEY=AKXXXXXXXXXXXXXXXXXX
ALPACA_API_SECRET=your_alpaca_secret_key_here
ALPACA_BASE_URL=https://paper-api.alpaca.markets
ALPACA_PAPER_TRADING=true

# Alpha Vantage API - Get from: https://www.alphavantage.co/
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here

# Polygon.io API - Get from: https://polygon.io/
POLYGON_API_KEY=your_polygon_api_key_here

# Trading Risk Management
MAX_DAILY_DRAWDOWN=0.02
MAX_POSITION_SIZE=0.05
TRADING_ENABLED=false

# =============================================================================
# NOTIFICATION SERVICES
# =============================================================================

# Email Configuration (SMTP) - Gmail example
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_gmail_app_password_here
SMTP_FROM_EMAIL=<EMAIL>
SMTP_USE_TLS=true

# Twilio SMS - Get from: https://www.twilio.com/
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_FROM_NUMBER=+**********

# Firebase Push Notifications - Get from: https://console.firebase.google.com/
FIREBASE_SERVER_KEY=AAAAxxxxxxx:APA91bxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
FIREBASE_PROJECT_ID=your_firebase_project_id

# Slack Integration - Get webhook from: https://api.slack.com/
SLACK_WEBHOOK_URL=*****************************************************************************
SLACK_BOT_TOKEN=xoxb-************-************-XXXXXXXXXXXXXXXXXXXXXXXX

# =============================================================================
# DEPLOYMENT & INFRASTRUCTURE
# =============================================================================

# Coolify Deployment Platform - Get from your Coolify instance
COOLIFY_API_KEY=your_coolify_api_key_here
COOLIFY_BASE_URL=https://your-coolify-instance.com

# Docker Registry
DOCKER_REGISTRY_URL=registry.hub.docker.com
DOCKER_REGISTRY_USERNAME=your_docker_username
DOCKER_REGISTRY_PASSWORD=your_docker_password

# =============================================================================
# AI & ML SERVICES
# =============================================================================

# OpenAI API - Get from: https://platform.openai.com/
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=4096

# Anthropic Claude API - Get from: https://console.anthropic.com/
ANTHROPIC_API_KEY=sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# ruv-FANN Configuration
RUV_FANN_MODEL_PATH=./models/ruv_fann
RUV_FANN_WASM_PATH=./wasm/ruv_fann.wasm
RUV_SWARM_MCP_URL=http://localhost:9000

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Application Monitoring
LOG_LEVEL=INFO
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5

# Performance Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# =============================================================================
# EXTERNAL API KEYS & SERVICES
# =============================================================================

# SendGrid Email Service - Get from: https://sendgrid.com/
SENDGRID_API_KEY=SG.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# AWS Services - Get from: https://aws.amazon.com/
AWS_ACCESS_KEY_ID=AKIAXXXXXXXXXXXXXXXX
AWS_SECRET_ACCESS_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# GitHub Integration - Get from: https://github.com/settings/tokens
GITHUB_TOKEN=ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
GITHUB_WEBHOOK_SECRET=your_github_webhook_secret

# =============================================================================
# SAAS PRODUCT DEVELOPMENT
# =============================================================================

# Domain & DNS Configuration
PRIMARY_DOMAIN=yourdomain.com
SUBDOMAIN_PREFIX=app
SSL_CERT_PATH=./certs/ssl.crt
SSL_KEY_PATH=./certs/ssl.key

# Payment Processing - Get from: https://stripe.com/
STRIPE_PUBLISHABLE_KEY=pk_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
STRIPE_SECRET_KEY=sk_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
STRIPE_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Test Database (separate from main DB)
TEST_DATABASE_URL=postgresql://postgres:test@localhost:5433/ph_agent_test

# Feature Flags
ENABLE_TRADING=false
ENABLE_SAAS_GENERATION=false
ENABLE_NOTIFICATIONS=true
ENABLE_AUTHENTICATION=true

# Debug Configuration
DEBUG=false
VERBOSE_LOGGING=false
ENABLE_PROFILING=false

# =============================================================================
# BACKUP & SECURITY
# =============================================================================

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=unconstrained_ai
DB_USER=postgres
DB_PASSWORD=your_db_password
DB_SSL_MODE=prefer
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_SSL=false
REDIS_TIMEOUT=30
REDIS_POOL_SIZE=10

# Trading Configuration
MARKET_DATA_PROVIDER=alphavantage
BROKER=alpaca
PAPER_TRADING=true
MAX_DAILY_TRADES=50
MAX_POSITION_SIZE=0.05
MAX_DAILY_LOSS=0.02
MAX_TOTAL_LOSS=0.10
TRADING_SYMBOLS=AAPL,GOOGL,MSFT,TSLA

# SaaS Configuration
DEPLOYMENT_PLATFORM=coolify
MAX_CONCURRENT_PRODUCTS=5
MVP_TIMEOUT_HOURS=72
TARGET_MRR=500.0
VALIDATION_THRESHOLD=0.7

# Security Configuration
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_EXPIRATION_HOURS=24
PASSWORD_MIN_LENGTH=8
MAX_LOGIN_ATTEMPTS=5
SESSION_TIMEOUT_MINUTES=30
ENABLE_2FA=false

# API Keys
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key
ALPACA_API_KEY=your_alpaca_api_key
ALPACA_SECRET_KEY=your_alpaca_secret_key
OPENAI_API_KEY=your_openai_api_key
COOLIFY_API_KEY=your_coolify_api_key
COOLIFY_BASE_URL=https://your-coolify-instance.com

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/unconstrained_ai.log