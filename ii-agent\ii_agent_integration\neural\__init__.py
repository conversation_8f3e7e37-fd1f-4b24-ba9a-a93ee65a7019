"""
Real Neural Networks Module
===========================

Production-ready neural network implementations for multi-agent systems.
"""

try:
    from .real_neural_networks import (
        RealNeuralNetwork,
        RealNeuralAgent,
        TextEncoder,
        AgentSpecialization,
        NeuralConfig
    )
    
    __all__ = [
        "RealNeuralNetwork",
        "RealNeuralAgent", 
        "TextEncoder",
        "AgentSpecialization",
        "NeuralConfig"
    ]
    
except ImportError as e:
    # Fallback if PyTorch is not available
    import logging
    logging.getLogger(__name__).warning(f"Real neural networks not available: {e}")
    
    __all__ = []
