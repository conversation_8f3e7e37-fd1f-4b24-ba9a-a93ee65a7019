"""
Simple WebSocket test to check tool execution in the live system
"""

import asyncio
import websockets
import json
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_agent_file_creation():
    """Test file creation through WebSocket interface"""
    
    uri = "ws://localhost:8080/api/ws"
    
    try:
        async with websockets.connect(uri) as websocket:
            logger.info("Connected to WebSocket")
            
            # Test message for file creation
            test_message = {
                "type": "message",
                "content": "Create a simple text file called 'hello.txt' with the content 'Hello World from WebSocket test!'"
            }
            
            # Send the message
            await websocket.send(json.dumps(test_message))
            logger.info(f"Sent message: {test_message['content']}")
            
            # Listen for responses
            response_count = 0
            while response_count < 10:  # Limit responses to prevent infinite loop
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    response_data = json.loads(response)
                    
                    logger.info(f"Response {response_count + 1}: {response_data}")
                    
                    # Check for specific issues
                    content = response_data.get('content', '')
                    if '[no tools were called]' in content:
                        logger.error("*** ISSUE DETECTED: Agent reports no tools were called ***")
                    if 'Tool with name' in content and 'not found' in content:
                        logger.error("*** ISSUE DETECTED: Tool not found error ***")
                    if 'str_replace_editor' in content:
                        logger.info("*** Tool usage detected: str_replace_editor ***")
                    if 'hello.txt' in content:
                        logger.info("*** File creation mentioned in response ***")
                    
                    response_count += 1
                    
                except asyncio.TimeoutError:
                    logger.info("Timeout waiting for response - ending test")
                    break
                except Exception as e:
                    logger.error(f"Error receiving response: {e}")
                    break
                    
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")

if __name__ == "__main__":
    asyncio.run(test_agent_file_creation())
