from typing import Any, Optional
from ii_agent.llm.message_history import Message<PERSON><PERSON><PERSON>
from ii_agent.tools.base import <PERSON><PERSON><PERSON>, ToolImplOutput


class MessageTool(LLMTool):
    name = "message_user"

    description = """\
Send a message to the user. Use this tool to communicate effectively in a variety of scenarios, including:
* Sharing your current thoughts or reasoning process
* Asking clarifying or follow-up questions
* Acknowledging receipt of messages
* Providing real-time progress updates
* Reporting completion of tasks or milestones
* Explaining changes in strategy, unexpected behavior, or encountered issues"""
    
    input_schema = {
        "type": "object",
        "properties": {
            "text": {"type": "string", "description": "The message to send to the user"},
            "message": {"type": "string", "description": "Alias for 'text' (planner compatibility)"},
        },
        # Accept either 'text' or 'message'
        "oneOf": [
            {"required": ["text"]},
            {"required": ["message"]},
        ],
    }

    async def run_impl(
        self,
        tool_input: dict[str, Any],
        message_history: Optional[MessageHistory] = None,
    ) -> ToolImplOutput:
        text = tool_input.get("text") or tool_input.get("message")
        assert text, "Model returned empty message"
        # Echo the text back as tool output so the model can include it in the assistant message
        return ToolImplOutput(text, "Sent message to user", auxiliary_data={"success": True})