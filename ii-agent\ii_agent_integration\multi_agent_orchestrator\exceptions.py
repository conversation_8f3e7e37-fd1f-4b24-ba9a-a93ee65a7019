class AuthRequiredError(Exception):
    """
    Custom exception raised when an operation requiring authentication is attempted
    without proper authentication, and the orchestrator's _auth_enabled flag is set to True.
    """
    pass

class InvalidAgentTypeError(Exception):
    """
    Custom exception raised when an attempt is made to register an agent instance
    that does not inherit from the BaseAgent abstract class.
    """
    pass

class InvalidAgentNameError(Exception):
    """
    Custom exception raised when an agent name does not meet the specified
    format or validation rules.
    """
    pass

class AgentInternalError(Exception):
    """
    Custom exception raised when an agent reports an internal failure during
    task reception or other operations.
    """
    pass
