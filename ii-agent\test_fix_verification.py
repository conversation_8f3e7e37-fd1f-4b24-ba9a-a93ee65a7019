#!/usr/bin/env python3
"""
Test to verify the Gemini conversation history fix resolves the tool calling issue
"""

import asyncio
import websockets
import json
import time

async def test_tool_execution_fix():
    """Test if tools are now being called after the fix"""
    
    uri = "ws://localhost:8080/api/ws"
    
    try:
        print("🧪 Testing tool execution after Gemini conversation history fix...")
        print(f"Connecting to: {uri}")
        
        async with websockets.connect(uri) as websocket:
            print("✓ Connected to WebSocket")
            
            # Send a clear file creation request
            test_message = {
                "type": "message", 
                "content": "Please create a text file called 'test_fix_verification.txt' containing the text 'Tool calling is now working correctly!'"
            }
            
            await websocket.send(json.dumps(test_message))
            print(f"✓ Sent request: {test_message['content']}")
            
            # Listen for responses
            responses = []
            timeout_seconds = 30
            start_time = time.time()
            
            while time.time() - start_time < timeout_seconds:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5)
                    response_data = json.loads(response)
                    responses.append(response_data)
                    
                    content = response_data.get('content', '')
                    print(f"📝 Response: {content[:100]}{'...' if len(content) > 100 else ''}")
                    
                    # Check for key indicators
                    if '[no tools were called]' in content:
                        print("❌ STILL BROKEN: Agent reports no tools were called")
                        return False
                    
                    if 'str_replace_editor' in content and 'create' in content:
                        print("✅ PROGRESS: Tool execution detected!")
                        
                    if 'test_fix_verification.txt' in content and ('created' in content or 'file' in content):
                        print("🎉 SUCCESS: File creation mentioned in response!")
                        return True
                        
                except asyncio.TimeoutError:
                    continue
                except Exception as e:
                    print(f"⚠️  Error receiving response: {e}")
                    break
            
            print(f"⏰ Test completed after {len(responses)} responses")
            return len([r for r in responses if 'str_replace_editor' in str(r)]) > 0
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_tool_execution_fix())
    if success:
        print("\n🎊 VERIFICATION: Tool calling fix appears to be working!")
    else:
        print("\n😞 ISSUE PERSISTS: Tool calling still not working properly")
        print("Need to investigate further...")
