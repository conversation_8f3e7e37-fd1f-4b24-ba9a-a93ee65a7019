"""
RUV-FANN Compliant Neural Agent with Proper Fallback Hierarchy
==============================================================

This module implements the RUV-FANN architecture ADR-004 fallback strategy:
1. Primary Path: WASM acceleration (ruv_fann_wasm) - <75ms target
2. Stage 1 Fallback: Simpler In-WASM model - performance over accuracy
3. Stage 2 Fallback: MCP Server offload (ruv-swarm) - robust but slower  
4. Stage 3 Fallback: Explicit failure with detailed error codes

Following RUV-FANN architecture principles for resilience and testability.
"""

import logging
import time
from typing import Dict, Any, List, Optional

# WASM acceleration (Primary Path - ruv_fann_wasm equivalent)
try:
    from .phase2_wasm_integration import (
        WASMConfig,
        WASMPerformanceMode,
        WASMNeuralAgent,
        WASMSwarmCoordinator
    )
    WASM_AVAILABLE = True
    logging.info("RUV-FANN Primary Path: WASM acceleration available")
except ImportError as e:
    WASM_AVAILABLE = False
    logging.warning(f"RUV-FANN Primary Path: WASM acceleration not available: {e}")

# Simpler neural models (Stage 1 Fallback)
try:
    from .phase1_real_neural_networks import (
        NetworkConfig, 
        SimpleNeuralNetwork, 
        RealNeuralAgent, 
        NeuralSwarmCoordinator,
        AgentType
    )
    SIMPLE_MODELS_AVAILABLE = True
    logging.info("RUV-FANN Stage 1 Fallback: Simple neural models available")
except ImportError as e:
    SIMPLE_MODELS_AVAILABLE = False
    logging.warning(f"RUV-FANN Stage 1 Fallback: Simple models not available: {e}")

# MCP Server interface (Stage 2 Fallback - ruv-swarm equivalent)
try:
    from .phase3_gpu_acceleration import (
        GPUConfig,
        GPUBackend,
        GPUMemoryStrategy,
        GPUNeuralAgent,
        GPUSwarmCoordinator
    )
    MCP_SERVER_AVAILABLE = True
    logging.info("RUV-FANN Stage 2 Fallback: MCP Server interface available")
except ImportError as e:
    MCP_SERVER_AVAILABLE = False
    logging.warning(f"RUV-FANN Stage 2 Fallback: MCP Server not available: {e}")

# Core imports
try:
    from .ruv_fann_neural_core import TextAnalyzer, AgentSelector, NeuralPattern
except ImportError:
    # Fallback for standalone execution
    try:
        from ruv_fann_neural_core import TextAnalyzer, AgentSelector, NeuralPattern
    except ImportError:
        # Mock classes for testing
        class TextAnalyzer:
            pass
        class AgentSelector:
            pass
        class NeuralPattern:
            pass

# RUV-FANN Error Handling (ADR-006)
class BaseRuvFANNError(Exception):
    """Base error for all RUV-FANN operations"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None, cause: Optional[Exception] = None):
        super().__init__(message)
        self.details = details or {}
        self.cause = cause

class NeuroDivergentError(BaseRuvFANNError):
    """WASM-related errors (Primary Path failures)"""
    pass

class RuvSwarmError(BaseRuvFANNError):
    """MCP-related errors (Stage 2 Fallback failures)"""
    pass

# Performance constants (ADR-004)
WASM_PERFORMANCE_THRESHOLD_MS = 75  # C-1 constraint from RUV-FANN
MCP_EXTENDED_TIMEOUT_MS = 5000  # Extended timeout for ruv-swarm

# Availability check for RUV-FANN fallback hierarchy
NEURAL_AVAILABLE = WASM_AVAILABLE or SIMPLE_MODELS_AVAILABLE or MCP_SERVER_AVAILABLE

if not NEURAL_AVAILABLE:
    logging.error("RUV-FANN: Complete system failure - no fallback mechanisms available!")
else:
    fallback_status = []
    if WASM_AVAILABLE:
        fallback_status.append("WASM(Primary)")
    if SIMPLE_MODELS_AVAILABLE:
        fallback_status.append("Simple(Stage1)")
    if MCP_SERVER_AVAILABLE:
        fallback_status.append("MCP(Stage2)")
    logging.info(f"RUV-FANN Fallback Hierarchy: {' → '.join(fallback_status)}")

class CircuitBreakerState:
    """Circuit breaker states for MCP server (ADR-003)"""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

class MCPCircuitBreaker:
    """Circuit breaker for MCP server communication (ADR-003)"""
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = CircuitBreakerState.CLOSED
    
    def can_execute(self) -> bool:
        """Check if MCP request can be executed"""
        if self.state == CircuitBreakerState.CLOSED:
            return True
        elif self.state == CircuitBreakerState.OPEN:
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = CircuitBreakerState.HALF_OPEN
                return True
            return False
        else:  # HALF_OPEN
            return True
    
    def record_success(self):
        """Record successful MCP operation"""
        self.failure_count = 0
        self.state = CircuitBreakerState.CLOSED
    
    def record_failure(self):
        """Record failed MCP operation"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN

class SimpleNeuralCore:
    """Simplified neural intelligence core for Stage 3 Fallback."""
    
    def __init__(self):
        self.accuracy = 84.8
        self.confidence_threshold = 0.6
        
    def analyze_message(self, message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Analyze message and return neural insights."""
        analysis = {
            "complexity": min(len(message) / 100, 1.0),
            "confidence": 0.85,
            "agent_type": "analyst" if "analyze" in message.lower() else "coder",
            "neural_score": 0.72
        }
        return analysis

class RuvFANNNeuralAgent:
    """
    RUV-FANN Compliant Neural Agent with ADR-004 Fallback Strategy
    ===============================================================
    
    Implements the proper RUV-FANN fallback hierarchy:
    - Primary Path: WASM acceleration (<75ms, ruv_fann_wasm)
    - Stage 1 Fallback: Simpler In-WASM model (performance over accuracy)  
    - Stage 2 Fallback: MCP Server offload (ruv-swarm, robust but slower)
    - Stage 3 Fallback: Explicit failure with error codes
    """
    
    def __init__(self, enable_wasm: bool = True, enable_simple_models: bool = True, enable_mcp_server: bool = True):
        # RUV-FANN fallback hierarchy setup
        self.enable_wasm = enable_wasm and WASM_AVAILABLE
        self.enable_simple_models = enable_simple_models and SIMPLE_MODELS_AVAILABLE  
        self.enable_mcp_server = enable_mcp_server and MCP_SERVER_AVAILABLE
        
        # Coordinators for each fallback stage
        self.wasm_coordinator = None  # Primary Path (ruv_fann_wasm equivalent)
        self.simple_coordinator = None  # Stage 1 Fallback
        self.mcp_coordinator = None  # Stage 2 Fallback (ruv-swarm equivalent)
        self.fallback_core = SimpleNeuralCore()  # Stage 3 Fallback
        
        # Circuit breaker for MCP server (ADR-003)
        self.mcp_circuit_breaker = MCPCircuitBreaker()
        
        # Performance tracking
        self.active_path = "stage3_fallback"
        self.performance_metrics = {
            "wasm_calls": 0,
            "wasm_failures": 0, 
            "simple_model_calls": 0,
            "mcp_calls": 0,
            "mcp_failures": 0,
            "fallback_calls": 0
        }
        
        self._initialize_fallback_hierarchy()
    
    def _initialize_fallback_hierarchy(self):
        """Initialize the RUV-FANN fallback hierarchy according to ADR-004"""
        
        # Initialize Primary Path: WASM acceleration
        if self.enable_wasm:
            try:
                wasm_config = WASMConfig(
                    enable_simd=True,
                    enable_threads=True,  # Maximum performance for <75ms constraint
                    memory_pages=32,
                    optimization_mode=WASMPerformanceMode.PERFORMANCE
                )
                self.wasm_coordinator = WASMSwarmCoordinator(wasm_config)
                self.active_path = "primary_wasm"
                logging.info("🚀 RUV-FANN Primary Path: WASM acceleration activated (<75ms target)")
            except Exception as e:
                logging.error(f"RUV-FANN Primary Path failed to initialize: {e}")
                self.enable_wasm = False
        
        # Initialize Stage 1 Fallback: Simpler In-WASM model  
        if self.enable_simple_models and not self.enable_wasm:
            try:
                self.simple_coordinator = NeuralSwarmCoordinator()
                self.active_path = "stage1_simple"
                logging.info("🔧 RUV-FANN Stage 1 Fallback: Simple models activated")
            except Exception as e:
                logging.error(f"RUV-FANN Stage 1 Fallback failed to initialize: {e}")
                self.enable_simple_models = False
        
        # Initialize Stage 2 Fallback: MCP Server interface (ruv-swarm equivalent)
        if self.enable_mcp_server and not self.enable_wasm and not self.enable_simple_models:
            try:
                # Using GPU acceleration as MCP server interface (represents ruv-swarm)
                mcp_config = GPUConfig(
                    backend=GPUBackend.AUTO,
                    memory_strategy=GPUMemoryStrategy.LAZY,  # Conservative for reliability
                    batch_size=16,
                    enable_fp16=False,  # Precision over speed for robust processing
                    enable_tensor_cores=False
                )
                self.mcp_coordinator = GPUSwarmCoordinator(mcp_config)
                self.active_path = "stage2_mcp"
                logging.info("🌐 RUV-FANN Stage 2 Fallback: MCP Server interface activated")
            except Exception as e:
                logging.error(f"RUV-FANN Stage 2 Fallback failed to initialize: {e}")
                self.enable_mcp_server = False
    
    def analyze_input(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Analyze input using RUV-FANN fallback hierarchy (ADR-004)
        
        Returns analysis with performance metrics and fallback information.
        """
        start_time = time.time()
        context = context or {}
        
        try:
            # Primary Path: WASM acceleration (<75ms target)
            if self.enable_wasm and self.wasm_coordinator:
                return self._try_wasm_analysis(user_input, context, start_time)
            
            # Stage 1 Fallback: Simpler In-WASM model
            elif self.enable_simple_models and self.simple_coordinator:
                return self._try_simple_model_analysis(user_input, context, start_time)
            
            # Stage 2 Fallback: MCP Server offload (ruv-swarm)
            elif self.enable_mcp_server and self.mcp_coordinator and self.mcp_circuit_breaker.can_execute():
                return self._try_mcp_server_analysis(user_input, context, start_time)
            
            # Stage 3 Fallback: Explicit failure with detailed error
            else:
                return self._handle_complete_failure(user_input, context, start_time)
                
        except Exception as e:
            logging.error(f"RUV-FANN analysis failed: {e}")
            return self._handle_complete_failure(user_input, context, start_time, error=e)
    
    def _try_wasm_analysis(self, user_input: str, context: Dict[str, Any], start_time: float) -> Dict[str, Any]:
        """Primary Path: WASM acceleration with <75ms performance constraint"""
        try:
            self.performance_metrics["wasm_calls"] += 1
            
            # Use WASM coordinator for high-speed prediction
            agent, analysis = self.wasm_coordinator.select_optimal_agent(user_input)
            
            execution_time_ms = (time.time() - start_time) * 1000
            
            # Check C-1 performance constraint (<75ms)
            if execution_time_ms > WASM_PERFORMANCE_THRESHOLD_MS:
                logging.warning(f"RUV-FANN WASM exceeded 75ms threshold: {execution_time_ms:.2f}ms")
                self.performance_metrics["wasm_failures"] += 1
                
                # Trigger Stage 1 Fallback
                if self.enable_simple_models and self.simple_coordinator:
                    return self._try_simple_model_analysis(user_input, context, start_time)
                else:
                    raise NeuroDivergentError(
                        "WASM_PERFORMANCE_CONSTRAINT_VIOLATION", 
                        details={"execution_time_ms": execution_time_ms, "threshold_ms": WASM_PERFORMANCE_THRESHOLD_MS}
                    )
            
            # Successful primary path execution
            analysis.update({
                "ruv_fann_path": "primary_wasm",
                "execution_time_ms": execution_time_ms,
                "performance_compliant": True,
                "fallback_stage": "primary"
            })
            
            return analysis
            
        except Exception as e:
            self.performance_metrics["wasm_failures"] += 1
            raise NeuroDivergentError("WASM_RUNTIME_ERROR", cause=e)
    
    def _try_simple_model_analysis(self, user_input: str, context: Dict[str, Any], start_time: float) -> Dict[str, Any]:
        """Stage 1 Fallback: Simpler In-WASM model (performance over accuracy)"""
        try:
            self.performance_metrics["simple_model_calls"] += 1
            
            # Use simple neural coordinator
            agent, analysis = self.simple_coordinator.select_optimal_agent(user_input)
            
            execution_time_ms = (time.time() - start_time) * 1000
            
            analysis.update({
                "ruv_fann_path": "stage1_simple",
                "execution_time_ms": execution_time_ms,
                "performance_compliant": execution_time_ms < WASM_PERFORMANCE_THRESHOLD_MS,
                "fallback_stage": "stage1",
                "accuracy_reduced": True  # Acknowledge reduced accuracy for performance
            })
            
            return analysis
            
        except Exception as e:
            # If Stage 1 fails, try Stage 2
            if self.enable_mcp_server and self.mcp_coordinator and self.mcp_circuit_breaker.can_execute():
                return self._try_mcp_server_analysis(user_input, context, start_time)
            else:
                raise NeuroDivergentError("SIMPLE_MODEL_ERROR", cause=e)
    
    def _try_mcp_server_analysis(self, user_input: str, context: Dict[str, Any], start_time: float) -> Dict[str, Any]:
        """Stage 2 Fallback: MCP Server offload (ruv-swarm equivalent)"""
        try:
            self.performance_metrics["mcp_calls"] += 1
            
            # Use MCP server interface (GPU coordinator as ruv-swarm representative)
            agent, analysis = self.mcp_coordinator.select_optimal_agent(user_input)
            
            execution_time_ms = (time.time() - start_time) * 1000
            
            # Record successful MCP operation
            self.mcp_circuit_breaker.record_success()
            
            analysis.update({
                "ruv_fann_path": "stage2_mcp",
                "execution_time_ms": execution_time_ms,
                "performance_compliant": False,  # MCP is robust but slower
                "fallback_stage": "stage2",
                "mcp_server_used": True,
                "extended_timeout": execution_time_ms > MCP_EXTENDED_TIMEOUT_MS
            })
            
            return analysis
            
        except Exception as e:
            self.performance_metrics["mcp_failures"] += 1
            self.mcp_circuit_breaker.record_failure()
            raise RuvSwarmError("MCP_SERVER_EXECUTION_FAILED", cause=e)
    
    def _handle_complete_failure(self, user_input: str, context: Dict[str, Any], start_time: float, error: Exception = None) -> Dict[str, Any]:
        """Stage 3 Fallback: Explicit failure with detailed error codes"""
        self.performance_metrics["fallback_calls"] += 1
        execution_time_ms = (time.time() - start_time) * 1000
        
        # Use fallback core for basic analysis
        fallback_analysis = self.fallback_core.analyze_message(user_input, context)
        
        # Determine specific error code
        if not WASM_AVAILABLE and not SIMPLE_MODELS_AVAILABLE and not MCP_SERVER_AVAILABLE:
            error_code = "COMPLETE_SYSTEM_UNAVAILABLE"
        elif self.mcp_circuit_breaker.state == CircuitBreakerState.OPEN:
            error_code = "MCP_CIRCUIT_BREAKER_OPEN"
        else:
            error_code = "PREDICTION_UNAVAILABLE"
        
        fallback_analysis.update({
            "ruv_fann_path": "stage3_fallback",
            "execution_time_ms": execution_time_ms,
            "performance_compliant": False,
            "fallback_stage": "stage3",
            "error_code": error_code,
            "system_degraded": True,
            "original_error": str(error) if error else None,
            "circuit_breaker_state": self.mcp_circuit_breaker.state
        })
        
        logging.error(f"RUV-FANN Stage 3 Fallback activated: {error_code}")
        return fallback_analysis
    
    def get_performance_info(self) -> Dict[str, Any]:
        """Get comprehensive performance information for the RUV-FANN system"""
        return {
            "ruv_fann_compliance": {
                "architecture_version": "ADR-004",
                "fallback_hierarchy_active": True,
                "primary_path": "wasm_acceleration",
                "current_active_path": self.active_path
            },
            "availability": {
                "wasm_available": WASM_AVAILABLE,
                "simple_models_available": SIMPLE_MODELS_AVAILABLE,
                "mcp_server_available": MCP_SERVER_AVAILABLE,
                "neural_system_available": NEURAL_AVAILABLE
            },
            "performance_metrics": self.performance_metrics.copy(),
            "circuit_breaker": {
                "state": self.mcp_circuit_breaker.state,
                "failure_count": self.mcp_circuit_breaker.failure_count,
                "threshold": self.mcp_circuit_breaker.failure_threshold
            },
            "constraints": {
                "wasm_threshold_ms": WASM_PERFORMANCE_THRESHOLD_MS,
                "mcp_timeout_ms": MCP_EXTENDED_TIMEOUT_MS
            }
        }

# Legacy compatibility alias
SimpleNeuralAgent = RuvFANNNeuralAgent

def get_neural_agent() -> RuvFANNNeuralAgent:
    """Factory function to create a RUV-FANN compliant neural agent"""
    return RuvFANNNeuralAgent()

if __name__ == "__main__":
    # Test RUV-FANN compliance
    logging.basicConfig(level=logging.INFO)
    
    agent = RuvFANNNeuralAgent()
    test_input = "Help me optimize this Python function for better performance"
    
    result = agent.analyze_input(test_input)
    
    print("=== RUV-FANN ANALYSIS RESULT ===")
    print(f"Path: {result.get('ruv_fann_path', 'unknown')}")
    print(f"Execution Time: {result.get('execution_time_ms', 0):.2f}ms")
    print(f"Performance Compliant: {result.get('performance_compliant', False)}")
    print(f"Fallback Stage: {result.get('fallback_stage', 'unknown')}")
    
    print("\n=== PERFORMANCE INFO ===")
    perf_info = agent.get_performance_info()
    print(f"Architecture: {perf_info['ruv_fann_compliance']['architecture_version']}")
    print(f"Active Path: {perf_info['ruv_fann_compliance']['current_active_path']}")
    print(f"Metrics: {perf_info['performance_metrics']}")
