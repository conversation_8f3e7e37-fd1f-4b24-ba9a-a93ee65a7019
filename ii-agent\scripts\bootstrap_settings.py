import os
import async<PERSON>
import json
from pathlib import Path

from ii_agent.core.storage.models.settings import Settings
from ii_agent.core.config.llm_config import LLMConfig, APITypes
from ii_agent.core.config.ii_agent_config import IIAgentConfig
from ii_agent.core.storage import get_file_store
from ii_agent.core.config.client_config import ClientConfig

DEFAULT_MODEL = os.getenv("II_DEFAULT_MODEL", "gemini-2.0-flash")

async def main():
    config = IIAgentConfig()
    file_store = get_file_store(config.file_store, config.file_store_path)
    settings_path = "settings.json"

    # If settings already exist, do nothing
    try:
        file_store.read(settings_path)
        print("Settings already exist; bootstrap skipped.")
        return
    except FileNotFoundError:
        pass

    google_key = os.getenv("GOOGLE_AI_STUDIO_API_KEY")

    llm_configs = {
        DEFAULT_MODEL: LLMConfig(
            api_type=APITypes.GEMINI,
            model=DEFAULT_MODEL,
            api_key=google_key,
            max_retries=3,
            temperature=0.0,
        )
    }

    # Default sandbox config: Docker mode, service port 17300
    from ii_agent.core.config.sandbox_config import SandboxConfig
    from ii_agent.utils.constants import WorkSpaceMode
    sandbox_config = SandboxConfig(mode=WorkSpaceMode.DOCKER, service_port=17300)

    # Default client config for Docker sandbox
    # Use the internal Docker DNS name 'sandbox' and the sandbox service port
    client_config = ClientConfig(server_url="http://sandbox:17300", cwd="/workspace")

    settings = Settings(
        llm_configs=llm_configs,
        sandbox_config=sandbox_config,
        client_config=client_config,
    )

    # Write settings.json
    json_str = settings.model_dump_json(context={"expose_secrets": True})
    file_store.write(settings_path, json_str)
    print(f"Bootstrapped settings with default model '{DEFAULT_MODEL}'.")

if __name__ == "__main__":
    asyncio.run(main())
