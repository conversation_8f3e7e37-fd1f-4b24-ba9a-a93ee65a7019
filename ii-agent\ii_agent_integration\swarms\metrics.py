"""
Lightweight metrics for swarms: counters & histograms.
Use logging and in-memory stores; can be upgraded to OpenTelemetry.
"""
import time
from collections import defaultdict
from typing import Dict


class Metrics:
    def __init__(self):
        self.counters: Dict[str, int] = defaultdict(int)
        self.timings_ms: Dict[str, list[float]] = defaultdict(list)

    def inc(self, name: str, value: int = 1):
        self.counters[name] += value

    def observe_ms(self, name: str, value_ms: float):
        self.timings_ms[name].append(float(value_ms))

    def snapshot(self):
        # Return shallow copy
        return {
            "counters": dict(self.counters),
            "timings_ms": {k: v[:] for k, v in self.timings_ms.items()},
        }


global_metrics = Metrics()
