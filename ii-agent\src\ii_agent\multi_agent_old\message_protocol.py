"""MessageProtocol - Standardized Agent Communication

Production-grade message protocol for multi-agent coordination.
Ensures reliable, structured communication between agents.
"""

import json
import uuid
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timezone
from enum import Enum
import hashlib


class MessageType(Enum):
    """Standard message types for agent communication"""
    TASK_ASSIGNMENT = "task_assignment"
    TASK_COMPLETION = "task_completion"
    TASK_COMPLETE = "task_completion"  # Alias for backwards compatibility
    TASK_FAILURE = "task_failure"
    COORDINATION_REQUEST = "coordination_request"
    COORDINATION_RESPONSE = "coordination_response"
    STATUS_UPDATE = "status_update"
    AGENT_STATUS = "status_update"  # Alias for backwards compatibility
    CAPABILITY_INQUIRY = "capability_inquiry"
    CAPABILITY_RESPONSE = "capability_response"
    AGENT_REGISTRATION = "agent_registration"
    AGENT_DEREGISTRATION = "agent_deregistration"
    RESOURCE_REQUEST = "resource_request"
    HEARTBEAT = "heartbeat"
    ERROR = "error"
    ACK = "acknowledgment"


class MessagePriority(Enum):
    """Message priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


class MessageProtocol:
    """
    Standardized protocol for agent-to-agent communication.
    
    Provides:
    - Message structure validation
    - Serialization/deserialization
    - Message routing metadata
    - Reliability features (acknowledgments, retries)
    - Performance optimization
    """
    
    PROTOCOL_VERSION = "1.0"
    MAX_MESSAGE_SIZE = 1024 * 1024  # 1MB
    
    def __init__(self):
        """Initialize MessageProtocol"""
        self.message_cache: Dict[str, Dict[str, Any]] = {}
        self.validation_rules: Dict[str, Any] = self._setup_validation_rules()
    
    def _setup_validation_rules(self) -> Dict[str, Any]:
        """Setup message validation rules"""
        return {
            "required_fields": ["type", "from_agent"],  # More flexible required fields
            "optional_fields": ["id", "timestamp", "to_agent", "data", "priority", "requires_ack", "correlation_id"],
            "max_message_size": self.MAX_MESSAGE_SIZE,
            "valid_types": [msg_type.value for msg_type in MessageType],
            "valid_priorities": [priority.value for priority in MessagePriority]
        }
    
    def create_message(self,
                      message_type: str,
                      from_agent: str,
                      data: Optional[Dict[str, Any]] = None,
                      to_agent: Optional[str] = None,
                      priority: int = MessagePriority.NORMAL.value,
                      requires_ack: bool = False,
                      correlation_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a standardized message.
        
        Args:
            message_type: Type of message (from MessageType enum)
            from_agent: ID of sending agent
            data: Message payload data
            to_agent: ID of target agent (optional for broadcasts)
            priority: Message priority level
            requires_ack: Whether message requires acknowledgment
            correlation_id: ID to correlate with previous messages
            
        Returns:
            Standardized message dictionary
        """
        message_id = str(uuid.uuid4())
        timestamp = datetime.now(timezone.utc).isoformat()
        
        message = {
            "id": message_id,
            "type": message_type,
            "timestamp": timestamp,
            "from_agent": from_agent,
            "protocol_version": self.PROTOCOL_VERSION
        }
        
        # Add optional fields
        if to_agent:
            message["to_agent"] = to_agent
        if data:
            message["data"] = data
        if priority != MessagePriority.NORMAL.value:
            message["priority"] = priority
        if requires_ack:
            message["requires_ack"] = requires_ack
        if correlation_id:
            message["correlation_id"] = correlation_id
        
        # Add message hash for integrity
        message["checksum"] = self._calculate_checksum(message)
        
        # Cache message if it requires acknowledgment
        if requires_ack:
            self.message_cache[message_id] = message.copy()
        
        return message
    
    def create_task_assignment(self,
                             from_agent: str,
                             to_agent: str,
                             task_data: Dict[str, Any],
                             priority: int = MessagePriority.NORMAL.value) -> Dict[str, Any]:
        """Create task assignment message"""
        return self.create_message(
            message_type=MessageType.TASK_ASSIGNMENT.value,
            from_agent=from_agent,
            to_agent=to_agent,
            data=task_data,
            priority=priority,
            requires_ack=True
        )
    
    def create_task_completion(self,
                             from_agent: str,
                             task_id: str,
                             result_data: Dict[str, Any],
                             correlation_id: Optional[str] = None) -> Dict[str, Any]:
        """Create task completion message"""
        return self.create_message(
            message_type=MessageType.TASK_COMPLETION.value,
            from_agent=from_agent,
            data={
                "task_id": task_id,
                "result": result_data
            },
            correlation_id=correlation_id
        )
    
    def create_coordination_request(self,
                                  from_agent: str,
                                  to_agent: str,
                                  coordination_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create coordination request message"""
        return self.create_message(
            message_type=MessageType.COORDINATION_REQUEST.value,
            from_agent=from_agent,
            to_agent=to_agent,
            data=coordination_data,
            requires_ack=True
        )
    
    def create_status_update(self,
                           from_agent: str,
                           status_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create status update message"""
        return self.create_message(
            message_type=MessageType.STATUS_UPDATE.value,
            from_agent=from_agent,
            data=status_data
        )
    
    def create_capability_inquiry(self,
                                from_agent: str,
                                required_capabilities: List[str]) -> Dict[str, Any]:
        """Create capability inquiry message"""
        return self.create_message(
            message_type=MessageType.CAPABILITY_INQUIRY.value,
            from_agent=from_agent,
            data={"required_capabilities": required_capabilities}
        )
    
    def create_acknowledgment(self,
                            from_agent: str,
                            original_message_id: str,
                            status: str = "received") -> Dict[str, Any]:
        """Create acknowledgment message"""
        return self.create_message(
            message_type=MessageType.ACK.value,
            from_agent=from_agent,
            data={
                "original_message_id": original_message_id,
                "status": status
            },
            correlation_id=original_message_id
        )
    
    def create_heartbeat(self, from_agent: str) -> Dict[str, Any]:
        """Create heartbeat message"""
        return self.create_message(
            message_type=MessageType.HEARTBEAT.value,
            from_agent=from_agent,
            data={"timestamp": datetime.now(timezone.utc).isoformat()}
        )
    
    def validate_message(self, message: Dict[str, Any]) -> bool:
        """
        Validate message structure and content.
        
        Args:
            message: Message to validate
            
        Returns:
            True if message is valid, False otherwise
        """
        try:
            # Check basic required fields - be more flexible
            basic_required = ["type", "from_agent"]
            for field in basic_required:
                if field not in message:
                    return False
            
            # Check message type
            if message["type"] not in self.validation_rules["valid_types"]:
                return False
            
            # Check priority if present
            if "priority" in message:
                if message["priority"] not in self.validation_rules["valid_priorities"]:
                    return False
            
            # Check message size
            message_size = len(json.dumps(message))
            if message_size > self.validation_rules["max_message_size"]:
                return False
            
            # Verify checksum if present
            if "checksum" in message:
                calculated_checksum = self._calculate_checksum(
                    {k: v for k, v in message.items() if k != "checksum"}
                )
                if message["checksum"] != calculated_checksum:
                    return False
            
            return True
            
        except Exception:
            return False
    
    def serialize_message(self, message: Dict[str, Any]) -> str:
        """
        Serialize message to JSON string.
        
        Args:
            message: Message to serialize
            
        Returns:
            JSON string representation
        """
        try:
            return json.dumps(message, separators=(',', ':'))
        except Exception as e:
            raise ValueError(f"Failed to serialize message: {e}")
    
    def deserialize_message(self, message_str: str) -> Dict[str, Any]:
        """
        Deserialize message from JSON string.
        
        Args:
            message_str: JSON string to deserialize
            
        Returns:
            Message dictionary
        """
        try:
            message = json.loads(message_str)
            
            # Validate deserialized message
            if not self.validate_message(message):
                raise ValueError("Invalid message format")
            
            return message
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Failed to parse JSON: {e}")
        except Exception as e:
            raise ValueError(f"Failed to deserialize message: {e}")
    
    def route_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add routing metadata to message.
        
        Args:
            message: Message to add routing to
            
        Returns:
            Message with routing metadata
        """
        routing_metadata = {
            "routing": {
                "hops": 0,
                "path": [],
                "created_at": datetime.now(timezone.utc).isoformat()
            }
        }
        
        return {**message, **routing_metadata}
    
    def extract_task_data(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract task data from task assignment message"""
        if message.get("type") == MessageType.TASK_ASSIGNMENT.value:
            return message.get("data", {})
        return None
    
    def extract_coordination_data(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract coordination data from coordination request message"""
        if message.get("type") == MessageType.COORDINATION_REQUEST.value:
            return message.get("data", {})
        return None
    
    def is_acknowledgment_required(self, message: Dict[str, Any]) -> bool:
        """Check if message requires acknowledgment"""
        return message.get("requires_ack", False)
    
    def get_message_priority(self, message: Dict[str, Any]) -> int:
        """Get message priority level"""
        return message.get("priority", MessagePriority.NORMAL.value)
    
    def get_correlation_id(self, message: Dict[str, Any]) -> Optional[str]:
        """Get correlation ID from message"""
        return message.get("correlation_id")
    
    def mark_acknowledged(self, message_id: str) -> bool:
        """Mark message as acknowledged and remove from cache"""
        if message_id in self.message_cache:
            del self.message_cache[message_id]
            return True
        return False
    
    def get_pending_acknowledgments(self) -> List[str]:
        """Get list of message IDs waiting for acknowledgment"""
        return list(self.message_cache.keys())
    
    def _calculate_checksum(self, message: Dict[str, Any]) -> str:
        """Calculate message checksum for integrity verification"""
        # Create a canonical string representation
        message_str = json.dumps(message, sort_keys=True, separators=(',', ':'))
        return hashlib.md5(message_str.encode()).hexdigest()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get protocol statistics"""
        return {
            "protocol_version": self.PROTOCOL_VERSION,
            "pending_acknowledgments": len(self.message_cache),
            "validation_rules": self.validation_rules,
            "max_message_size": self.MAX_MESSAGE_SIZE
        }
