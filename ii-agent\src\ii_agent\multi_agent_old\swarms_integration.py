"""
Swarms Framework Integration - ii-agent Delegation Layer

This module provides a delegation layer that forwards all swarms functionality
to the core swarms implementation in the main project.
"""

import logging
from typing import Dict, List, Any, Optional
from src.swarms.swarms_integration import SwarmsFrameworkIntegration as CoreSwarmsFrameworkIntegration
from src.swarms.swarms_integration import SwarmsCoordinationPattern as CoreSwarmsCoordinationPattern


# Re-export coordination patterns from core
SwarmsCoordinationPattern = CoreSwarmsCoordinationPattern


class SwarmsFrameworkIntegration:
    """ii-agent delegation layer for Swarms framework integration"""
    
    def __init__(self, coordination_hub, backend_adapter: Optional[Any] = None):
        """Initialize by delegating to core swarms integration"""
        self.coordination_hub = coordination_hub
        self.backend_adapter = backend_adapter
        
        # Delegate to core implementation
        self._core_swarms = CoreSwarmsFrameworkIntegration(
            coordination_hub, 
            backend_adapter
        )
        
        # Logging
        self.logger = logging.getLogger(__name__)
        self.logger.info("ii-agent Swarms integration initialized (delegating to core)")
    
    # Delegate all methods to core implementation
    async def start(self):
        """Start the Swarms integration"""
        return await self._core_swarms.start()
    
    async def stop(self):
        """Stop the Swarms integration"""
        return await self._core_swarms.stop()

    async def health(self) -> Dict[str, Any]:
        """Health status"""
        return await self._core_swarms.health()

    async def ready(self) -> Dict[str, Any]:
        """Readiness probe"""
        return await self._core_swarms.ready()
    
    async def execute_swarms_coordination(
        self, 
        task, 
        pattern,
        agent_config: Optional[Dict[str, Any]] = None
    ):
        """Execute task using specified Swarms coordination pattern"""
        return await self._core_swarms.execute_swarms_coordination(
            task, 
            pattern, 
            agent_config
        )
    
    async def history_recent(self, n: int = 10) -> List[Dict[str, Any]]:
        """Get recent execution history"""
        return await self._core_swarms.history_recent(n)

    async def history_stats(self) -> Dict[str, Any]:
        """Get execution history statistics"""
        return await self._core_swarms.history_stats()
    
    # Property delegation
    @property
    def swarms_agents(self):
        return self._core_swarms.swarms_agents
    
    @property
    def coordination_patterns(self):
        return self._core_swarms.coordination_patterns
    
    @property
    def metrics(self):
        return self._core_swarms.metrics
    
    @property 
    def circuit_breaker(self):
        return self._core_swarms.circuit_breaker
    
    @property
    def history(self):
        return self._core_swarms.history
