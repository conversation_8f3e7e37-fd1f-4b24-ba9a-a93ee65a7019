#!/usr/bin/env python3
"""
Comprehensive RUV-FANN Neural Test Suite
"""
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_neural_comprehensive():
    """Comprehensive test of all neural capabilities"""
    print("🧠 Comprehensive Neural Intelligence Test")
    print("=" * 60)
    
    from ii_agent.neural.simple_neural import SimpleNeuralAgent
    
    # Create neural agent
    agent = SimpleNeuralAgent(enable_neural=True)
    
    # Test various scenarios
    test_scenarios = [
        {
            "input": "I need help debugging a Python function that crashes",
            "expected_type": "coder"
        },
        {
            "input": "Can you research the latest machine learning frameworks?",
            "expected_type": "researcher"
        },
        {
            "input": "Help me analyze this dataset and create visualizations",
            "expected_type": "data_scientist"
        },
        {
            "input": "I need to design a responsive user interface",
            "expected_type": "designer"
        },
        {
            "input": "Please test this application for security vulnerabilities",
            "expected_type": "security"
        },
        {
            "input": "Help me set up a CI/CD pipeline with Dock<PERSON>",
            "expected_type": "devops"
        },
        {
            "input": "Can you analyze the performance of our system?",
            "expected_type": "analyst"
        }
    ]
    
    print(f"\n🧠 Testing {len(test_scenarios)} Neural Analysis Scenarios:")
    print("-" * 60)
    
    correct_predictions = 0
    total_predictions = len(test_scenarios)
    
    for i, scenario in enumerate(test_scenarios, 1):
        analysis = agent.get_neural_analysis(scenario["input"])
        
        predicted_type = analysis.get("agent_type")
        expected_type = scenario["expected_type"]
        confidence = analysis.get("confidence", 0) * 100
        strategy = analysis.get("strategy", "")
        
        is_correct = predicted_type == expected_type
        if is_correct:
            correct_predictions += 1
            status = "✅"
        else:
            status = "❌"
        
        print(f"{status} Test {i}: {scenario['input'][:50]}...")
        print(f"    Expected: {expected_type} | Predicted: {predicted_type} | Confidence: {confidence:.1f}%")
        print(f"    Strategy: {strategy}")
        print()
    
    accuracy = (correct_predictions / total_predictions) * 100
    print("=" * 60)
    print(f"🧠 Neural Intelligence Test Results:")
    print(f"   Correct Predictions: {correct_predictions}/{total_predictions}")
    print(f"   Accuracy: {accuracy:.1f}%")
    print(f"   Target Accuracy: 84.8%")
    
    if accuracy >= 70:  # Reasonable threshold
        print("🎉 NEURAL INTELLIGENCE WORKING EXCELLENTLY!")
        return True
    else:
        print("⚠️  Neural accuracy below threshold - needs tuning")
        return False

def test_neural_swarm_details():
    """Test detailed neural swarm capabilities"""
    print("\n🧠 Testing Neural Swarm Intelligence Details")
    print("-" * 60)
    
    from ii_agent.neural.simple_neural import SimpleNeuralAgent
    
    agent = SimpleNeuralAgent(enable_neural=True)
    
    # Test swarm information
    analysis = agent.get_neural_analysis("Test message for swarm analysis")
    swarm_info = analysis.get("analysis", {})
    
    print(f"Neural Swarm Intelligence: {swarm_info.get('swarm_intelligence', 0):.3f}")
    print(f"Neural Agents in Swarm: {swarm_info.get('neural_agents_count', 0)}")
    print(f"Pattern Matching: {swarm_info.get('pattern_match', 'Unknown')}")
    
    # Test that we have multiple agents in the swarm
    agent_count = swarm_info.get('neural_agents_count', 0)
    if agent_count >= 4:
        print("✅ Neural swarm properly initialized with multiple specialized agents")
        return True
    else:
        print(f"❌ Neural swarm has insufficient agents: {agent_count}")
        return False

if __name__ == "__main__":
    # Run comprehensive tests
    test1_success = test_neural_comprehensive()
    test2_success = test_neural_swarm_details()
    
    print("\n" + "=" * 60)
    if test1_success and test2_success:
        print("🎉 ALL NEURAL TESTS PASSED! Ready for Docker deployment!")
    else:
        print("❌ Some neural tests failed. Check implementation.")
    print("=" * 60)
