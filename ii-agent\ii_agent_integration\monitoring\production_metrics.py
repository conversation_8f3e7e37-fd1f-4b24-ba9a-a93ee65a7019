"""
Production Metrics Exporter
===========================

Real-time metrics collection and export for Prometheus monitoring
of the multi-agent system performance, GPU utilization, and coordination efficiency.
"""

import time
import logging
import asyncio
import psutil
import threading
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from prometheus_client import Counter, Histogram, Gauge, Info, start_http_server, CollectorRegistry, make_wsgi_app
import json
import os
import base64
import hashlib
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

logger = logging.getLogger(__name__)

# Try to import GPU monitoring
try:
    import torch
    GPU_MONITORING_AVAILABLE = torch.cuda.is_available()
except ImportError:
    GPU_MONITORING_AVAILABLE = False

try:
    import pynvml
    pynvml.nvmlInit()
    NVIDIA_ML_AVAILABLE = True
except ImportError:
    NVIDIA_ML_AVAILABLE = False


@dataclass
class MetricsConfig:
    """Configuration for metrics collection"""
    enable_gpu_metrics: bool = True
    enable_system_metrics: bool = True
    enable_coordination_metrics: bool = True
    enable_neural_metrics: bool = True
    metrics_port: int = 9090
    gpu_metrics_port: int = 9091
    coordination_metrics_port: int = 9092
    neural_metrics_port: int = 9093
    collection_interval: float = 10.0
    export_interval: float = 5.0
    # 🔐 SECURITY: Authentication configuration
    enable_auth: bool = True
    auth_username: str = "admin"
    auth_password: str = "secure_metrics_2024"


class AuthenticatedMetricsHandler(BaseHTTPRequestHandler):
    """HTTP handler with authentication for metrics endpoint"""

    def __init__(self, registry, auth_username, auth_password, *args, **kwargs):
        self.registry = registry
        self.auth_username = auth_username
        self.auth_password = auth_password
        self.wsgi_app = make_wsgi_app(registry)
        super().__init__(*args, **kwargs)

    def _authenticate(self) -> bool:
        """Verify HTTP Basic Authentication"""
        auth_header = self.headers.get('Authorization', '')

        if not auth_header.startswith('Basic '):
            return False

        try:
            # Decode base64 credentials
            encoded_credentials = auth_header[6:]
            decoded_credentials = base64.b64decode(encoded_credentials).decode('utf-8')
            username, password = decoded_credentials.split(':', 1)

            # Verify credentials (in production, use proper credential management)
            return username == self.auth_username and password == self.auth_password

        except Exception as e:
            logger.warning(f"Authentication error: {e}")
            return False

    def _send_auth_required(self):
        """Send 401 Unauthorized response"""
        self.send_response(401)
        self.send_header('WWW-Authenticate', 'Basic realm="Metrics"')
        self.send_header('Content-Type', 'text/plain')
        self.end_headers()
        self.wfile.write(b'Authentication required')

    def do_GET(self):
        """Handle GET requests with authentication"""
        # Check authentication
        if not self._authenticate():
            self._send_auth_required()
            return

        # Serve metrics using Prometheus WSGI app
        try:
            from wsgiref.simple_server import make_server
            import io
            import sys

            # Create a fake WSGI environment
            environ = {
                'REQUEST_METHOD': 'GET',
                'PATH_INFO': self.path,
                'QUERY_STRING': urlparse(self.path).query or '',
                'CONTENT_TYPE': '',
                'CONTENT_LENGTH': '',
                'SERVER_NAME': self.server.server_name,
                'SERVER_PORT': str(self.server.server_port),
                'wsgi.version': (1, 0),
                'wsgi.url_scheme': 'http',
                'wsgi.input': io.StringIO(),
                'wsgi.errors': sys.stderr,
                'wsgi.multithread': True,
                'wsgi.multiprocess': False,
                'wsgi.run_once': False,
            }

            # Capture response
            response_data = []
            response_status = []
            response_headers = []

            def start_response(status, headers):
                response_status.append(status)
                response_headers.extend(headers)

            # Call WSGI app
            response_iter = self.wsgi_app(environ, start_response)
            response_data = b''.join(response_iter)

            # Send response
            status_code = int(response_status[0].split(' ', 1)[0])
            self.send_response(status_code)

            for header_name, header_value in response_headers:
                self.send_header(header_name, header_value)
            self.end_headers()

            self.wfile.write(response_data)

        except Exception as e:
            logger.error(f"Error serving metrics: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'text/plain')
            self.end_headers()
            self.wfile.write(f'Internal server error: {str(e)}'.encode())

    def log_message(self, format, *args):
        """Override to use our logger"""
        logger.debug(f"Metrics request: {format % args}")


class ProductionMetricsCollector:
    """Production-ready metrics collector for multi-agent system"""
    
    def __init__(self, config: MetricsConfig = None):
        self.config = config or MetricsConfig()
        self.registry = CollectorRegistry()
        
        # Initialize metrics
        self._init_system_metrics()
        self._init_coordination_metrics()
        self._init_neural_metrics()
        self._init_gpu_metrics()
        
        # Collection state
        self.collection_active = False
        self.collection_thread = None
        
        logger.info("Production metrics collector initialized")
    
    def _init_system_metrics(self):
        """Initialize system performance metrics"""
        self.system_cpu_usage = Gauge(
            'ii_agent_system_cpu_usage_percent',
            'System CPU usage percentage',
            registry=self.registry
        )
        
        self.system_memory_usage = Gauge(
            'ii_agent_system_memory_usage_bytes',
            'System memory usage in bytes',
            registry=self.registry
        )
        
        self.system_memory_total = Gauge(
            'ii_agent_system_memory_total_bytes',
            'Total system memory in bytes',
            registry=self.registry
        )
        
        self.system_disk_usage = Gauge(
            'ii_agent_system_disk_usage_percent',
            'System disk usage percentage',
            registry=self.registry
        )
        
        self.process_memory_usage = Gauge(
            'ii_agent_process_memory_usage_bytes',
            'Process memory usage in bytes',
            registry=self.registry
        )
        
        self.process_cpu_usage = Gauge(
            'ii_agent_process_cpu_usage_percent',
            'Process CPU usage percentage',
            registry=self.registry
        )
    
    def _init_coordination_metrics(self):
        """Initialize multi-agent coordination metrics"""
        self.coordination_requests_total = Counter(
            'ii_agent_coordination_requests_total',
            'Total number of coordination requests',
            ['strategy', 'status'],
            registry=self.registry
        )
        
        self.coordination_duration = Histogram(
            'ii_agent_coordination_duration_seconds',
            'Time spent on coordination',
            ['strategy'],
            registry=self.registry
        )
        
        self.consensus_score = Gauge(
            'ii_agent_consensus_score',
            'Current consensus score',
            ['strategy'],
            registry=self.registry
        )
        
        self.active_agents = Gauge(
            'ii_agent_active_agents_count',
            'Number of active agents',
            registry=self.registry
        )
        
        self.agent_response_time = Histogram(
            'ii_agent_response_time_seconds',
            'Agent response time',
            ['agent_id', 'specialization'],
            registry=self.registry
        )
        
        self.coordination_success_rate = Gauge(
            'ii_agent_coordination_success_rate',
            'Coordination success rate',
            registry=self.registry
        )
    
    def _init_neural_metrics(self):
        """Initialize neural network performance metrics"""
        self.neural_inference_time = Histogram(
            'ii_agent_neural_inference_duration_seconds',
            'Neural network inference time',
            ['agent_id', 'device'],
            registry=self.registry
        )
        
        self.neural_training_loss = Gauge(
            'ii_agent_neural_training_loss',
            'Neural network training loss',
            ['agent_id'],
            registry=self.registry
        )
        
        self.neural_confidence_score = Gauge(
            'ii_agent_neural_confidence_score',
            'Neural network confidence score',
            ['agent_id'],
            registry=self.registry
        )
        
        self.neural_parameters_count = Gauge(
            'ii_agent_neural_parameters_total',
            'Total number of neural network parameters',
            ['agent_id'],
            registry=self.registry
        )
        
        self.neural_learning_episodes = Counter(
            'ii_agent_neural_learning_episodes_total',
            'Total number of learning episodes',
            ['agent_id'],
            registry=self.registry
        )
    
    def _init_gpu_metrics(self):
        """Initialize GPU performance metrics"""
        if not GPU_MONITORING_AVAILABLE:
            logger.warning("GPU monitoring not available")
            return
        
        self.gpu_utilization = Gauge(
            'ii_agent_gpu_utilization_percent',
            'GPU utilization percentage',
            ['device_id'],
            registry=self.registry
        )
        
        self.gpu_memory_used = Gauge(
            'ii_agent_gpu_memory_used_bytes',
            'GPU memory used in bytes',
            ['device_id'],
            registry=self.registry
        )
        
        self.gpu_memory_total = Gauge(
            'ii_agent_gpu_memory_total_bytes',
            'Total GPU memory in bytes',
            ['device_id'],
            registry=self.registry
        )
        
        self.gpu_temperature = Gauge(
            'ii_agent_gpu_temperature_celsius',
            'GPU temperature in Celsius',
            ['device_id'],
            registry=self.registry
        )
        
        self.gpu_power_usage = Gauge(
            'ii_agent_gpu_power_usage_watts',
            'GPU power usage in watts',
            ['device_id'],
            registry=self.registry
        )
        
        self.gpu_inference_speedup = Gauge(
            'ii_agent_gpu_inference_speedup_ratio',
            'GPU vs CPU inference speedup ratio',
            ['agent_id'],
            registry=self.registry
        )
    
    def collect_system_metrics(self):
        """Collect system performance metrics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.system_cpu_usage.set(cpu_percent)
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.system_memory_usage.set(memory.used)
            self.system_memory_total.set(memory.total)
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.system_disk_usage.set(disk_percent)
            
            # Process metrics
            process = psutil.Process()
            process_memory = process.memory_info()
            self.process_memory_usage.set(process_memory.rss)
            self.process_cpu_usage.set(process.cpu_percent())
            
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")
    
    def collect_gpu_metrics(self):
        """Collect GPU performance metrics"""
        if not GPU_MONITORING_AVAILABLE:
            return
        
        try:
            # PyTorch GPU metrics
            if torch.cuda.is_available():
                for device_id in range(torch.cuda.device_count()):
                    # Memory usage
                    allocated = torch.cuda.memory_allocated(device_id)
                    total = torch.cuda.get_device_properties(device_id).total_memory
                    
                    self.gpu_memory_used.labels(device_id=device_id).set(allocated)
                    self.gpu_memory_total.labels(device_id=device_id).set(total)
                    
                    # Utilization (approximated from memory usage)
                    utilization = (allocated / total) * 100 if total > 0 else 0
                    self.gpu_utilization.labels(device_id=device_id).set(utilization)
            
            # NVIDIA ML metrics (if available)
            if NVIDIA_ML_AVAILABLE:
                device_count = pynvml.nvmlDeviceGetCount()
                for device_id in range(device_count):
                    handle = pynvml.nvmlDeviceGetHandleByIndex(device_id)
                    
                    # Temperature
                    try:
                        temp = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
                        self.gpu_temperature.labels(device_id=device_id).set(temp)
                    except:
                        pass
                    
                    # Power usage
                    try:
                        power = pynvml.nvmlDeviceGetPowerUsage(handle) / 1000.0  # Convert to watts
                        self.gpu_power_usage.labels(device_id=device_id).set(power)
                    except:
                        pass
                    
                    # Utilization
                    try:
                        util = pynvml.nvmlDeviceGetUtilizationRates(handle)
                        self.gpu_utilization.labels(device_id=device_id).set(util.gpu)
                    except:
                        pass
        
        except Exception as e:
            logger.error(f"Failed to collect GPU metrics: {e}")
    
    def record_coordination_metrics(self, strategy: str, duration: float, success: bool, consensus_score: float, agent_count: int):
        """Record coordination performance metrics"""
        status = "success" if success else "failure"
        self.coordination_requests_total.labels(strategy=strategy, status=status).inc()
        self.coordination_duration.labels(strategy=strategy).observe(duration)
        self.consensus_score.labels(strategy=strategy).set(consensus_score)
        self.active_agents.set(agent_count)
    
    def record_neural_metrics(self, agent_id: str, inference_time: float, confidence: float, 
                            training_loss: Optional[float] = None, device: str = "cpu"):
        """Record neural network performance metrics"""
        self.neural_inference_time.labels(agent_id=agent_id, device=device).observe(inference_time)
        self.neural_confidence_score.labels(agent_id=agent_id).set(confidence)
        
        if training_loss is not None:
            self.neural_training_loss.labels(agent_id=agent_id).set(training_loss)
            self.neural_learning_episodes.labels(agent_id=agent_id).inc()
    
    def record_gpu_speedup(self, agent_id: str, speedup_ratio: float):
        """Record GPU vs CPU speedup metrics"""
        self.gpu_inference_speedup.labels(agent_id=agent_id).set(speedup_ratio)
    
    def start_collection(self):
        """Start metrics collection with authenticated HTTP server"""
        if self.collection_active:
            logger.warning("Metrics collection already active")
            return

        self.collection_active = True
        self.collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        self.collection_thread.start()

        # Start authenticated HTTP server for metrics export
        try:
            if self.config.enable_auth:
                # Get credentials from environment or config
                auth_username = os.environ.get('II_AGENT_METRICS_AUTH_USER', self.config.auth_username)
                auth_password = os.environ.get('II_AGENT_METRICS_AUTH_PASS', self.config.auth_password)

                # Create authenticated handler
                def handler_factory(*args, **kwargs):
                    return AuthenticatedMetricsHandler(
                        self.registry, auth_username, auth_password, *args, **kwargs
                    )

                # Start authenticated server
                server = HTTPServer(('0.0.0.0', self.config.metrics_port), handler_factory)
                server_thread = threading.Thread(target=server.serve_forever, daemon=True)
                server_thread.start()

                logger.info(f"🔐 Authenticated metrics server started on port {self.config.metrics_port}")
                logger.info(f"   Username: {auth_username}")
                logger.info(f"   Access: http://localhost:{self.config.metrics_port}/metrics")
            else:
                # Fallback to basic server (not recommended for production)
                start_http_server(self.config.metrics_port, registry=self.registry)
                logger.warning(f"⚠️ Unauthenticated metrics server started on port {self.config.metrics_port}")

        except Exception as e:
            logger.error(f"Failed to start metrics server: {e}")
            # Fallback to basic server
            try:
                start_http_server(self.config.metrics_port, registry=self.registry)
                logger.warning(f"Fallback: Basic metrics server started on port {self.config.metrics_port}")
            except Exception as fallback_error:
                logger.error(f"Failed to start fallback metrics server: {fallback_error}")
    
    def stop_collection(self):
        """Stop metrics collection"""
        self.collection_active = False
        if self.collection_thread:
            self.collection_thread.join(timeout=5)
    
    def _collection_loop(self):
        """Background metrics collection loop"""
        while self.collection_active:
            try:
                if self.config.enable_system_metrics:
                    self.collect_system_metrics()
                
                if self.config.enable_gpu_metrics:
                    self.collect_gpu_metrics()
                
                time.sleep(self.config.collection_interval)
                
            except Exception as e:
                logger.error(f"Error in metrics collection loop: {e}")
                time.sleep(1)
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get current metrics summary"""
        summary = {
            "system": {
                "cpu_usage": psutil.cpu_percent(),
                "memory_usage": psutil.virtual_memory().percent,
                "disk_usage": psutil.disk_usage('/').percent
            },
            "collection_active": self.collection_active,
            "gpu_available": GPU_MONITORING_AVAILABLE,
            "nvidia_ml_available": NVIDIA_ML_AVAILABLE
        }
        
        if GPU_MONITORING_AVAILABLE and torch.cuda.is_available():
            summary["gpu"] = {
                "device_count": torch.cuda.device_count(),
                "current_device": torch.cuda.current_device()
            }
        
        return summary


# Global metrics collector instance
_metrics_collector: Optional[ProductionMetricsCollector] = None

def get_metrics_collector(config: MetricsConfig = None) -> ProductionMetricsCollector:
    """Get or create global metrics collector"""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = ProductionMetricsCollector(config)
    return _metrics_collector

def start_metrics_collection(config: MetricsConfig = None):
    """Start global metrics collection"""
    collector = get_metrics_collector(config)
    collector.start_collection()
    return collector
