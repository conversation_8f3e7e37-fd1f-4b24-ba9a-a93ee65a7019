"""Task Coordinator for Multi-Agent System.

This module provides task coordination and management for the multi-agent system.
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """Task priority enumeration."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class Task:
    """Task data structure."""
    task_id: str
    task_type: str
    data: Dict[str, Any]
    status: TaskStatus = TaskStatus.PENDING
    priority: TaskPriority = TaskPriority.NORMAL
    required_capabilities: List[str] = None
    assigned_agents: List[str] = None
    created_at: float = None
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

    def __post_init__(self):
        if self.required_capabilities is None:
            self.required_capabilities = []
        if self.assigned_agents is None:
            self.assigned_agents = []
        if self.created_at is None:
            self.created_at = time.time()


class TaskCoordinator:
    """Coordinates tasks across multiple agents."""

    def __init__(self, agent_registry=None):
        """Initialize the task coordinator.
        
        Args:
            agent_registry: The agent registry instance for agent management
        """
        self.agent_registry = agent_registry
        self.tasks: Dict[str, Task] = {}
        self.task_queue: List[str] = []
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.max_concurrent_tasks = 10
        self.task_timeout = 300  # 5 minutes default timeout
        self.performance_metrics = {
            "tasks_completed": 0,
            "tasks_failed": 0,
            "average_execution_time": 0.0,
            "total_execution_time": 0.0
        }
        self._lock = asyncio.Lock()
        self._running = False
        # Executor callback for role-based step execution (set via set_executor)
        self._executor = None

    async def start(self):
        """Start the task coordinator."""
        self._running = True
        logger.info("Task coordinator started")

    def set_executor(self, executor):
        """Set the async executor used to run role steps.
        The executor must be an awaitable callable: (role: str, text: str) -> str
        """
        self._executor = executor

    async def coordinate_task(self, task_type: str, query: str, session_id: str, steps: List[str]):
        """Execute a sequential coordination pattern using the configured executor."""
        if not callable(self._executor):
            return {"status": "error", "message": "Step executor not set"}
        outputs: List[Dict[str, Any]] = []
        current_text = query
        for role in steps or []:
            try:
                result = await self._executor(role, current_text)
            except Exception as e:
                result = f"[{role}] error: {e}"
            outputs.append({"role": role, "output": result})
            current_text = result or current_text
        return {
            "status": "ok",
            "session_id": session_id,
            "pattern": "sequential",
            "steps": outputs,
            "final": current_text,
        }

    async def coordinate_task_concurrent(self, query: str, session_id: str, researcher_count: int = 2):
        """Execute a concurrent coordination pattern with N researchers in parallel."""
        if not callable(self._executor):
            return {"status": "error", "message": "Step executor not set"}
        researcher_count = max(1, int(researcher_count or 1))
        tasks = [asyncio.create_task(self._executor(f"researcher-{i+1}", query)) for i in range(researcher_count)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        outputs: List[Dict[str, Any]] = []
        for i, res in enumerate(results):
            role = f"researcher-{i+1}"
            if isinstance(res, Exception):
                outputs.append({"role": role, "output": f"[{role}] error: {res}"})
            else:
                outputs.append({"role": role, "output": res})
        return {
            "status": "ok",
            "session_id": session_id,
            "pattern": "concurrent",
            "steps": outputs,
        }

    async def coordinate_task_hierarchical(self, query: str, session_id: str, max_subtasks: int = 3):
        """Execute a simple hierarchical pattern: planner -> researchers -> reviewer."""
        if not callable(self._executor):
            return {"status": "error", "message": "Step executor not set"}
        # Planner proposes plan
        planner_prompt = f"Plan up to {max_subtasks} actionable subtasks for: {query}"
        planner_out = await self._executor("planner", planner_prompt)
        # Run a single researcher step as a safe default (parsing subtasks robustly is out of scope here)
        researcher_out = await self._executor("researcher", planner_out or query)
        # Reviewer summarizes
        reviewer_out = await self._executor("reviewer", researcher_out or planner_out or query)
        return {
            "status": "ok",
            "session_id": session_id,
            "pattern": "hierarchical",
            "steps": [
                {"role": "planner", "output": planner_out},
                {"role": "researcher", "output": researcher_out},
                {"role": "reviewer", "output": reviewer_out},
            ],
            "final": reviewer_out,
        }

    async def stop(self):
        """Stop the task coordinator."""
        self._running = False
        
        # Cancel all running tasks
        for task_id, task in self.running_tasks.items():
            if not task.done():
                task.cancel()
                logger.info(f"Cancelled task {task_id}")
        
        # Wait for tasks to complete
        if self.running_tasks:
            await asyncio.gather(*self.running_tasks.values(), return_exceptions=True)
        
        logger.info("Task coordinator stopped")

    async def create_task(
        self,
        task_type: str,
        data: Dict[str, Any],
        priority: TaskPriority = TaskPriority.NORMAL,
        required_capabilities: List[str] = None,
        timeout: Optional[float] = None
    ) -> str:
        """Create a new task.
        
        Args:
            task_type: Type of task to create
            data: Task data
            priority: Task priority
            required_capabilities: Required agent capabilities
            timeout: Task timeout in seconds
            
        Returns:
            Task ID
        """
        task_id = str(uuid.uuid4())
        
        task = Task(
            task_id=task_id,
            task_type=task_type,
            data=data,
            priority=priority,
            required_capabilities=required_capabilities or []
        )
        
        async with self._lock:
            self.tasks[task_id] = task
            self._add_to_queue(task_id)
        
        logger.info(f"Created task {task_id} of type {task_type}")
        
        # Try to execute immediately if possible
        await self._process_queue()
        
        return task_id

    def _add_to_queue(self, task_id: str):
        """Add task to queue based on priority."""
        task = self.tasks[task_id]
        
        # Insert based on priority (higher priority first)
        inserted = False
        for i, queued_task_id in enumerate(self.task_queue):
            queued_task = self.tasks[queued_task_id]
            if task.priority.value > queued_task.priority.value:
                self.task_queue.insert(i, task_id)
                inserted = True
                break
        
        if not inserted:
            self.task_queue.append(task_id)

    async def _process_queue(self):
        """Process the task queue."""
        if not self._running:
            return
        
        async with self._lock:
            while (len(self.running_tasks) < self.max_concurrent_tasks and 
                   self.task_queue):
                
                task_id = self.task_queue.pop(0)
                task = self.tasks[task_id]
                
                # Find suitable agents
                suitable_agents = await self._find_suitable_agents(task)
                
                if suitable_agents:
                    # Start task execution
                    task.status = TaskStatus.RUNNING
                    task.started_at = time.time()
                    task.assigned_agents = suitable_agents[:1]  # Assign first suitable agent
                    
                    # Create async task for execution
                    async_task = asyncio.create_task(
                        self._execute_task(task)
                    )
                    self.running_tasks[task_id] = async_task
                    
                    logger.info(f"Started executing task {task_id}")
                else:
                    # No suitable agents, put back in queue
                    self.task_queue.append(task_id)
                    logger.warning(f"No suitable agents for task {task_id}")
                    break

    async def _find_suitable_agents(self, task: Task) -> List[str]:
        """Find agents suitable for the task.
        
        Args:
            task: Task to find agents for
            
        Returns:
            List of suitable agent IDs
        """
        if not self.agent_registry:
            # Return mock agents for testing
            return ["default_agent"]
        
        try:
            # Get available agents from registry
            available_agents = await self.agent_registry.get_available_agents()
            
            suitable_agents = []
            for agent_id, agent_info in available_agents.items():
                agent_capabilities = agent_info.get("capabilities", [])
                
                # Check if agent has required capabilities
                if all(cap in agent_capabilities for cap in task.required_capabilities):
                    suitable_agents.append(agent_id)
            
            return suitable_agents
            
        except Exception as e:
            logger.error(f"Error finding suitable agents: {e}")
            return ["fallback_agent"]

    async def _execute_task(self, task: Task):
        """Execute a task.
        
        Args:
            task: Task to execute
        """
        try:
            # Simulate task execution
            await asyncio.sleep(0.1)  # Simulate processing time
            
            # Create result based on task type
            if task.task_type == "coordination":
                result = await self._execute_coordination_task(task)
            elif task.task_type == "analysis":
                result = await self._execute_analysis_task(task)
            else:
                result = await self._execute_generic_task(task)
            
            # Mark task as completed
            task.status = TaskStatus.COMPLETED
            task.completed_at = time.time()
            task.result = result
            
            # Update metrics
            execution_time = task.completed_at - task.started_at
            self._update_metrics(execution_time, success=True)
            
            logger.info(f"Task {task.task_id} completed successfully")
            
        except asyncio.CancelledError:
            task.status = TaskStatus.CANCELLED
            logger.info(f"Task {task.task_id} was cancelled")
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = str(e)
            task.completed_at = time.time()
            
            # Update metrics
            if task.started_at:
                execution_time = task.completed_at - task.started_at
                self._update_metrics(execution_time, success=False)
            
            logger.error(f"Task {task.task_id} failed: {e}")
            
        finally:
            # Remove from running tasks
            async with self._lock:
                if task.task_id in self.running_tasks:
                    del self.running_tasks[task.task_id]
            
            # Process queue for next tasks
            await self._process_queue()

    async def _execute_coordination_task(self, task: Task) -> Dict[str, Any]:
        """Execute a coordination task."""
        coordination_type = task.data.get("coordination_type", "sequential")
        
        if coordination_type == "sequential":
            return await self._coordinate_sequential(task)
        elif coordination_type == "concurrent":
            return await self._coordinate_concurrent(task)
        elif coordination_type == "hierarchical":
            return await self._coordinate_hierarchical(task)
        else:
            return {"message": f"Coordination task completed: {coordination_type}"}

    async def _coordinate_sequential(self, task: Task) -> Dict[str, Any]:
        """Execute sequential coordination."""
        steps = task.data.get("steps", ["step1", "step2", "step3"])
        results = []
        
        for step in steps:
            await asyncio.sleep(0.02)  # Simulate step execution
            results.append({
                "step": step,
                "result": f"Completed {step}",
                "timestamp": time.time()
            })
        
        return {
            "coordination_type": "sequential",
            "steps_completed": len(steps),
            "results": results,
            "success": True
        }

    async def _coordinate_concurrent(self, task: Task) -> Dict[str, Any]:
        """Execute concurrent coordination."""
        agents = task.data.get("agents", ["agent1", "agent2", "agent3"])
        
        # Simulate concurrent execution
        async def execute_agent(agent_id):
            await asyncio.sleep(0.03)  # Simulate agent work
            return {
                "agent_id": agent_id,
                "result": f"Agent {agent_id} completed task",
                "timestamp": time.time()
            }
        
        # Run agents concurrently
        agent_tasks = [execute_agent(agent_id) for agent_id in agents]
        results = await asyncio.gather(*agent_tasks)
        
        return {
            "coordination_type": "concurrent",
            "agents_used": len(agents),
            "results": results,
            "success": True
        }

    async def _coordinate_hierarchical(self, task: Task) -> Dict[str, Any]:
        """Execute hierarchical coordination."""
        max_subtasks = task.data.get("max_subtasks", 3)
        
        # Simulate hierarchical breakdown
        subtasks = []
        for i in range(max_subtasks):
            await asyncio.sleep(0.01)  # Simulate subtask creation
            subtasks.append({
                "subtask_id": f"subtask_{i}",
                "description": f"Subtask {i} of hierarchical coordination",
                "status": "completed",
                "result": f"Subtask {i} result"
            })
        
        return {
            "coordination_type": "hierarchical",
            "subtasks_created": len(subtasks),
            "subtasks": subtasks,
            "success": True
        }

    async def _execute_analysis_task(self, task: Task) -> Dict[str, Any]:
        """Execute an analysis task."""
        analysis_type = task.data.get("analysis_type", "general")
        
        await asyncio.sleep(0.05)  # Simulate analysis
        
        return {
            "analysis_type": analysis_type,
            "result": f"Analysis completed for {analysis_type}",
            "confidence": 0.85,
            "timestamp": time.time()
        }

    async def _execute_generic_task(self, task: Task) -> Dict[str, Any]:
        """Execute a generic task."""
        await asyncio.sleep(0.02)  # Simulate generic processing
        
        return {
            "task_type": task.task_type,
            "result": f"Generic task {task.task_type} completed",
            "data_processed": len(str(task.data)),
            "timestamp": time.time()
        }

    def _update_metrics(self, execution_time: float, success: bool):
        """Update performance metrics."""
        if success:
            self.performance_metrics["tasks_completed"] += 1
        else:
            self.performance_metrics["tasks_failed"] += 1
        
        # Update average execution time
        total_tasks = (self.performance_metrics["tasks_completed"] + 
                      self.performance_metrics["tasks_failed"])
        
        self.performance_metrics["total_execution_time"] += execution_time
        self.performance_metrics["average_execution_time"] = (
            self.performance_metrics["total_execution_time"] / total_tasks
        )

    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task status.
        
        Args:
            task_id: Task ID
            
        Returns:
            Task status information
        """
        if task_id not in self.tasks:
            return None
        
        task = self.tasks[task_id]
        
        return {
            "task_id": task.task_id,
            "task_type": task.task_type,
            "status": task.status.value,
            "priority": task.priority.value,
            "created_at": task.created_at,
            "started_at": task.started_at,
            "completed_at": task.completed_at,
            "assigned_agents": task.assigned_agents,
            "result": task.result,
            "error": task.error
        }

    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a task.
        
        Args:
            task_id: Task ID to cancel
            
        Returns:
            True if cancelled successfully
        """
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        
        if task.status == TaskStatus.PENDING:
            # Remove from queue
            async with self._lock:
                if task_id in self.task_queue:
                    self.task_queue.remove(task_id)
                task.status = TaskStatus.CANCELLED
            return True
        
        elif task.status == TaskStatus.RUNNING:
            # Cancel running task
            if task_id in self.running_tasks:
                self.running_tasks[task_id].cancel()
                return True
        
        return False

    def get_metrics(self) -> Dict[str, Any]:
        """Get performance metrics.
        
        Returns:
            Performance metrics
        """
        return {
            **self.performance_metrics,
            "active_tasks": len(self.running_tasks),
            "queued_tasks": len(self.task_queue),
            "total_tasks": len(self.tasks)
        }

    def get_queue_status(self) -> Dict[str, Any]:
        """Get queue status.
        
        Returns:
            Queue status information
        """
        return {
            "queued_tasks": len(self.task_queue),
            "running_tasks": len(self.running_tasks),
            "max_concurrent": self.max_concurrent_tasks,
            "queue": [
                {
                    "task_id": task_id,
                    "task_type": self.tasks[task_id].task_type,
                    "priority": self.tasks[task_id].priority.value
                }
                for task_id in self.task_queue[:10]  # Show first 10
            ]
        }