---
description: Repository Information Overview
alwaysApply: true
---

# Repository Information Overview

## Repository Summary
This repository contains a multi-agent AI system with a Python backend and React/Next.js frontend. The project appears to be focused on intelligent agent coordination, neural swarms, and multi-agent chat sessions.

## Repository Structure
- **src/**: Core Python backend code with agent coordination, swarms, and integration components
- **ii-agent/**: Main project with backend server, frontend UI, and Docker configuration
- **models/**: Neural agent models for different roles (researcher, coder, analyst, etc.)
- **docs/**: Documentation for different phases of integration
- **tests/**: Test files for various components and integration scenarios
- **monitoring/**: Monitoring setup with Grafana and Prometheus

### Main Repository Components
- **Backend Server**: FastAPI-based WebSocket server for agent communication
- **Frontend UI**: Next.js application with React components
- **Neural Swarms**: Coordination system for multiple AI agents
- **Docker Infrastructure**: Multi-container setup for deployment

## Projects

### Backend (Python)
**Configuration File**: pyproject.toml

#### Language & Runtime
**Language**: Python
**Version**: >=3.10
**Build System**: Hatchling
**Package Manager**: uv (Python package manager)

#### Dependencies
**Main Dependencies**:
- anthropic[vertex] (>=0.50.0)
- fastapi (>=0.115.12)
- google-cloud-aiplatform (>=1.90.0)
- openai (>=1.68.2)
- torch (>=2.0.0)
- uvicorn[standard] (>=0.29.0)
- docker (>=7.1.0)

**Development Dependencies**:
- pytest-asyncio (>=1.0.0)
- pytest (>=8.3.5)

#### Build & Installation
```bash
uv sync --locked
```

#### Docker
**Dockerfile**: docker/backend/Dockerfile
**Image**: Python 3.12 with uv package manager
**Configuration**: Multi-container setup with nginx, frontend, backend, and sandbox services

#### Testing
**Framework**: pytest
**Test Location**: tests/ directory
**Run Command**:
```bash
pytest tests/
```

### Frontend (Next.js)
**Configuration File**: frontend/package.json

#### Language & Runtime
**Language**: TypeScript/JavaScript
**Version**: Node.js (not specified)
**Build System**: Next.js
**Package Manager**: npm/yarn

#### Dependencies
**Main Dependencies**:
- next (^15.3.3)
- react (^19.0.0)
- react-dom (^19.0.0)
- @monaco-editor/react (^4.7.0)
- framer-motion (^12.4.7)

**Development Dependencies**:
- typescript (^5)
- eslint (^9)
- tailwindcss (^4)

#### Build & Installation
```bash
npm install
npm run build
```

#### Docker
**Dockerfile**: docker/frontend/Dockerfile
**Configuration**: Served through nginx proxy

## Usage & Operations
The system can be started using Docker Compose:
```bash
docker-compose up
```

For development:
```bash
# Backend
python ws_server.py

# Frontend
cd frontend
npm run dev
```