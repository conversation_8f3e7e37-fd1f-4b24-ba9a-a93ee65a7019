"""Multi-Agent Chat Session - Original ii-agent compatible implementation.

This module provides a drop-in replacement for the original ChatSession
that matches the exact patterns and models from the official ii-agent repository.

Repository: https://github.com/Intelligent-Internet/ii-agent
Updated: 2025-01-09 19:10:00 UTC
"""

import asyncio
import json
import logging
import uuid
from pathlib import Path
from typing import Dict, Any, List, Optional

from fastapi import WebSocket, WebSocketDisconnect
from starlette.websockets import WebSocketState
from pydantic import BaseModel, ValidationError, Field

logger = logging.getLogger(__name__)

# Message models that exactly match the original ii-agent repository
try:
    # Try importing from original ii-agent models first
    from ii_agent.server.models.messages import (
        WebSocketMessage,
        QueryContent,
        InitAgentContent,
    )
    from ii_agent.core.event import RealtimeEvent, EventType
    logger.info("✅ Using original ii-agent message models")
    USING_ORIGINAL_MODELS = True
    
except ImportError as e:
    logger.warning(f"⚠️ Original models not available, using compatible fallbacks: {e}")
    # Exact replicas of original ii-agent models based on repository analysis
    
    class WebSocketMessage(BaseModel):
        """Base model for WebSocket messages - matches original ii-agent."""
        type: str
        content: Dict[str, Any] = {}

    class QueryContent(BaseModel):
        """Model for query message content - matches original ii-agent."""
        text: str = ""
        resume: bool = False
        files: List[str] = []

    class InitAgentContent(BaseModel):
        """Model for agent initialization content - matches original ii-agent."""
        model_name: str
        tool_args: Dict[str, Any] = {}
        thinking_tokens: int = 0
    
    # Event models matching original
    from enum import Enum
    class EventType(str, Enum):
        CONNECTION_ESTABLISHED = "connection_established"
        AGENT_INITIALIZED = "agent_initialized"
        WORKSPACE_INFO = "workspace_info"
        PROCESSING = "processing"
        AGENT_THINKING = "agent_thinking"
        TOOL_CALL = "tool_call"
        TOOL_RESULT = "tool_result"
        AGENT_RESPONSE = "agent_response"
        STREAM_COMPLETE = "stream_complete"
        ERROR = "error"
        SYSTEM = "system"
        PONG = "pong"
        USER_MESSAGE = "user_message"
        COORDINATION_INFO = "coordination_info"
        
    class RealtimeEvent(BaseModel):
        type: EventType
        content: Dict[str, Any]
    
    USING_ORIGINAL_MODELS = False

# OrchestrationDecision removed - using tool-based coordination approach

# Multi-agent components with fallbacks
try:
    from .agents.agent_registry import AgentRegistry
    from .agents.task_coordinator import TaskCoordinator
    from .swarms.swarms_integration import SwarmsFrameworkIntegration
    MULTI_AGENT_AVAILABLE = True
    logger.info("✅ Multi-agent components loaded successfully")
except ImportError as e:
    logger.warning(f"⚠️ Multi-agent components not available: {e}")
    # Create fallback classes that match the expected interface
    class AgentRegistry:
        def __init__(self): 
            self.agents = {}
            logger.info("🔄 FALLBACK: AgentRegistry initialized")
        async def register_agent(self, agent_id, agent_name=None, capabilities=None, role=None, **kwargs): 
            logger.info(f"🔄 FALLBACK: Registering agent {agent_id}")
            return True
        async def start(self): 
            logger.info("🔄 FALLBACK: AgentRegistry.start() called")
        async def stop(self): pass
    
    class TaskCoordinator:
        def __init__(self): 
            logger.info("🔄 FALLBACK: TaskCoordinator initialized")
        async def coordinate_task(self, task_type, **kwargs): 
            logger.info(f"🔄 FALLBACK: Coordinating task {task_type}")
            return {"status": "completed", "message": "Fallback coordination"}
        async def start(self): pass
        async def stop(self): pass
    
    class SwarmsFrameworkIntegration:
        def __init__(self): 
            logger.info("🔄 FALLBACK: SwarmsFrameworkIntegration initialized")
        async def start(self): pass
        async def stop(self): pass
    
    MULTI_AGENT_AVAILABLE = False


class MultiAgentChatSession:
    """Drop-in multi-agent chat session that follows original ii-agent patterns."""

    def __init__(
        self,
        websocket: WebSocket,
        session_uuid: uuid.UUID,
        file_store=None,
        config=None,
    ):
        self.websocket = websocket
        self.session_uuid = session_uuid
        self.file_store = file_store
        self.config = config
        
        # Real agent state (like original ChatSession)
        self.agent = None
        self.message_processor: Optional[asyncio.Task] = None
        
        # Multi-agent components (fallback)
        self.agent_registry = AgentRegistry()
        # Pass the registry to TaskCoordinator to satisfy its required constructor argument
        try:
            self.task_coordinator = TaskCoordinator(self.agent_registry)
        except TypeError:
            # Fallback compatibility if a shim TaskCoordinator with no-arg __init__ is in use
            self.task_coordinator = TaskCoordinator()
        # Initialize swarms integration with coordination hub (task coordinator)
        try:
            self.swarms_integration = SwarmsFrameworkIntegration(self.task_coordinator)
        except TypeError:
            # Fallback compatibility: allow zero-arg constructor if available
            self.swarms_integration = SwarmsFrameworkIntegration()
        
        # Session state
        self.active_task: Optional[asyncio.Task] = None
        self.first_message = True
        self.is_active = True
        
        logger.info(f"🧠 MultiAgentChatSession initialized for session {session_uuid}")

    async def send_event(self, event: RealtimeEvent):
        """Send an event to the client via WebSocket - matches original pattern."""
        ws = self.websocket
        if not ws:
            return
        try:
            # Avoid sending after close
            state = getattr(ws, "application_state", None)
            client_state = getattr(ws, "client_state", None)
            if (
                state == WebSocketState.DISCONNECTED
                or client_state == WebSocketState.DISCONNECTED
            ):
                return
            await ws.send_json(event.model_dump())
        except WebSocketDisconnect:
            # Mark closed so future sends are skipped
            logger.info("Client disconnected while sending event")
            # Don't set websocket to None here - let the main loop handle it
        except RuntimeError as e:
            # Starlette raises RuntimeError if send attempted after close/response completed
            logger.info(f"WebSocket runtime error while sending (likely closed): {e}")
            # Don't set websocket to None here - let the main loop handle it
        except Exception as e:
            logger.error(f"Error sending event to client: {e}")

    async def start_chat_loop(self):
        """Start the chat loop for this session - matches original pattern exactly."""
        logger.info(f"🚀 Starting multi-agent chat session {self.session_uuid}")
        
        # Initial WebSocket validation
        if self.websocket is None:
            logger.error("❌ WebSocket is None at start of chat loop!")
            return
            
        logger.info(f"📡 Initial WebSocket state: {getattr(self.websocket, 'client_state', 'UNKNOWN')}")
        
        try:
            # Send initial handshake
            logger.info("🤝 Starting handshake...")
            await self.handshake()
            
            # Check if websocket is still valid after handshake
            logger.info(f"📡 WebSocket state after handshake: {getattr(self.websocket, 'client_state', 'UNKNOWN') if self.websocket else 'None'}")
            if self.websocket is None or self.websocket.client_state == WebSocketState.DISCONNECTED:
                logger.warning("⚠️ WebSocket disconnected during handshake, ending session")
                return
                
            # Initialize multi-agent components
            logger.info("🧠 Initializing multi-agent components...")
            await self.agent_registry.start()
            await self.task_coordinator.start()
            await self.swarms_integration.start()
            
            # Send Swarms status to UI (only if still connected)
            if self.websocket and self.websocket.client_state != WebSocketState.DISCONNECTED:
                await self.send_event(
                    RealtimeEvent(
                        type=EventType.SYSTEM,
                        content={
                            "message": "🐝 Swarms Framework initialized and ready for coordination",
                            "swarms_status": "active",
                            "coordination_patterns": ["sequential", "concurrent", "hierarchical"],
                        },
                    )
                )
                
                logger.info("🚀 Multi-agent chat loop started successfully")
                logger.info("🐝 Swarms coordination patterns available")
            
            # Main message loop with robust error handling
            while self.is_active:
                try:
                    # CRITICAL FIX: Check application_state like FastAPI docs show
                    if (self.websocket is None or 
                        not hasattr(self.websocket, 'application_state') or
                        self.websocket.application_state != WebSocketState.CONNECTED):
                        logger.info("WebSocket not in CONNECTED state, ending chat loop")
                        break

                    # Also check client_state for extra safety
                    state = getattr(self.websocket, 'client_state', None)
                    if state == WebSocketState.DISCONNECTED:
                        logger.info("WebSocket client disconnected, ending chat loop")
                        break

                    # Snapshot websocket to avoid race with external cleanup
                    ws = self.websocket
                    if ws is None:
                        logger.info("WebSocket became None before receive, ending chat loop")
                        break
                    # Use receive_text safely with timeout to avoid stuck loops
                    message_text = await asyncio.wait_for(ws.receive_text(), timeout=60.0)
                    if not message_text:
                        continue
                    message_data = json.loads(message_text)
                    await self.handle_message(message_data)
                    
                except asyncio.TimeoutError:
                    # Periodic liveness check; continue loop if still connected
                    continue
                except WebSocketDisconnect:
                    logger.info("Client disconnected via WebSocketDisconnect")
                    break
                except json.JSONDecodeError as e:
                    logger.warning(f"Invalid JSON received: {e}")
                    await self.send_event(
                        RealtimeEvent(
                            type=EventType.ERROR,
                            content={"message": "Invalid JSON format"},
                        )
                    )
                except Exception as e:
                    logger.error(f"Error in message loop: {e}")
                    break
                    
        except WebSocketDisconnect:
            logger.info("Client disconnected during initialization")
        except Exception as e:
            logger.error(f"Error in chat loop: {e}")
        finally:
            # Wait for active task to complete before cleanup
            if hasattr(self, 'active_task') and self.active_task and not self.active_task.done():
                try:
                    await asyncio.wait_for(self.active_task, timeout=5.0)
                except asyncio.TimeoutError:
                    logger.warning("Active task did not complete within timeout, cancelling")
                    self.active_task.cancel()
                except asyncio.CancelledError:
                    logger.info("Active task was cancelled")
                except Exception as e:
                    logger.error(f"Error waiting for active task completion: {e}")
                    
            self.cleanup()
            logger.info(f"Multi-agent chat session {self.session_uuid} ended")

    async def handshake(self):
        """Handle handshake message - matches original pattern exactly."""
        logger.info(f"🤝 Starting handshake for session {self.session_uuid}")
        
        # Check WebSocket state before handshake
        if self.websocket is None:
            logger.error("❌ WebSocket is None before handshake!")
            return
            
        logger.info(f"📡 WebSocket state before handshake: {getattr(self.websocket, 'client_state', 'UNKNOWN')}")
        
        try:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.CONNECTION_ESTABLISHED,
                    content={
                        "message": "Connected to Agent WebSocket Server",
                        "workspace_path": f"/workspace/{self.session_uuid}",
                        "multi_agent_mode": True,
                    },
                )
            )
            logger.info("✅ Handshake event sent successfully")
        except Exception as e:
            logger.error(f"❌ Handshake failed: {e}")
            # Check if WebSocket became None during send_event
            if self.websocket is None:
                logger.error("💀 WebSocket became None during handshake send_event!")
            raise

    async def handle_message(self, message_data: dict):
        """Handle incoming WebSocket messages - matches original pattern exactly."""
        try:
            # Validate message structure exactly like original
            ws_message = WebSocketMessage(**message_data)
            msg_type = ws_message.type
            content = ws_message.content

            # Route to appropriate handler - exactly like original
            handlers = {
                "init_agent": self._handle_init_agent,
                "query": self._handle_query,
                "workspace_info": self._handle_workspace_info,
                "ping": self._handle_ping,
                "cancel": self._handle_cancel,
                "edit_query": self._handle_edit_query,
                "enhance_prompt": self._handle_enhance_prompt,
                "review_result": self._handle_review_result,
            }

            handler = handlers.get(msg_type)
            if handler:
                await handler(content)
            else:
                await self.send_event(
                    RealtimeEvent(
                        type=EventType.ERROR,
                        content={"message": f"Unknown message type: {msg_type}"},
                    )
                )

        except ValidationError as e:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": f"Invalid message format: {str(e)}"},
                )
            )
        except Exception as e:
            logger.error(f"Error handling message: {str(e)}")
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": f"Error processing request: {str(e)}"},
                )
            )

    async def handle_multi_agent_message(self, payload: dict):
        """Legacy method - redirects to handle_message for compatibility."""
        await self.handle_message(payload)

    async def _handle_init_agent(self, content: dict):
        """Handle agent initialization - creates real FunctionCallAgent like original."""
        try:
            init_content = InitAgentContent(**content)
            
            # Always try to create real LLM agent first
            logger.info(f"🎯 Attempting to create real FunctionCallAgent for model: {init_content.model_name}")
            
            try:
                from ii_agent.core.storage.settings.file_settings_store import FileSettingsStore
                from ii_agent.llm import get_client
                from ii_agent.utils.workspace_manager import WorkspaceManager
                from ii_agent.utils.sandbox_manager import SandboxManager
                from ii_agent.core.config.client_config import ClientConfig
                from ii_agent.db.manager import Sessions
                
                # Create LLM client using factory
                user_id = None  # TODO: Support user id
                settings_store = await FileSettingsStore.get_instance(self.config, user_id)
                settings = await settings_store.load()
                llm_config = settings.llm_configs.get(init_content.model_name)
                
                # If requested model not found, try to use available Gemini models
                if not llm_config:
                    available_models = list(settings.llm_configs.keys())
                    gemini_models = [m for m in available_models if 'gemini' in m.lower()]
                    
                    if gemini_models:
                        logger.warning(f"⚠️ Model '{init_content.model_name}' not found, using available Gemini model: {gemini_models[0]}")
                        init_content.model_name = gemini_models[0]
                        llm_config = settings.llm_configs.get(init_content.model_name)
                    
                    if not llm_config:
                        raise ValueError(f"LLM config not found for model: {init_content.model_name}. Available models: {available_models}")

                llm_config.thinking_tokens = init_content.thinking_tokens
                client = get_client(llm_config)

                # Create workspace manager
                from pathlib import Path
                workspace_path = Path(self.config.workspace_root).resolve()
                workspace_manager = WorkspaceManager(
                    parent_dir=workspace_path,
                    session_id=str(self.session_uuid),
                    settings=settings,
                )
                
                # Check and create database session
                device_id = self.websocket.query_params.get("device_id")
                session_id = workspace_manager.session_id
                existing_session = Sessions.get_session_by_id(session_id)
                if existing_session:
                    logger.info(f"Found existing session {session_id} with workspace at {existing_session.workspace_dir}")
                else:
                    # Create new session if it doesn't exist
                    Sessions.create_session(
                        device_id=device_id,
                        session_uuid=session_id,
                        workspace_path=workspace_manager.root,
                    )
                    logger.info(f"Created new session {session_id} with workspace at {workspace_manager.root}")

                sandbox_manager = SandboxManager(session_id=self.session_uuid, settings=settings)
                if self.websocket.query_params.get("session_uuid") is None:
                    await sandbox_manager.start_sandbox()
                else:
                    # WIP
                    await sandbox_manager.connect_sandbox()

                # Update Config for client
                client_config = ClientConfig(
                    server_url=sandbox_manager.get_host_url(),
                    cwd=str(workspace_manager.root.absolute()),
                )
                settings.client_config = client_config

                # Create agent using internal methods (copied from original ChatSession)
                self.agent = self._create_agent(
                    client,
                    workspace_manager,
                    sandbox_manager,
                    self.websocket,
                    init_content.tool_args,
                    self.file_store,
                    settings=settings,
                )

                # Start message processor for this session
                self.message_processor = self.agent.start_message_processing()
                
                logger.info(f"✅ Real FunctionCallAgent created successfully for session {self.session_uuid}")

                # Attach a simple sequential step executor to TaskCoordinator
                async def step_executor(payload: dict) -> str:
                    role = payload.get("role", "agent")
                    text = payload.get("text", "")
                    try:
                        # Prefer real agent if available
                        if self.agent is not None and hasattr(self.agent, "run_step"):
                            return await self.agent.run_step(role, text)
                        elif self.agent is not None:
                            # Role-specific behaviors guided through the real agent when possible
                            if role.lower().startswith("planner"):
                                role_prompt = (
                                    "[PLANNER] Create 2-3 concise subgoals to address the request. "
                                    "Return bullets (•) only.\n\nRequest:\n" + text
                                )
                                await self.agent.run_agent_async(role_prompt, files=[], resume=False)
                                # Extract last assistant text response from MessageHistory
                                try:
                                    if hasattr(self.agent, 'history') and self.agent.history:
                                        # Use the correct method to get last response
                                        if hasattr(self.agent.history, 'get_last_assistant_text_response'):
                                            last_text = self.agent.history.get_last_assistant_text_response()
                                            if last_text:
                                                return last_text
                                        elif hasattr(self.agent.history, '_message_lists'):
                                            # Fallback: get from message lists directly
                                            for msg_list in reversed(self.agent.history._message_lists):
                                                for msg in reversed(msg_list):
                                                    if hasattr(msg, 'role') and msg.role == 'assistant':
                                                        return getattr(msg, 'content', str(msg))
                                except Exception:
                                    pass
                                return role_prompt
                            elif role.lower().startswith("researcher"):
                                role_prompt = (
                                    "[RESEARCHER] Research briefly and summarize with 2 sources (URLs). "
                                    "If web tools unavailable, produce best-effort summary.\n\nTopic:\n" + text
                                )
                                await self.agent.run_agent_async(role_prompt, files=[], resume=False)
                                try:
                                    if self.agent.history:
                                        last_text = self.agent.history.get_last_assistant_text_response()
                                        if last_text:
                                            return last_text
                                except Exception:
                                    pass
                                return role_prompt
                            elif role.lower().startswith("coder"):
                                # Write a simple notes.md to the workspace
                                try:
                                    if hasattr(self.agent, "workspace_manager") and self.agent.workspace_manager:
                                        from pathlib import Path
                                        notes_path = Path(self.agent.workspace_manager.root) / "notes.md"
                                        content = f"\n\n## Notes\n\n{text}\n"
                                        notes_path.parent.mkdir(parents=True, exist_ok=True)
                                        with open(notes_path, "a", encoding="utf-8") as f:
                                            f.write(content)
                                        return f"Wrote notes to {notes_path}"
                                except Exception as fe:
                                    # If file operation fails, fall back to LLM response
                                    role_prompt = f"[CODER] Draft file content for notes.md based on:\n{text}"
                                    await self.agent.run_agent_async(role_prompt, files=[], resume=False)
                                    try:
                                        if self.agent.history:
                                            last_text = self.agent.history.get_last_assistant_text_response()
                                            if last_text:
                                                return last_text
                                    except Exception:
                                        pass
                                    return role_prompt
                                # Fallback role prompt
                                role_prompt = f"[CODER] {text}"
                                await self.agent.run_agent_async(role_prompt, files=[], resume=False)
                                try:
                                    if self.agent.history:
                                        last_text = self.agent.history.get_last_assistant_text_response()
                                        if last_text:
                                            return last_text
                                except Exception:
                                    pass
                                return role_prompt
                            elif role.lower().startswith("reviewer"):
                                role_prompt = (
                                    "[REVIEWER] Critique briefly (1-2 points) and provide a final summary.\n\nContent:\n" + text
                                )
                                await self.agent.run_agent_async(role_prompt, files=[], resume=False)
                                try:
                                    if self.agent.history:
                                        last_text = self.agent.history.get_last_assistant_text_response()
                                        if last_text:
                                            return last_text
                                except Exception:
                                    pass
                                return role_prompt
                            else:
                                role_prompt = f"[{role.upper()}] {text}"
                                await self.agent.run_agent_async(role_prompt, files=[], resume=False)
                                try:
                                    if self.agent.history:
                                        last_text = self.agent.history.get_last_assistant_text_response()
                                        if last_text:
                                            return last_text
                                except Exception:
                                    pass
                                return role_prompt
                        else:
                            # Deterministic fallback per role
                            if role.lower().startswith("planner"):
                                return f"• Understand the task\n• Gather information\n• Produce concise deliverable based on: {text[:120]}"
                            if role.lower().startswith("researcher"):
                                return f"Summary (no web): {text[:160]}\nSources: (n/a)"
                            if role.lower().startswith("coder"):
                                return f"Drafted notes.md content based on input ({len(text)} chars)"
                            if role.lower().startswith("reviewer"):
                                return f"Reviewed content. Ready to present results."
                            return f"[{role}] {text}"
                    except Exception as e:
                        return f"[{role}] error: {e}"

                await self.task_coordinator.start()
                self.task_coordinator.set_executor(step_executor)
                
                # Set up coordination callbacks for tools
                async def coordination_callback(pattern: str, params: dict) -> dict:
                    """Callback for coordination tools to trigger multi-agent workflows."""
                    if pattern == 'sequential':
                        return await self.task_coordinator.coordinate_task(
                            task_type="tool_coordination",
                            query=params.get('query', 'Tool-requested coordination'),
                            session_id=str(self.session_uuid),
                            steps=params.get('steps', ["planner", "researcher", "coder", "reviewer"]),
                        )
                    elif pattern == 'concurrent':
                        return await self.task_coordinator.coordinate_task_concurrent(
                            query=params.get('query', 'Tool-requested coordination'),
                            session_id=str(self.session_uuid),
                            researcher_count=params.get('researcher_count', 2),
                        )
                    elif pattern == 'hierarchical':
                        return await self.task_coordinator.coordinate_task_hierarchical(
                            query=params.get('query', 'Tool-requested coordination'),
                            session_id=str(self.session_uuid),
                            max_subtasks=params.get('max_subtasks', 3),
                        )
                    return {"status": "unknown_pattern", "message": f"Unknown pattern: {pattern}"}

                async def clarification_callback(questions: list) -> None:
                    """Callback for clarification tool to pause and ask questions."""
                    q_text = "\n".join(questions) or "Please clarify your request."
                    await self.send_event(RealtimeEvent(type=EventType.SYSTEM, content={
                        "message": f"❓ Clarification needed:\n{q_text}"
                    }))
                    await self.send_event(RealtimeEvent(type=EventType.STREAM_COMPLETE, content={}))
                    self._waiting_for_clarification = True

                # Wire callbacks into the agent
                self.agent._coordination_callback = coordination_callback
                self.agent._clarification_callback = clarification_callback

                await self.send_event(
                    RealtimeEvent(
                        type=EventType.AGENT_INITIALIZED,
                        content={
                            "message": "Agent initialized with multi-agent coordination",
                            "vscode_url": sandbox_manager.expose_port(self.config.code_server_port),
                            "capabilities": ["sequential", "concurrent", "hierarchical", "clarification"],
                        },
                    )
                )
                return
                
            except Exception as real_agent_error:
                logger.error(f"❌ Failed to create real agent: {real_agent_error}")
                import traceback
                traceback.print_exc()
                
                # Fallback to multi-agent coordination
                logger.warning("🔄 Falling back to multi-agent coordination system")
                await self.agent_registry.register_agent(
                    agent_id=f"agent_{self.session_uuid}",
                    agent_name=f"MultiAgent_{init_content.model_name}",
                    capabilities=init_content.tool_args.keys(),
                    role="primary",
                    model_name=init_content.model_name,
                    thinking_tokens=init_content.thinking_tokens,
                    tool_args=init_content.tool_args,
                )

                await self.send_event(
                    RealtimeEvent(
                        type=EventType.AGENT_INITIALIZED,
                        content={
                            "message": f"Multi-agent system initialized (fallback - real agent failed: {str(real_agent_error)})",
                            "vscode_url": f"http://localhost:9000",
                        },
                    )
                )
            
        except ValidationError as e:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": f"Invalid init_agent content: {str(e)}"},
                )
            )
        except Exception as e:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": f"Error initializing agent: {str(e)}"},
                )
            )

    async def _handle_query(self, content: dict):
        """Handle query processing - matches original pattern exactly."""
        try:
            query_content = QueryContent(**content)

            # Set session name from first message (matches original exactly)
            if self.first_message and query_content.text.strip():
                session_name = query_content.text.strip()[:100]
                self.first_message = False
                logger.info(f"Session named: {session_name}")

            # Check for slash commands (matches original)
            if query_content.text.strip().startswith("/"):
                await self._handle_slash_command(query_content.text.strip())
                return

            # Check if there's an active task (matches original)
            if self.has_active_task():
                await self.send_event(
                    RealtimeEvent(
                        type=EventType.ERROR,
                        content={"message": "A query is already being processed"},
                    )
                )
                return

            # Send processing acknowledgment (matches original exactly)
            await self.send_event(
                RealtimeEvent(
                    type=EventType.PROCESSING,
                    content={"message": "Processing your request..."},
                )
            )

            # Add Swarms coordination status
            await self.send_event(
                RealtimeEvent(
                    type=EventType.SYSTEM,
                    content={
                        "message": "🐝 Swarms coordination: Analyzing query for multi-agent approach...",
                        "swarms_status": "coordinating",
                        "coordination_pattern": "sequential"
                    },
                )
            )

            # Run the agent in a separate task (matches original exactly)
            self.active_task = asyncio.create_task(
                self._run_agent_async(
                    query_content.text, query_content.resume, query_content.files
                )
            )

        except ValidationError as e:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": f"Invalid query content: {str(e)}"},
                )
            )
    
    async def _handle_slash_command(self, command: str):
        """Handle slash commands - matches original pattern."""
        await self.send_event(
            RealtimeEvent(
                type=EventType.SYSTEM,
                content={"message": f"Command '{command}' processed"},
            )
        )
        await self.send_event(
            RealtimeEvent(
                type=EventType.STREAM_COMPLETE,
                content={},
            )
        )
    
    def has_active_task(self) -> bool:
        """Check if there's an active task for this session - matches original."""
        return self.active_task is not None and not self.active_task.done()
    
    def _create_agent(
        self,
        client,
        workspace_manager,
        sandbox_manager,
        websocket: WebSocket,
        tool_args: Dict[str, Any],
        file_store,
        settings,
    ):
        """Create a new agent instance - copied from original ChatSession."""
        # Setup logging
        logger_for_agent_logs = logging.getLogger(f"agent_logs_{id(websocket)}")
        logger_for_agent_logs.setLevel(logging.DEBUG)
        logger_for_agent_logs.propagate = False

        # Ensure we don't duplicate handlers
        if not logger_for_agent_logs.handlers:
            logger_for_agent_logs.addHandler(logging.FileHandler(self.config.logs_path))
            if not self.config.minimize_stdout_logs:
                logger_for_agent_logs.addHandler(logging.StreamHandler())

        # Create context manager
        from ii_agent.llm.token_counter import TokenCounter
        from ii_agent.llm.context_manager.llm_summarizing import LLMSummarizingContextManager
        
        token_counter = TokenCounter()
        context_manager = LLMSummarizingContextManager(
            client=client,
            token_counter=token_counter,
            logger=logger_for_agent_logs,
            token_budget=self.config.token_budget,
        )

        # Create agent
        return self._create_agent_instance(
            client,
            workspace_manager,
            sandbox_manager,
            websocket,
            tool_args,
            context_manager,
            logger_for_agent_logs,
            file_store,
            settings,
        )

    def _create_agent_instance(
        self,
        client,
        workspace_manager,
        sandbox_manager,
        websocket: WebSocket,
        tool_args: Dict[str, Any],
        context_manager,
        logger,
        file_store,
        settings,
    ):
        """Create the actual agent instance - copied from original ChatSession."""
        from ii_agent.agents.function_call import FunctionCallAgent
        from ii_agent.llm.message_history import MessageHistory
        from ii_agent.prompts.system_prompt import SystemPromptBuilder
        from ii_agent.tools import get_system_tools
        
        # Initialize agent queue and tools
        session_id = workspace_manager.session_id
        queue = asyncio.Queue()

        system_prompt_builder = SystemPromptBuilder(
            workspace_manager.workspace_mode,
            tool_args.get("sequential_thinking", False),
        )
        tools = get_system_tools(
            client=client,
            workspace_manager=workspace_manager,
            sandbox_manager=sandbox_manager,
            message_queue=queue,
            system_prompt_builder=system_prompt_builder,
            settings=settings,
            tool_args=tool_args,
        )

        # try to get history from file store
        init_history = MessageHistory(context_manager)
        try:
            init_history.restore_from_session(str(session_id), file_store)
        except FileNotFoundError:
            logger.info(f"No history found for session {session_id}")

        agent = FunctionCallAgent(
            system_prompt_builder=system_prompt_builder,
            client=client,
            tools=tools,
            workspace_manager=workspace_manager,
            message_queue=queue,
            logger_for_agent_logs=logger,
            init_history=init_history,
            max_output_tokens_per_turn=self.config.max_output_tokens_per_turn,
            max_turns=self.config.max_turns,
            websocket=websocket,
        )
        
        # Store the session ID in the agent for event tracking
        agent.session_id = session_id
        return agent
    
    async def _run_agent_async(self, user_input: str, resume: bool = False, files: list = []):
        """Run the agent asynchronously - uses real agent if available."""
        try:
            # Check if we have a real agent
            if hasattr(self, 'agent') and self.agent is not None:
                logger.info(f"🎯 Running real FunctionCallAgent for query: {user_input}")
                
                # Send Swarms coordination for real agent
                await self.send_event(
                    RealtimeEvent(
                        type=EventType.SYSTEM,
                        content={
                            "message": "🐝 Swarms coordinating with Gemini Agent...",
                            "swarms_status": "coordinating_llm",
                            "agent_type": "gemini_function_call"
                        },
                    )
                )
                
                # Add user message to the event queue to save to database
                # Ensure the user message is recorded in the agent queue for DB/event tracing
                self.agent.message_queue.put_nowait(
                    RealtimeEvent(type=EventType.USER_MESSAGE, content={"text": user_input})
                )
                
                # Run the agent with the query using the new async method
                await self.agent.run_agent_async(user_input, files, resume)
                
                # Send Swarms completion for real agent
                await self.send_event(
                    RealtimeEvent(
                        type=EventType.SYSTEM,
                        content={
                            "message": "🐝 Swarms coordination with Gemini complete!",
                            "swarms_status": "llm_completed",
                            "agent_type": "gemini_function_call"
                        },
                    )
                )
                
                # Save history to file store when finished
                if self.agent.history:
                    self.agent.history.save_to_session(
                        str(self.session_uuid), self.file_store
                    )
                return
                
            # Fallback to multi-agent coordination
            logger.info(f"🧠 Processing multi-agent query (fallback): {user_input}")

            # Ask Base Agent for a structured orchestration decision first
            # Tool-based coordination approach: let the agent decide naturally via tools
            # No forced decision analysis - agent will call coordination tools if needed
            
            # Send Swarms coordination update
            await self.send_event(
                RealtimeEvent(
                    type=EventType.SYSTEM,
                    content={
                        "message": "🐝 Swarms Framework: Activating multi-agent coordination...",
                        "swarms_status": "active",
                        "agents": ["task_coordinator", "response_generator"]
                    },
                )
            )
            
            # Simulate agent processing through multi-agent coordination (handle both real and shim coordinators)
            coord_message = "Query processed successfully"
            try:
                if hasattr(self.task_coordinator, "coordinate_task"):
                    # Emit step-by-step coordination info (human-readable)
                    await self.send_event(RealtimeEvent(type=EventType.COORDINATION_INFO, content={"message": "Pattern: sequential"}))
                    for step in ["planner", "researcher", "coder", "reviewer"]:
                        await self.send_event(
                            RealtimeEvent(
                                type=EventType.COORDINATION_INFO,
                                content={
                                    "message": f"{step}: started",
                                    "step": step,
                                    "status": "started",
                                },
                            )
                        )
                    # Execute default sequential pattern in fallback path
                    result = await self.task_coordinator.coordinate_task(
                        task_type="query_processing",
                        query=user_input,
                        session_id=str(self.session_uuid),
                        steps=["planner", "researcher", "coder", "reviewer"],
                    )
                    if isinstance(result, dict):
                        # Emit completed info for each step result
                        pattern = result.get("pattern", "sequential")
                        
                        for s in result.get("steps", []):
                            step_content = {
                                "step": s.get("role"),
                                "status": "completed",
                                "summary": (s.get("output") or "")[:160],
                                "pattern": pattern,
                            }
                            
                            # Add duration if available
                            if "duration_ms" in s:
                                step_content["duration_ms"] = s["duration_ms"]
                            
                            # For hierarchical pattern, include worker details
                            if pattern == "hierarchical" and "worker_id" in s:
                                step_content["worker_id"] = s["worker_id"]
                                step_content["subtask"] = s.get("subtask", "")
                                if "steps" in s:
                                    # Emit individual worker step events
                                    for worker_step in s["steps"]:
                                        await self.send_event(
                                            RealtimeEvent(
                                                type=EventType.COORDINATION_INFO,
                                                content={
                                                    "step": worker_step.get("role"),
                                                    "status": "completed",
                                                    "summary": (worker_step.get("output") or "")[:160],
                                                    "pattern": pattern,
                                                    "duration_ms": worker_step.get("duration_ms", 0),
                                                    "worker_id": s["worker_id"],
                                                },
                                            )
                                        )
                            
                            await self.send_event(
                                RealtimeEvent(
                                    type=EventType.COORDINATION_INFO,
                                    content=step_content,
                                )
                            )
                        
                        coord_message = result.get("message", coord_message)
                        
                        # For hierarchical pattern, also emit subtask information
                        if pattern == "hierarchical" and "subtasks" in result:
                            await self.send_event(
                                RealtimeEvent(
                                    type=EventType.SYSTEM,
                                    content={
                                        "message": f"🏗️ Hierarchical decomposition: {len(result['subtasks'])} subtasks identified",
                                        "pattern": "hierarchical",
                                        "subtasks": result["subtasks"],
                                    },
                                )
                            )
                    else:
                        coord_message = str(result)
                elif hasattr(self.task_coordinator, "submit_task"):
                    task_id = await self.task_coordinator.submit_task(
                        task_type="query_processing",
                        task_data={"query": user_input, "session_id": str(self.session_uuid)},
                        required_capabilities=[],
                    )
                    coord_message = f"Task submitted: {task_id}"
            except Exception as e:
                coord_message = f"Coordinator error: {e}"
            
            # Send Swarms completion status
            await self.send_event(
                RealtimeEvent(
                    type=EventType.SYSTEM,
                    content={
                        "message": "🐝 Swarms coordination complete - generating response...",
                        "swarms_status": "completed",
                        "coordination_pattern": "sequential"
                    },
                )
            )
            
            # Send agent response (matches original pattern exactly)
            await self.send_event(
                RealtimeEvent(
                    type=EventType.AGENT_RESPONSE,
                    content={
                        "text": f"Multi-agent processing complete: {coord_message}",
                    },
                )
            )
            
            # Signal completion (matches original exactly)
            await self.send_event(
                RealtimeEvent(
                    type=EventType.STREAM_COMPLETE,
                    content={},
                )
            )
            
        except Exception as e:
            logger.error(f"Error in multi-agent query processing: {e}")
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": f"Error running agent: {str(e)}"},
                )
            )
        finally:
            # Clean up the task reference (matches original)
            self.active_task = None

    async def _handle_workspace_info(self, content: dict = None):
        """Handle workspace info request - matches original pattern."""
        await self.send_event(
            RealtimeEvent(
                type=EventType.WORKSPACE_INFO,
                content={
                    "path": f"/workspace/{self.session_uuid}",
                },
            )
        )

    async def _handle_ping(self, content: dict = None):
        """Handle ping message - matches original pattern."""
        await self.send_event(RealtimeEvent(type=EventType.PONG, content={}))
    
    async def _handle_cancel(self, content: dict = None):
        """Handle query cancellation - matches original pattern."""
        if self.active_task and not self.active_task.done():
            self.active_task.cancel()
            
        await self.send_event(
            RealtimeEvent(
                type=EventType.SYSTEM,
                content={"message": "Query cancelled"},
            )
        )
    
    async def _handle_edit_query(self, content: dict):
        """Handle query editing - matches original pattern."""
        try:
            # For now, treat edit_query the same as regular query
            await self._handle_query(content)
        except Exception as e:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": f"Error editing query: {str(e)}"},
                )
            )
    
    async def _handle_enhance_prompt(self, content: dict):
        """Handle prompt enhancement - matches original pattern."""
        await self.send_event(
            RealtimeEvent(
                type=EventType.PROMPT_GENERATED,
                content={
                    "result": content.get("text", "Enhanced prompt"),
                    "original_request": content.get("text", ""),
                },
            )
        )
    
    async def _handle_review_result(self, content: dict):
        """Handle review result - matches original pattern."""
        await self.send_event(
            RealtimeEvent(
                type=EventType.SYSTEM,
                content={"message": "Review completed"},
            )
        )

    def cleanup(self):
        """Clean up resources - matches original pattern exactly."""
        try:
            # Stop the main loop and prevent further reads
            self.is_active = False

            # Cancel any running tasks (matches original)
            if self.active_task and not self.active_task.done():
                self.active_task.cancel()
                self.active_task = None

            # Do not attempt to await websocket.close() here (sync method)
            # Simply drop the reference so the loop's None-checks stop further use
            self.websocket = None

            logger.info("🧹 Multi-agent chat session cleaned up")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
