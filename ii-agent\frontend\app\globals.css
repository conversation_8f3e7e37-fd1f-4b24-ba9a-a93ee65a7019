/* Tailwind Base Imports */
@import "tailwindcss";

/* Design System Imports */
@import "./styles/design-tokens.css";
@import "./styles/animations.css";
@import "./styles/utilities.css";

/* Tailwind Plugins */
@plugin "tailwindcss-animate";

/* Font Configuration */
@theme {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Dark Mode Configuration - Using Tailwind's built-in class strategy */
/* This is handled by the design-tokens.css file using .dark class */

/* Base Layer Styles */
@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Typography Base Styles */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-sans font-semibold;
  }

  h1 {
    @apply text-4xl;
  }

  h2 {
    @apply text-3xl;
  }

  h3 {
    @apply text-2xl;
  }

  h4 {
    @apply text-xl;
  }

  h5 {
    @apply text-lg;
  }

  h6 {
    @apply text-base;
  }

  /* Link Styles */
  a {
    @apply transition-colors hover:text-primary;
  }

  /* Code Styles */
  code {
    @apply font-mono text-sm;
  }

  pre {
    @apply font-mono text-sm overflow-x-auto;
  }

  /* Selection Styles */
  ::selection {
    @apply bg-primary text-primary-foreground;
  }
}

/* Tailwind Theme Extension for Color Variables */
@theme inline {
  /* Map CSS variables to Tailwind theme colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  /* Radius Tokens */
  --radius-sm: var(--radius-sm);
  --radius-md: var(--radius-md);
  --radius-lg: var(--radius-lg);
  --radius-xl: var(--radius-xl);
}
