FROM nginx:alpine

# Install envsubst
RUN apk add --no-cache gettext

COPY docker/nginx/nginx.conf.template /etc/nginx/conf.d/default.conf.template
COPY docker/nginx/docker-entrypoint.sh /docker-entrypoint.sh

# Normalize line endings and make entrypoint executable
RUN apk add --no-cache dos2unix && dos2unix /docker-entrypoint.sh && chmod +x /docker-entrypoint.sh

EXPOSE 80

ENTRYPOINT ["/bin/sh", "/docker-entrypoint.sh"]
