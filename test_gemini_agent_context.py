#!/usr/bin/env python3
"""
Test Gemini tool calling in the exact context used by the agent
"""

import os
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'ii-agent', 'src'))

from ii_agent.llm.gemini import GeminiDirectClient
from ii_agent.llm.message_history import MessageHistory
from ii_agent.utils.workspace_manager import WorkspaceManager
from ii_agent.server.api.settings import Settings
from ii_agent.tools.tool_manager import get_system_tools
from ii_agent.tools.terminal_client import TerminalClient
from ii_agent.tools.str_replace_client import StrReplaceClientMock
from ii_agent.prompts.system_prompt import SystemPromptBuilder
from ii_agent.core.config.system import SystemConfiguration
from ii_agent.llm.base import ToolCallParameters

async def test_gemini_in_agent_context():
    """Test Gemini tool calling exactly as the agent does it"""
    
    print("=" * 60)
    print("TESTING GEMINI TOOL CALLING IN AGENT CONTEXT")
    print("=" * 60)
    
    try:
        # Initialize all components exactly as the agent does
        settings = Settings()
        workspace_manager = WorkspaceManager(
            working_directory=os.path.abspath("."),
            is_sandbox=False
        )
        terminal_client = TerminalClient(workspace_manager=workspace_manager)
        str_replace_client = StrReplaceClientMock()
        
        print(f"Working directory: {workspace_manager.working_directory}")
        print(f"Gemini API key configured: {'Yes' if settings.gemini_api_key else 'No'}")
        
        # Create Gemini client
        gemini_client = GeminiDirectClient(api_key=settings.gemini_api_key)
        print(f"Gemini client created: {type(gemini_client)}")
        
        # Get tools exactly as the agent does
        system_prompt_builder = SystemPromptBuilder("FULL", False)
        tools = get_system_tools(
            client=gemini_client,
            workspace_manager=workspace_manager,
            sandbox_manager=None,
            message_queue=None,
            system_prompt_builder=system_prompt_builder,
            settings=settings,
            tool_args={}
        )
        
        print(f"\\nLoaded {len(tools)} tools:")
        file_tools = []
        for i, tool in enumerate(tools[:10]):  # Show first 10
            print(f"  {i+1:2d}. {tool.name}")
            if 'str_replace' in tool.name or 'editor' in tool.name:
                file_tools.append(tool)
                print(f"      *** FILE TOOL DETECTED ***")
        
        print(f"\\nFile tools available: {len(file_tools)}")
        
        if len(file_tools) == 0:
            print("ERROR: No file tools found!")
            return
        
        # Convert tools to tool parameters exactly as agent does
        tool_params = [tool.get_tool_param() for tool in tools]
        print(f"\\nGenerated {len(tool_params)} tool parameters")
        
        # Find the str_replace_editor tool
        editor_tool = None
        for param in tool_params:
            if param.name == 'str_replace_editor':
                editor_tool = param
                print(f"Found str_replace_editor tool:")
                print(f"  Name: {param.name}")
                print(f"  Description: {param.description[:100]}...")
                print(f"  Schema keys: {list(param.input_schema.get('properties', {}).keys())}")
                if 'command' in param.input_schema.get('properties', {}):
                    commands = param.input_schema['properties']['command'].get('enum', [])
                    print(f"  Available commands: {commands}")
                    if 'create' in commands:
                        print(f"  *** SUPPORTS CREATE COMMAND! ***")
                break
        
        if not editor_tool:
            print("ERROR: str_replace_editor tool not found!")
            return
        
        # Create a simple message history
        context_manager = SystemConfiguration()
        history = MessageHistory(context_manager)
        
        # Add user message asking for file creation
        user_message = "Create a simple text file called 'test_gemini_output.txt' with the content 'Hello from Gemini test!'"
        history.add_user_prompt(user_message, [])
        
        print(f"\\nUser message: {user_message}")
        print(f"Messages for LLM: {len(history.get_messages_for_llm())} messages")
        
        # Get system prompt
        system_prompt = system_prompt_builder.get_system_prompt()
        print(f"System prompt length: {len(system_prompt)} characters")
        
        # Make the LLM call exactly as the agent does
        print(f"\\nCalling Gemini with {len(tool_params)} tools...")
        
        model_response = gemini_client.generate(
            messages=history.get_messages_for_llm(),
            max_tokens=4096,
            tools=tool_params,
            system_prompt=system_prompt
        )
        
        print(f"\\nModel response: {len(model_response)} items")
        for i, item in enumerate(model_response):
            print(f"  {i+1}. Type: {type(item).__name__}")
            if hasattr(item, 'text'):
                print(f"     Text: {item.text[:100]}...")
            if hasattr(item, 'tool_name'):
                print(f"     *** TOOL CALL: {item.tool_name} ***")
                print(f"     Tool input: {item.tool_input}")
        
        # Add to history and check for pending tool calls
        history.add_assistant_turn(model_response)
        pending_tool_calls = history.get_pending_tool_calls()
        
        print(f"\\nPending tool calls: {len(pending_tool_calls)}")
        if len(pending_tool_calls) > 0:
            for call in pending_tool_calls:
                print(f"  Tool: {call.tool_name}")
                print(f"  Input: {call.tool_input}")
        else:
            print("  *** NO TOOL CALLS FOUND - THIS IS THE ISSUE! ***")
        
        return len(pending_tool_calls) > 0
        
    except Exception as e:
        print(f"Error in test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    import asyncio
    success = asyncio.run(test_gemini_in_agent_context())
    if success:
        print("\\n✓ SUCCESS: Tool calling working correctly")
    else:
        print("\\n✗ FAILURE: Tool calling not working")
