"""
Real GPU Acceleration Module
===========================

Production-ready GPU acceleration using PyTorch CUDA for genuine performance improvements.
"""

try:
    from .real_gpu_acceleration import (
        RealGPUManager,
        GPUConfig,
        GPUInfo,
        GPUMemoryStrategy,
        get_gpu_manager,
        is_gpu_available,
        get_optimal_batch_size
    )
    
    __all__ = [
        "RealGPUManager",
        "GPUConfig", 
        "GPUInfo",
        "GPUMemoryStrategy",
        "get_gpu_manager",
        "is_gpu_available",
        "get_optimal_batch_size"
    ]
    
except ImportError as e:
    # Fallback if PyTorch CUDA is not available
    import logging
    logging.getLogger(__name__).warning(f"GPU acceleration not available: {e}")
    
    __all__ = []
