#!/usr/bin/env python3
"""
Test tool availability and agent tool execution
"""

import asyncio
import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ii_agent.tools.tool_manager import get_system_tools
from ii_agent.tools import AgentTool<PERSON>anager
from ii_agent.utils.workspace_manager import WorkspaceManager
from ii_agent.server.api.settings import Settings
from ii_agent.agents.function_call import FunctionCallAgent
from ii_agent.llm.gemini import GeminiDirect<PERSON>lient
from ii_agent.tools.terminal_client import TerminalClient
from ii_agent.tools.str_replace_client import StrReplaceClientMock
from ii_agent.server.websocket.message_queue import MessageQueue

async def test_tool_availability():
    """Test which tools are available in the system"""
    
    print("=" * 50)
    print("TESTING TOOL AVAILABILITY")
    print("=" * 50)
    
    # Initialize required components
    settings = Settings()
    workspace_manager = WorkspaceManager(
        working_directory=os.path.abspath("."),
        is_sandbox=False
    )
    terminal_client = TerminalClient(workspace_manager=workspace_manager)
    str_replace_client = StrReplaceClientMock()
    message_queue = MessageQueue()
    
    print(f"Working directory: {workspace_manager.working_directory}")
    print(f"Is sandbox: {workspace_manager.is_sandbox}")
    
    # Get system tools
    print("\n1. Loading system tools...")
    tools = get_system_tools(
        settings=settings,
        workspace_manager=workspace_manager,
        terminal_client=terminal_client,
        str_replace_client=str_replace_client,
        message_queue=message_queue
    )
    
    print(f"Found {len(tools)} tools:")
    for i, tool in enumerate(tools):
        print(f"  {i+1:2d}. {tool.name:<30} - {tool.description[:60]}")
        if "str_replace" in tool.name or "create" in tool.name.lower() or "file" in tool.name.lower():
            print(f"      *** FILE TOOL DETECTED ***")
    
    # Test AgentToolManager
    print("\n2. Testing AgentToolManager...")
    tool_manager = AgentToolManager(
        tools=tools,
        settings=settings,
        workspace_manager=workspace_manager,
        terminal_client=terminal_client,
        str_replace_client=str_replace_client,
        message_queue=message_queue
    )
    
    managed_tools = tool_manager.get_tools()
    print(f"AgentToolManager has {len(managed_tools)} tools:")
    for i, tool in enumerate(managed_tools):
        print(f"  {i+1:2d}. {tool.name}")
    
    # Check for file creation capability
    print("\n3. Looking for file creation tools...")
    file_tools = []
    for tool in managed_tools:
        if hasattr(tool, 'name'):
            if "str_replace" in tool.name or "create" in tool.name.lower() or "editor" in tool.name:
                file_tools.append(tool)
                print(f"  Found: {tool.name}")
                if hasattr(tool, 'input_schema'):
                    schema = tool.input_schema
                    if 'properties' in schema and 'command' in schema['properties']:
                        commands = schema['properties']['command'].get('enum', [])
                        if 'create' in commands:
                            print(f"    *** Supports 'create' command! ***")
    
    print(f"\nFile creation tools found: {len(file_tools)}")
    
    # Test a simple agent interaction
    print("\n4. Testing agent with simple file creation request...")
    try:
        # Initialize Gemini client
        gemini_client = GeminiDirectClient(api_key=settings.gemini_api_key)
        
        # Create agent
        agent = FunctionCallAgent(
            llm_client=gemini_client,
            settings=settings,
            workspace_manager=workspace_manager,
            terminal_client=terminal_client,
            str_replace_client=str_replace_client,
            message_queue=message_queue,
        )
        
        print("Agent created successfully")
        
        # Simple test message
        test_message = "Create a simple text file called 'test_output.txt' with content 'Hello, this is a test file!'"
        print(f"Test message: {test_message}")
        
        # Process the message
        async for response in agent.process_user_message(test_message):
            print(f"Agent response: {response}")
            if "[no tools were called]" in response:
                print("*** ISSUE DETECTED: Agent says no tools were called ***")
        
        # Check if file was created
        if os.path.exists("test_output.txt"):
            print("*** SUCCESS: File was created! ***")
            with open("test_output.txt", "r") as f:
                content = f.read()
                print(f"File content: {content}")
        else:
            print("*** ISSUE: File was not created ***")
    
    except Exception as e:
        print(f"Error testing agent: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_tool_availability())
