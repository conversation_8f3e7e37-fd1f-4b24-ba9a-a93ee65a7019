"""Multi-agent coordination tools for FunctionCallAgent."""

import asyncio
import json
from typing import Any, Dict, List, Optional

from ii_agent.tools.base import LLMTool, ToolImplOutput
from ii_agent.llm.message_history import MessageHistory


class CoordinateSequentialTool(LLMTool):
    """Tool for sequential multi-agent coordination: planner → researcher → coder → reviewer."""
    
    name = "coordinate_sequential"
    description = "Coordinate a sequential multi-agent workflow (planner → researcher → coder → reviewer) for complex tasks requiring step-by-step execution."
    
    input_schema = {
        "type": "object",
        "properties": {
            "steps": {
                "type": "array",
                "items": {"type": "string"},
                "description": "List of steps for sequential coordination (default: ['planner', 'researcher', 'coder', 'reviewer'])",
                "default": ["planner", "researcher", "coder", "reviewer"]
            }
        },
        "required": []
    }

    async def run_impl(self, tool_input: dict[str, Any], message_history: Optional[MessageHistory] = None) -> ToolImplOutput:
        """Run sequential coordination with specified steps."""
        default_steps = ["planner", "researcher", "coder", "reviewer"]
        steps = tool_input.get("steps", default_steps)
        actual_steps = steps or default_steps
        
        # Get coordinator from the agent's session context
        if hasattr(self, 'agent') and hasattr(self.agent, '_coordination_callback'):
            result = await self.agent._coordination_callback('sequential', {
                'steps': actual_steps
            })
            message = f"Sequential coordination initiated: {' → '.join(actual_steps)}. Result: {result.get('message', 'Completed')}"
        else:
            message = f"Sequential coordination requested: {' → '.join(actual_steps)} (coordinator not available)"
        
        return ToolImplOutput(
            tool_output=message,
            tool_result_message=f"Sequential coordination with steps: {' → '.join(actual_steps)}",
            auxiliary_data={"steps": actual_steps, "success": True}
        )


class CoordinateConcurrentTool(LLMTool):
    """Tool for concurrent multi-agent coordination: parallel researchers → aggregation."""
    
    name = "coordinate_concurrent"
    description = "Coordinate concurrent multi-agent workflow with parallel researchers for tasks requiring simultaneous investigation of multiple topics."
    
    input_schema = {
        "type": "object",
        "properties": {
            "researchers": {
                "type": "integer",
                "description": "Number of parallel researchers (1-4)",
                "default": 2,
                "minimum": 1,
                "maximum": 4
            },
            "topic": {
                "type": "string",
                "description": "Optional topic for research coordination"
            }
        },
        "required": []
    }

    async def run_impl(self, tool_input: dict[str, Any], message_history: Optional[MessageHistory] = None) -> ToolImplOutput:
        """Run concurrent coordination with specified number of researchers."""
        researchers = tool_input.get("researchers", 2)
        topic = tool_input.get("topic")
        researchers = max(1, min(researchers, 4))  # Limit to reasonable range
        
        if hasattr(self, 'agent') and hasattr(self.agent, '_coordination_callback'):
            result = await self.agent._coordination_callback('concurrent', {
                'researcher_count': researchers,
                'topic': topic
            })
            message = f"Concurrent coordination initiated: {researchers} parallel researchers. Result: {result.get('message', 'Completed')}"
        else:
            message = f"Concurrent coordination requested: {researchers} researchers (coordinator not available)"
        
        return ToolImplOutput(
            tool_output=message,
            tool_result_message=f"Concurrent coordination with {researchers} researchers",
            auxiliary_data={"researchers": researchers, "topic": topic, "success": True}
        )


class CoordinateHierarchicalTool(LLMTool):
    """Tool for hierarchical multi-agent coordination: planner decomposes → parallel workers → reviewer."""
    
    name = "coordinate_hierarchical"
    description = "Coordinate hierarchical multi-agent workflow where a planner decomposes tasks into subtasks executed by parallel workers."
    
    input_schema = {
        "type": "object",
        "properties": {
            "subtasks": {
                "type": "array",
                "items": {"type": "string"},
                "description": "Optional list of predefined subtasks"
            },
            "max_subtasks": {
                "type": "integer",
                "description": "Maximum number of subtasks (1-5)",
                "default": 3,
                "minimum": 1,
                "maximum": 5
            }
        },
        "required": []
    }

    async def run_impl(self, tool_input: dict[str, Any], message_history: Optional[MessageHistory] = None) -> ToolImplOutput:
        """Run hierarchical coordination with specified subtasks."""
        subtasks = tool_input.get("subtasks")
        max_subtasks = tool_input.get("max_subtasks", 3)
        max_subtasks = max(1, min(max_subtasks, 5))  # Limit to reasonable range
        
        if hasattr(self, 'agent') and hasattr(self.agent, '_coordination_callback'):
            result = await self.agent._coordination_callback('hierarchical', {
                'subtasks': subtasks,
                'max_subtasks': max_subtasks
            })
            message = f"Hierarchical coordination initiated: max {max_subtasks} subtasks. Result: {result.get('message', 'Completed')}"
        else:
            message = f"Hierarchical coordination requested: max {max_subtasks} subtasks (coordinator not available)"
        
        return ToolImplOutput(
            tool_output=message,
            tool_result_message=f"Hierarchical coordination with max {max_subtasks} subtasks",
            auxiliary_data={"subtasks": subtasks, "max_subtasks": max_subtasks, "success": True}
        )


class AskClarificationTool(LLMTool):
    """Tool for requesting clarification from the user before proceeding."""
    
    name = "ask_clarification"
    description = "Ask the user clarifying questions before proceeding with a complex task. Use when requirements are unclear."
    
    input_schema = {
        "type": "object",
        "properties": {
            "questions": {
                "type": "array",
                "items": {"type": "string"},
                "description": "List of clarification questions to ask the user",
                "minItems": 1
            }
        },
        "required": ["questions"]
    }

    async def run_impl(self, tool_input: dict[str, Any], message_history: Optional[MessageHistory] = None) -> ToolImplOutput:
        """Ask clarification questions and pause for user response."""
        questions = tool_input.get("questions", [])
        if not questions:
            message = "No clarification questions provided"
            return ToolImplOutput(
                tool_output=message,
                tool_result_message="Failed to ask clarification - no questions provided",
                auxiliary_data={"success": False}
            )
        
        if hasattr(self, 'agent') and hasattr(self.agent, '_clarification_callback'):
            await self.agent._clarification_callback(questions)
            message = f"Asked {len(questions)} clarification question(s). Waiting for user response."
        else:
            message = f"Clarification requested: {'; '.join(questions[:3])} (clarification system not available)"
        
        return ToolImplOutput(
            tool_output=message,
            tool_result_message=f"Asked {len(questions)} clarification question(s)",
            auxiliary_data={"questions": questions, "success": True}
        )