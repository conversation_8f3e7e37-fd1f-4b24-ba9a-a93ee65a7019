import asyncio
import uuid
import sys
import pathlib
import pytest

# Add project root to path
ROOT = pathlib.Path(__file__).resolve().parent.parent
II_SRC = ROOT / 'ii-agent' / 'src'
for p in (ROOT, II_SRC):
    if str(p) not in sys.path:
        sys.path.insert(0, str(p))

from ii_agent.core.config.ii_agent_config import IIAgentConfig
from ii_agent.core.storage.files import FileStore

# Import the module without triggering server initialization side-effects by delaying certain imports
from src.ii_agent_integration.multi_agent_chat_session import MultiAgentChatSession, RealtimeEvent

class DummyWebSocket:
    def __init__(self):
        self.sent = []
        self._recv_queue = asyncio.Queue()
        self.query_params = {}
    async def accept(self):
        return True
    async def receive_text(self):
        return await self._recv_queue.get()
    async def send_json(self, data):
        self.sent.append(data)
    def feed(self, obj):
        import json
        self._recv_queue.put_nowait(json.dumps(obj))

@pytest.mark.asyncio
async def test_multi_agent_initialization_and_query(tmp_path):
    config = IIAgentConfig(
        workspace_root=str(tmp_path),
        logs_path=str(tmp_path / 'logs.txt'),
        code_server_port=12345,
        token_budget=8000,
        max_output_tokens_per_turn=2048,
        max_turns=20,
        minimize_stdout_logs=True,
    )
    file_store = FileStore()
    ws = DummyWebSocket()
    session = MultiAgentChatSession(ws, uuid.uuid4(), file_store, config)

    # Manually initialize to avoid WebSocket loop dependencies
    await session.initialize_multi_agent_system()
    # Directly invoke process query (bypassing WebSocket message schema)
    await session.process_multi_agent_query("Analyze and optimize the system for performance")

    messages = [m for m in ws.sent if isinstance(m, dict) and m.get('type')]
    ready = [m for m in messages if 'Multi-Agent System Ready' in m.get('content',{}).get('message','')]
    processing = [m for m in messages if 'Processing:' in m.get('content',{}).get('message','')]
    assert ready, 'Initialization message missing'
    assert processing, 'Processing event missing'
