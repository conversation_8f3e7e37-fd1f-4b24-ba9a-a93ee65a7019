#!/usr/bin/env python3
"""
Simple WebSocket test to check if the WebSocket endpoint is working
"""

import asyncio
import json
import websockets
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_websocket():
    """Test WebSocket connection without timeout parameter"""
    try:
        # Connect without the problematic timeout parameter
        async with websockets.connect("ws://localhost:8000/ws") as websocket:
            logger.info("✅ WebSocket connected successfully!")
            
            # Test basic message
            test_message = {
                "type": "ping",
                "data": {"message": "test connection"}
            }
            
            await websocket.send(json.dumps(test_message))
            logger.info("📤 Sent test message")
            
            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                response_data = json.loads(response)
                logger.info(f"📥 Received response: {response_data}")
                return True
            except asyncio.TimeoutError:
                logger.warning("⚠️ No response received within timeout")
                return False
                
    except Exception as e:
        logger.error(f"❌ WebSocket connection failed: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_websocket())
    if result:
        print("✅ WebSocket test PASSED")
    else:
        print("❌ WebSocket test FAILED")
