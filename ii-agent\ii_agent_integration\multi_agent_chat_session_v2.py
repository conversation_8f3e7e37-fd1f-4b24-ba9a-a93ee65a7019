"""Multi-Agent Chat Session - Original ii-agent compatible implementation.

This module provides a drop-in replacement for the original ChatSession
that matches the exact patterns and models from the official ii-agent repository.

Repository: https://github.com/Intelligent-Internet/ii-agent
Updated: 2025-01-09 19:10:00 UTC
"""

import asyncio
import json
import logging
import uuid
from typing import Dict, Any, List, Optional

from fastapi import WebSocket, WebSocketDisconnect
from pydantic import BaseModel, ValidationError

logger = logging.getLogger(__name__)

# Message models that exactly match the original ii-agent repository
try:
    # Try importing from original ii-agent models first
    from ii_agent.server.models.messages import (
        WebSocketMessage,
        QueryContent,
        InitAgentContent,
    )
    from ii_agent.core.event import RealtimeEvent, EventType
    logger.info("✅ Using original ii-agent message models")
    USING_ORIGINAL_MODELS = True
    
except ImportError as e:
    logger.warning(f"⚠️ Original models not available, using compatible fallbacks: {e}")
    # Exact replicas of original ii-agent models based on repository analysis
    
    class WebSocketMessage(BaseModel):
        """Base model for WebSocket messages - matches original ii-agent."""
        type: str
        content: Dict[str, Any] = {}

    class QueryContent(BaseModel):
        """Model for query message content - matches original ii-agent."""
        text: str = ""
        resume: bool = False
        files: List[str] = []

    class InitAgentContent(BaseModel):
        """Model for agent initialization content - matches original ii-agent."""
        model_name: str
        tool_args: Dict[str, Any] = {}
        thinking_tokens: int = 0
    
    # Event models matching original
    from enum import Enum
    class EventType(str, Enum):
        CONNECTION_ESTABLISHED = "connection_established"
        AGENT_INITIALIZED = "agent_initialized"
        WORKSPACE_INFO = "workspace_info"
        PROCESSING = "processing"
        AGENT_THINKING = "agent_thinking"
        TOOL_CALL = "tool_call"
        TOOL_RESULT = "tool_result"
        AGENT_RESPONSE = "agent_response"
        STREAM_COMPLETE = "stream_complete"
        ERROR = "error"
        SYSTEM = "system"
        PONG = "pong"
        USER_MESSAGE = "user_message"
        
    class RealtimeEvent(BaseModel):
        type: EventType
        content: Dict[str, Any]
    
    USING_ORIGINAL_MODELS = False

# Multi-agent components with fallbacks
try:
    from .agents.agent_registry import AgentRegistry
    from .agents.task_coordinator import TaskCoordinator
    from .swarms.swarms_integration import SwarmsFrameworkIntegration
    MULTI_AGENT_AVAILABLE = True
    logger.info("✅ Multi-agent components loaded successfully")
except ImportError as e:
    logger.warning(f"⚠️ Multi-agent components not available: {e}")
    # Create fallback classes that match the expected interface
    class AgentRegistry:
        def __init__(self): 
            self.agents = {}
            logger.info("🔄 FALLBACK: AgentRegistry initialized")
        async def register_agent(self, agent_id, agent_name=None, capabilities=None, role=None, **kwargs): 
            logger.info(f"🔄 FALLBACK: Registering agent {agent_id}")
            return True
        async def start(self): 
            logger.info("🔄 FALLBACK: AgentRegistry.start() called")
        async def stop(self): pass
    
    class TaskCoordinator:
        def __init__(self): 
            logger.info("🔄 FALLBACK: TaskCoordinator initialized")
        async def coordinate_task(self, task_type, **kwargs): 
            logger.info(f"🔄 FALLBACK: Coordinating task {task_type}")
            return {"status": "completed", "message": "Fallback coordination"}
        async def start(self): pass
        async def stop(self): pass
    
    class SwarmsFrameworkIntegration:
        def __init__(self): 
            logger.info("🔄 FALLBACK: SwarmsFrameworkIntegration initialized")
        async def start(self): pass
        async def stop(self): pass
    
    MULTI_AGENT_AVAILABLE = False


class MultiAgentChatSession:
    """Drop-in multi-agent chat session that follows original ii-agent patterns."""

    def __init__(
        self,
        websocket: WebSocket,
        session_uuid: uuid.UUID,
        file_store=None,
        config=None,
    ):
        self.websocket = websocket
        self.session_uuid = session_uuid
        self.file_store = file_store
        self.config = config
        
        # Multi-agent components
        self.agent_registry = AgentRegistry()
        self.task_coordinator = TaskCoordinator()
        self.swarms_integration = SwarmsFrameworkIntegration()
        
        # Session state
        self.active_task: Optional[asyncio.Task] = None
        self.first_message = True
        
        logger.info(f"🧠 MultiAgentChatSession initialized for session {session_uuid}")

    async def send_event(self, event: RealtimeEvent):
        """Send an event to the client via WebSocket - matches original pattern."""
        if self.websocket:
            try:
                await self.websocket.send_json(event.model_dump())
            except Exception as e:
                logger.error(f"Error sending event to client: {e}")

    async def start_chat_loop(self):
        """Start the chat loop for this session - matches original pattern."""
        try:
            # Initialize multi-agent components
            await self.agent_registry.start()
            await self.task_coordinator.start()
            await self.swarms_integration.start()
            
            logger.info("🚀 Multi-agent chat loop started successfully")
            
            # Send handshake
            await self.handshake()
            
            # Main message loop
            while True:
                try:
                    data = await self.websocket.receive_json()
                    await self.handle_multi_agent_message(data)
                except WebSocketDisconnect:
                    logger.info("WebSocket disconnected")
                    break
                except Exception as e:
                    logger.error(f"Unhandled loop error: {e}")
                    # Don't break the loop on errors, just log them
                    
        except Exception as e:
            logger.error(f"Error in chat loop: {e}")
        finally:
            await self.cleanup()

    async def handshake(self):
        """Handle handshake message - matches original pattern."""
        await self.send_event(
            RealtimeEvent(
                type=EventType.CONNECTION_ESTABLISHED,
                content={
                    "message": "Connected to Multi-Agent WebSocket Server",
                    "multi_agent_mode": True,
                    "session_id": str(self.session_uuid),
                },
            )
        )

    async def handle_multi_agent_message(self, payload: dict):
        """Handle incoming WebSocket messages - follows original validation pattern."""
        try:
            # Validate message structure exactly like original
            msg = WebSocketMessage(**payload)
        except ValidationError as e:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": f"Invalid message format: {e}"},
                )
            )
            return

        # Route to appropriate handler based on message type
        handlers = {
            "init_agent": self._handle_init_agent,
            "query": self._handle_query,
            "workspace_info": self._handle_workspace_info,
            "ping": self._handle_ping,
        }

        handler = handlers.get(msg.type)
        if handler:
            await handler(msg.content)
        else:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": f"Unknown message type: {msg.type}"},
                )
            )

    async def _handle_init_agent(self, content: dict):
        """Handle agent initialization - matches original pattern."""
        try:
            init_content = InitAgentContent(**content)
            
            # Register the agent with multi-agent system
            await self.agent_registry.register_agent(
                agent_id=f"agent_{self.session_uuid}",
                agent_name=f"MultiAgent_{init_content.model_name}",
                capabilities=init_content.tool_args.keys(),
                role="primary",
                model_name=init_content.model_name,
                thinking_tokens=init_content.thinking_tokens,
                tool_args=init_content.tool_args,
            )

            await self.send_event(
                RealtimeEvent(
                    type=EventType.AGENT_INITIALIZED,
                    content={
                        "message": f"Multi-agent system initialized with {init_content.model_name}",
                        "model_name": init_content.model_name,
                        "multi_agent_capabilities": list(init_content.tool_args.keys()),
                    },
                )
            )
            
        except ValidationError as e:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": f"Invalid init_agent content: {str(e)}"},
                )
            )
        except Exception as e:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": f"Error initializing multi-agent system: {str(e)}"},
                )
            )

    async def _handle_query(self, content: dict):
        """Handle query processing - matches original pattern."""
        try:
            query_content = QueryContent(**content)

            # Set session name from first message (matches original)
            if self.first_message and query_content.text.strip():
                session_name = query_content.text.strip()[:100]
                self.first_message = False
                logger.info(f"Session named: {session_name}")

            # Send processing acknowledgment
            await self.send_event(
                RealtimeEvent(
                    type=EventType.PROCESSING,
                    content={"message": "Multi-agent system processing your request..."},
                )
            )

            # Process with multi-agent coordination
            await self.process_multi_agent_query(query_content.text)

        except ValidationError as e:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": f"Invalid query content: {str(e)}"},
                )
            )

    async def _handle_workspace_info(self, content: dict = None):
        """Handle workspace info request - matches original pattern."""
        await self.send_event(
            RealtimeEvent(
                type=EventType.WORKSPACE_INFO,
                content={
                    "path": f"/workspace/multi-agent-{self.session_uuid}",
                    "multi_agent_mode": True,
                },
            )
        )

    async def _handle_ping(self, content: dict = None):
        """Handle ping message - matches original pattern."""
        await self.send_event(RealtimeEvent(type=EventType.PONG, content={}))

    async def process_multi_agent_query(self, query: str):
        """Process query using multi-agent coordination."""
        try:
            # Coordinate the task through our multi-agent system
            coordination_result = await self.task_coordinator.coordinate_task(
                task_type="query_processing",
                query=query,
                session_id=str(self.session_uuid),
            )
            
            # Send response back to client
            await self.send_event(
                RealtimeEvent(
                    type=EventType.AGENT_RESPONSE,
                    content={
                        "message": f"Multi-agent processing complete: {coordination_result.get('message', 'Query processed successfully')}",
                        "query": query,
                        "coordination_status": coordination_result.get('status', 'completed'),
                    },
                )
            )
            
            # Signal completion
            await self.send_event(
                RealtimeEvent(
                    type=EventType.STREAM_COMPLETE,
                    content={},
                )
            )
            
        except Exception as e:
            logger.error(f"Error in multi-agent query processing: {e}")
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": f"Multi-agent processing error: {str(e)}"},
                )
            )

    async def cleanup(self):
        """Clean up resources - matches original pattern."""
        try:
            await self.agent_registry.stop()
            await self.task_coordinator.stop()
            await self.swarms_integration.stop()
            
            if self.active_task and not self.active_task.done():
                self.active_task.cancel()
                
            logger.info("🧹 Multi-agent chat session cleaned up")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
