"""
Swarms Framework Integration for ii-agent

This module provides a clean integration layer between ii-agent and the Swarms framework,
replacing the broken multi-agent components with production-ready alternatives.
"""

import logging
from typing import Dict, List, Optional, Any, Union
import asyncio
from concurrent.futures import ThreadPoolExecutor

try:
    from swarms import Agent, SequentialWorkflow, ConcurrentWorkflow
    SWARMS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Swarms framework not available: {e}")
    SWARMS_AVAILABLE = False

logger = logging.getLogger(__name__)


class SwarmsMultiAgentManager:
    """
    Production-ready multi-agent manager using Swarms framework.
    
    Replaces ii-agent's broken multi-agent system with a stable,
    well-tested alternative that supports:
    - Sequential agent workflows
    - Concurrent agent execution  
    - Hierarchical coordination
    - Agent lifecycle management
    """
    
    def __init__(self):
        self.agents: Dict[str, Agent] = {}
        self.workflows: Dict[str, Union[SequentialWorkflow, ConcurrentWorkflow]] = {}
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        if not SWARMS_AVAILABLE:
            raise ImportError("Swarms framework is required but not installed. Run: pip install swarms")
        
        logger.info("✅ SwarmsMultiAgentManager initialized successfully")
    
    def create_agent(
        self, 
        name: str, 
        model_name: str = "gpt-4o-mini",
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> Agent:
        """
        Create a new Swarms agent with the specified configuration.
        
        Args:
            name: Unique agent identifier
            model_name: LLM model to use (e.g., "gpt-4o", "claude-3-sonnet")
            system_prompt: System prompt for the agent
            **kwargs: Additional agent configuration
            
        Returns:
            Configured Swarms Agent instance
        """
        if name in self.agents:
            logger.warning(f"Agent '{name}' already exists, returning existing instance")
            return self.agents[name]
        
        agent_config = {
            "agent_name": name,
            "model_name": model_name,
            "max_loops": 1,
            "streaming_on": False,
            "verbose": False,
            **kwargs
        }
        
        if system_prompt:
            agent_config["system_prompt"] = system_prompt
        
        try:
            agent = Agent(**agent_config)
            self.agents[name] = agent
            logger.info(f"✅ Created agent '{name}' with model '{model_name}'")
            return agent
        except Exception as e:
            logger.error(f"❌ Failed to create agent '{name}': {e}")
            raise
    
    def create_sequential_workflow(
        self, 
        workflow_name: str,
        agent_names: List[str],
        **kwargs
    ) -> SequentialWorkflow:
        """
        Create a sequential workflow where agents execute in order.
        
        Args:
            workflow_name: Unique workflow identifier
            agent_names: List of agent names to include in workflow
            **kwargs: Additional workflow configuration
            
        Returns:
            Configured SequentialWorkflow instance
        """
        if workflow_name in self.workflows:
            logger.warning(f"Workflow '{workflow_name}' already exists")
            return self.workflows[workflow_name]
        
        # Get agent instances
        agents = []
        for name in agent_names:
            if name not in self.agents:
                raise ValueError(f"Agent '{name}' not found. Create it first with create_agent()")
            agents.append(self.agents[name])
        
        try:
            workflow = SequentialWorkflow(
                name=workflow_name,
                agents=agents,
                **kwargs
            )
            self.workflows[workflow_name] = workflow
            logger.info(f"✅ Created sequential workflow '{workflow_name}' with {len(agents)} agents")
            return workflow
        except Exception as e:
            logger.error(f"❌ Failed to create sequential workflow '{workflow_name}': {e}")
            raise
    
    def create_concurrent_workflow(
        self, 
        workflow_name: str,
        agent_names: List[str],
        **kwargs
    ) -> ConcurrentWorkflow:
        """
        Create a concurrent workflow where agents execute in parallel.
        
        Args:
            workflow_name: Unique workflow identifier
            agent_names: List of agent names to include in workflow
            **kwargs: Additional workflow configuration
            
        Returns:
            Configured ConcurrentWorkflow instance
        """
        if workflow_name in self.workflows:
            logger.warning(f"Workflow '{workflow_name}' already exists")
            return self.workflows[workflow_name]
        
        # Get agent instances
        agents = []
        for name in agent_names:
            if name not in self.agents:
                raise ValueError(f"Agent '{name}' not found. Create it first with create_agent()")
            agents.append(self.agents[name])
        
        try:
            workflow = ConcurrentWorkflow(
                name=workflow_name,
                agents=agents,
                **kwargs
            )
            self.workflows[workflow_name] = workflow
            logger.info(f"✅ Created concurrent workflow '{workflow_name}' with {len(agents)} agents")
            return workflow
        except Exception as e:
            logger.error(f"❌ Failed to create concurrent workflow '{workflow_name}': {e}")
            raise
    
    def run_agent(self, agent_name: str, task: str, **kwargs) -> str:
        """
        Run a single agent with the specified task.
        
        Args:
            agent_name: Name of the agent to run
            task: Task description for the agent
            **kwargs: Additional run parameters
            
        Returns:
            Agent's response as string
        """
        if agent_name not in self.agents:
            raise ValueError(f"Agent '{agent_name}' not found")
        
        try:
            agent = self.agents[agent_name]
            result = agent.run(task=task, **kwargs)
            logger.info(f"✅ Agent '{agent_name}' completed task successfully")
            return result
        except Exception as e:
            logger.error(f"❌ Agent '{agent_name}' failed to complete task: {e}")
            raise
    
    def run_workflow(self, workflow_name: str, task: str, **kwargs) -> Any:
        """
        Execute a workflow with the specified task.
        
        Args:
            workflow_name: Name of the workflow to run
            task: Task description for the workflow
            **kwargs: Additional run parameters
            
        Returns:
            Workflow execution result
        """
        if workflow_name not in self.workflows:
            raise ValueError(f"Workflow '{workflow_name}' not found")
        
        try:
            workflow = self.workflows[workflow_name]
            result = workflow.run(task=task, **kwargs)
            logger.info(f"✅ Workflow '{workflow_name}' completed successfully")
            return result
        except Exception as e:
            logger.error(f"❌ Workflow '{workflow_name}' failed: {e}")
            raise
    
    async def run_workflow_async(self, workflow_name: str, task: str, **kwargs) -> Any:
        """
        Execute a workflow asynchronously.
        
        Args:
            workflow_name: Name of the workflow to run
            task: Task description for the workflow
            **kwargs: Additional run parameters
            
        Returns:
            Workflow execution result
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor, 
            self.run_workflow, 
            workflow_name, 
            task, 
            **kwargs
        )
    
    def list_agents(self) -> List[str]:
        """Get list of all agent names."""
        return list(self.agents.keys())
    
    def list_workflows(self) -> List[str]:
        """Get list of all workflow names."""
        return list(self.workflows.keys())
    
    def get_agent_info(self, agent_name: str) -> Dict[str, Any]:
        """Get information about a specific agent."""
        if agent_name not in self.agents:
            raise ValueError(f"Agent '{agent_name}' not found")
        
        agent = self.agents[agent_name]
        return {
            "name": agent.agent_name,
            "model": getattr(agent, 'model_name', 'unknown'),
            "created": True,
            "status": "ready"
        }
    
    def cleanup(self):
        """Clean up resources and shutdown executor."""
        try:
            self.executor.shutdown(wait=True)
            logger.info("✅ SwarmsMultiAgentManager cleaned up successfully")
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")


# Global instance for easy access
_swarms_manager: Optional[SwarmsMultiAgentManager] = None


def get_swarms_manager() -> SwarmsMultiAgentManager:
    """
    Get the global Swarms manager instance.
    Creates one if it doesn't exist.
    """
    global _swarms_manager
    if _swarms_manager is None:
        _swarms_manager = SwarmsMultiAgentManager()
    return _swarms_manager


def is_swarms_available() -> bool:
    """Check if Swarms framework is available."""
    return SWARMS_AVAILABLE
