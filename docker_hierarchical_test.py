#!/usr/bin/env python3
"""Test script to run hierarchical coordination inside Docker container."""

import asyncio
import sys
import os

# Add paths
sys.path.insert(0, '/app')
sys.path.insert(0, '/app/ii_agent_integration')

# Set environment variables
os.environ["II_AGENT_MULTI_AGENT"] = "true"
os.environ["GOOGLE_API_KEY"] = "test_key_for_verification"

async def test_hierarchical_coordination():
    """Test hierarchical coordination with mocked components."""
    
    print("🎯 Testing Hierarchical Coordination in Docker")
    print("=" * 50)
    
    try:
        from ii_agent_integration.agents.task_coordinator import TaskCoordinator
        from ii_agent_integration.agents.agent_registry import AgentRegistry
        print("✅ TaskCoordinator imported")
        
        # Create agent registry and coordinator
        registry = AgentRegistry()
        coordinator = TaskCoordinator(registry)
        print("✅ TaskCoordinator instantiated")
        
        # Create mock components
        class MockAgent:
            def __init__(self, name, role="assistant"):
                self.name = name
                self.role = role
                
            async def generate(self, prompt):
                if "planner" in self.name.lower() or "decompose" in prompt.lower():
                    return f"DECOMPOSITION: Breaking task into subtasks:\n1. Research phase\n2. Analysis phase\n3. Synthesis phase"
                elif "worker" in self.name.lower():
                    return f"WORKER RESULT: Completed {self.name} subtask with detailed analysis"
                elif "reviewer" in self.name.lower() or "review" in prompt.lower():
                    return f"REVIEW: All subtasks completed successfully, results integrated"
                else:
                    return f"RESPONSE from {self.name}: Task processed successfully"
        
        class MockSession:
            def __init__(self):
                self.agents = [
                    MockAgent("planner_agent", "planner"),
                    MockAgent("worker_agent_1", "worker"),
                    MockAgent("worker_agent_2", "worker"),
                    MockAgent("reviewer_agent", "reviewer")
                ]
                self.events = []
                
            async def emit_event(self, event_type, data):
                self.events.append({
                    "type": event_type, 
                    "data": data, 
                    "timestamp": "test_timestamp"
                })
                print(f"📡 Event: {event_type} - {data.get('message', '')}")
        
        # Test hierarchical task
        task = "Please decompose this complex multi-step research project into manageable components and coordinate their execution"
        
        print(f"📋 Testing task: {task[:60]}...")
        
        # Create mock session
        session = MockSession()
        agent_configs = [{"name": agent.name, "role": agent.role} for agent in session.agents]
        
        print("🚀 Starting hierarchical coordination...")
        
        # Run coordination
        result = await coordinator.coordinate_task_hierarchical(
            task,
            agent_configs,
            session
        )
        
        print(f"\n📊 COORDINATION RESULTS:")
        print(f"   Status: {result.get('status', 'unknown')}")
        print(f"   Pattern: {result.get('pattern', 'unknown')}")
        print(f"   Events: {len(session.events)}")
        
        # Verify hierarchical characteristics
        hierarchical_events = [e for e in session.events if "hierarchical" in str(e).lower()]
        coordination_flow = len(session.events) >= 3  # Start, info, complete
        
        print(f"\n✅ Hierarchical events: {len(hierarchical_events)}")
        print(f"✅ Coordination flow: {coordination_flow}")
        print(f"✅ Result generated: {result is not None}")
        
        if result and coordination_flow:
            print("\n🎉 HIERARCHICAL COORDINATION SUCCESS!")
            print("✅ Docker environment is working")
            print("✅ TaskCoordinator is functional")
            print("✅ Google Generative AI dependency resolved")
            return True
        else:
            print("\n⚠️  Some issues detected")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Test in Docker environment
    result = asyncio.run(test_hierarchical_coordination())
    exit_code = 0 if result else 1
    print(f"\n🏁 Test exit code: {exit_code}")
    sys.exit(exit_code)
