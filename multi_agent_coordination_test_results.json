{"test_timestamp": "2025-08-19T16:42:45.587699", "handshake_received": true, "coordination_detected": false, "events_received": [], "total_responses": 4, "all_responses": [{"type": "connection_established", "content": {"message": "Connected to Agent WebSocket Server", "workspace_path": "/workspace/06f093e5-b67b-4cb0-8243-e132946e5f4f", "multi_agent_mode": true}}, {"type": "system", "content": {"message": "🐝 Swarms Framework initialized and ready for coordination", "swarms_status": "active", "coordination_patterns": ["sequential", "concurrent", "hierarchical"]}}, {"type": "error", "content": {"message": "Error initializing agent: AgentRegistry.register_agent() got an unexpected keyword argument 'agent_id'"}}, {"type": "error", "content": {"message": "Unknown message type: message"}}]}