"""
Cross-platform process management
Replaces pexpect with platform-independent subprocess handling.
"""

import asyncio
import logging
import os
import platform
import subprocess
import sys
from typing import Optional, List, Dict, Any

logger = logging.getLogger(__name__)

class CrossPlatformProcess:
    """Cross-platform process manager that works on Windows, Linux, and macOS"""
    
    def __init__(self, command: str, cwd: Optional[str] = None, env: Optional[Dict[str, str]] = None):
        self.command = command
        self.cwd = cwd or os.getcwd()
        self.env = env or os.environ.copy()
        self.process: Optional[subprocess.Popen] = None
        self.is_windows = platform.system() == "Windows"
    
    async def spawn(self, timeout: Optional[float] = None) -> bool:
        """Spawn the process"""
        try:
            # Prepare command for platform
            if self.is_windows:
                # Windows: use shell=True for better compatibility
                cmd = self.command
                shell = True
            else:
                # Unix-like: split command
                cmd = self.command.split() if isinstance(self.command, str) else self.command
                shell = False
            
            # Start process
            self.process = subprocess.Popen(
                cmd,
                cwd=self.cwd,
                env=self.env,
                shell=shell,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            logger.info(f"Spawned process: {self.command} (PID: {self.process.pid})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to spawn process: {e}")
            return False
    
    async def send(self, text: str) -> bool:
        """Send text to process stdin"""
        if not self.process or not self.process.stdin:
            return False
        
        try:
            self.process.stdin.write(text)
            self.process.stdin.flush()
            return True
        except Exception as e:
            logger.error(f"Failed to send text to process: {e}")
            return False
    
    async def expect(self, pattern: str, timeout: Optional[float] = None) -> Optional[str]:
        """Wait for pattern in process output (simplified version of pexpect.expect)"""
        if not self.process:
            return None
        
        try:
            # Read output with timeout
            if timeout:
                # Use asyncio timeout
                output = await asyncio.wait_for(
                    self._read_until_pattern(pattern),
                    timeout=timeout
                )
            else:
                output = await self._read_until_pattern(pattern)
            
            return output
            
        except asyncio.TimeoutError:
            logger.warning(f"Timeout waiting for pattern: {pattern}")
            return None
        except Exception as e:
            logger.error(f"Error waiting for pattern: {e}")
            return None
    
    async def _read_until_pattern(self, pattern: str) -> str:
        """Read process output until pattern is found"""
        output_buffer = ""
        
        while self.process and self.process.poll() is None:
            # Read a chunk of output
            try:
                chunk = self.process.stdout.read(1024)
                if not chunk:
                    await asyncio.sleep(0.1)
                    continue
                
                output_buffer += chunk
                
                # Check if pattern is in buffer
                if pattern in output_buffer:
                    return output_buffer
                
                # Prevent buffer from growing too large
                if len(output_buffer) > 10000:
                    output_buffer = output_buffer[-5000:]
                    
            except Exception as e:
                logger.error(f"Error reading process output: {e}")
                break
        
        return output_buffer
    
    async def read_nonblocking(self, size: int = 1024) -> str:
        """Read available output without blocking"""
        if not self.process or not self.process.stdout:
            return ""
        
        try:
            # Check if data is available
            if self.is_windows:
                # Windows: use peek to check availability
                import msvcrt
                if msvcrt.kbhit():
                    return self.process.stdout.read(size)
                return ""
            else:
                # Unix: use select
                import select
                ready, _, _ = select.select([self.process.stdout], [], [], 0)
                if ready:
                    return self.process.stdout.read(size)
                return ""
                
        except Exception as e:
            logger.error(f"Error reading non-blocking: {e}")
            return ""
    
    async def terminate(self) -> bool:
        """Terminate the process"""
        if not self.process:
            return True
        
        try:
            self.process.terminate()
            
            # Wait for process to terminate
            try:
                await asyncio.wait_for(
                    asyncio.create_task(self._wait_for_termination()),
                    timeout=5.0
                )
            except asyncio.TimeoutError:
                # Force kill if terminate doesn't work
                self.process.kill()
                await asyncio.sleep(0.1)
            
            logger.info(f"Terminated process PID: {self.process.pid}")
            return True
            
        except Exception as e:
            logger.error(f"Error terminating process: {e}")
            return False
    
    async def _wait_for_termination(self):
        """Wait for process to terminate"""
        while self.process and self.process.poll() is None:
            await asyncio.sleep(0.1)
    
    def is_alive(self) -> bool:
        """Check if process is still running"""
        return self.process is not None and self.process.poll() is None
    
    def get_exit_code(self) -> Optional[int]:
        """Get process exit code"""
        return self.process.returncode if self.process else None

# Mock pexpect module for compatibility
class MockPexpect:
    """Mock pexpect module that uses CrossPlatformProcess"""
    
    @staticmethod
    def spawn(command: str, timeout: Optional[float] = None, cwd: Optional[str] = None) -> CrossPlatformProcess:
        """Create and spawn a cross-platform process"""
        process = CrossPlatformProcess(command, cwd=cwd)
        # Note: spawn is async, but pexpect.spawn is sync
        # This is a compatibility layer - real usage should use async
        return process

# Replace pexpect module
sys.modules['pexpect'] = MockPexpect()
