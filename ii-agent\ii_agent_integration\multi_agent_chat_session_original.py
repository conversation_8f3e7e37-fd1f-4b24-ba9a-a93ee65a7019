"""Multi-Agent Chat Session - Clean implementation.

This module provides a drop-in replacement for the original ChatSession
with true multi-agent orchestration using the internal components.

Last updated: 2025-01-09 18:52:00 UTC - Fixed Pydantic validation issues
"""

from __future__ import annotations

import json
import logging
import uuid
from typing import Dict, Any, List, Optional

from fastapi import WebSocket, WebSocketDisconnect
from pydantic import ValidationError

logger = logging.getLogger(__name__)

# Import our multi-agent components from the local integration
try:
    from .agents.agent_registry import AgentRegistry
    from .agents.task_coordinator import TaskCoordinator
    from .swarms.swarms_integration import (
        SwarmsFrameworkIntegration,
        SwarmsCoordinationPattern,
    )
    from .swarms.neural_swarms_agent import NeuralSwarmsAgent
    MULTI_AGENT_AVAILABLE = True
    logger.info("✅ Multi-agent components loaded successfully")
except ImportError as e:
    logger.warning(f"⚠️ Multi-agent components not available: {e}")
    # Create fallback classes - VERSION 2.1
    class AgentRegistry:
        def __init__(self): 
            self.agents = {}
            logger.info("🔄 FALLBACK: AgentRegistry initialized with start() method")
        async def register_agent(self, agent_id, agent_name=None, capabilities=None, role=None, **kwargs): 
            logger.info(f"🔄 FALLBACK: Registering agent {agent_id}")
            return True  # Return success
        def unregister_agent(self, agent_id): pass
        async def start(self): 
            logger.info("🔄 FALLBACK: AgentRegistry.start() called successfully")
            pass
        async def stop(self): pass
        def find_agents_by_capabilities(self, capabilities): return []
        def get_capability_summary(self): return {}
    
    class TaskCoordinator:
        def __init__(self, registry): pass
        async def coordinate_task(self, **kwargs): pass
        async def stop(self): pass
    
    class SwarmsFrameworkIntegration:
        def __init__(self, **kwargs): 
            logger.info("🔄 Using fallback SwarmsFrameworkIntegration")
            pass
        async def coordinate_task(self, **kwargs): return {"result": "fallback"}
        async def start(self): pass
        async def stop(self): pass
        async def execute_swarms_coordination(self, **kwargs): 
            return {"success": True, "result": "Fallback coordination completed", "agents": []}
    
    class SwarmsCoordinationPattern:
        SEQUENTIAL_WORKFLOW = "sequential_workflow"
    
    class NeuralSwarmsAgent:
        def __init__(self, **kwargs): pass
    
    MULTI_AGENT_AVAILABLE = False
    logger.info("🔄 Using fallback multi-agent classes")

from ii_agent.core.event import RealtimeEvent, EventType as _ServerEventType
try:
    EventType = _ServerEventType  # type: ignore
    _ = EventType.MESSAGE  # probe
except Exception:  # define minimal fallback
    from enum import Enum

    class EventType(Enum):  # type: ignore
        SYSTEM = "system"
        ERROR = "error"
        CONNECTION_ESTABLISHED = "connection_established"
from ii_agent.core.storage.files import FileStore
from ii_agent.core.config.ii_agent_config import IIAgentConfig
try:  # Attempt full import (may trigger heavy server initialization)
    from ii_agent.server.models.messages import (  # type: ignore
        WebSocketMessage,
        QueryContent as OriginalQueryContent,
        InitAgentContent as OriginalInitAgentContent,
    )
    # We'll use our own models with better defaults
    raise ImportError("Force fallback models for better compatibility")
except Exception:  # Fallback lightweight Pydantic models
    from pydantic import BaseModel

    class WebSocketMessage(BaseModel):
        type: str
        content: Dict[str, Any] = {}

    class QueryContent(BaseModel):
        text: str = "Hello multi-agent system"  # Primary field
        query: Optional[str] = None  # Alternative field
        message: Optional[str] = None  # Alternative field
        user_input: Optional[str] = None  # Alternative field
        
        def get_query_text(self) -> str:
            """Get query text from any available field."""
            return (self.query or self.text or self.message or 
                   self.user_input or "Hello multi-agent system")

    class InitAgentContent(BaseModel):
        model_name: str = "multi-agent-fallback"  # Required default
        thinking_tokens: int = 0
        tool_args: Dict[str, Any] = {}

logger = logging.getLogger(__name__)

# Version check to force module reload
__VERSION__ = "2025.01.09.1852"
logger.info(f"Loading MultiAgentChatSession version {__VERSION__}")


class MultiAgentChatSession:
    """Drop-in multi-agent chat session."""

    def __init__(
        self,
        websocket: WebSocket,
        session_uuid: uuid.UUID,
        file_store: FileStore,
        config: IIAgentConfig,
    ) -> None:
        self.websocket = websocket
        self.session_uuid = session_uuid
        self.file_store = file_store
        self.config = config

        self.agent_registry = AgentRegistry()
        self.task_coordinator = TaskCoordinator(self.agent_registry)
        self.swarms_integration = SwarmsFrameworkIntegration(
            coordination_hub=self.task_coordinator
        )

        self.active_agents: Dict[str, NeuralSwarmsAgent] = {}
        self.first_message = True

    async def start_chat_loop(self) -> None:
        await self.handshake()
        await self.initialize_multi_agent_system()
        try:
            while True:
                # Check if WebSocket is still valid before receiving
                if self.websocket is None:
                    logger.error("WebSocket is None, breaking chat loop")
                    break

                # Check WebSocket state
                from fastapi import WebSocketState
                state = getattr(self.websocket, "application_state", None)
                client_state = getattr(self.websocket, "client_state", None)
                if (
                    state == WebSocketState.DISCONNECTED
                    or client_state == WebSocketState.DISCONNECTED
                ):
                    logger.info("WebSocket disconnected, breaking chat loop")
                    break

                raw = await self.websocket.receive_text()
                data = json.loads(raw)
                logger.debug(f"📨 Received message: {data}")
                await self.handle_multi_agent_message(data)
        except json.JSONDecodeError:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR, content={"message": "Invalid JSON"}
                )
            )
        except WebSocketDisconnect:
            await self.cleanup_multi_agent_system()
        except Exception as e:  # pragma: no cover
            logger.exception("Error in chat loop: %s", e)
            await self.cleanup_multi_agent_system()

    async def initialize_multi_agent_system(self) -> None:
        await self.agent_registry.start()
        baseline = [
            ("neural-analyzer", ["code_analysis", "pattern_recognition"], "analysis"),
            ("neural-optimizer", ["performance_optimization", "refactoring"], "optimization"),
            ("neural-validator", ["code_validation", "quality_assessment"], "validation"),
            ("neural-synthesizer", ["result_synthesis", "report_generation"], "synthesis"),
        ]
        for agent_id, capabilities, specialization in baseline:
            if agent_id not in self.active_agents:
                ok = await self.agent_registry.register_agent(
                    {
                        "agent_id": agent_id,
                        "capabilities": capabilities,
                        "metadata": {"specialization": specialization},
                    }
                )
                if ok:
                    self.active_agents[agent_id] = NeuralSwarmsAgent(
                        agent_id=agent_id, specialization=specialization
                    )
        await self.swarms_integration.start()
        await self.send_event(
            RealtimeEvent(
                type=EventType.SYSTEM,
                content={
                    "message": (
                        "🧠 Multi-Agent System Ready\n"
                        f"├── Agents: {len(self.active_agents)}\n"
                        f"├── Capabilities: {len(self.agent_registry.get_capability_summary())}\n"
                        f"└── Patterns: {len(getattr(self.swarms_integration, 'coordination_patterns', {}))}"
                    )
                },
            )
        )

    async def handle_multi_agent_message(self, payload: Dict[str, Any]) -> None:
        logger.info(f"🔍 Processing message type: {payload.get('type', 'unknown')}")
        logger.debug(f"🔍 Full payload: {payload}")
        try:
            msg = WebSocketMessage(**payload)
        except ValidationError as e:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": f"Invalid message format: {e}"},
                )
            )
            return
        if msg.type == "query":
            try:
                qc = QueryContent(**msg.content)
                query_text = qc.get_query_text()
                await self.process_multi_agent_query(query_text)
            except Exception as e:
                logger.error(f"Failed to parse QueryContent: {e}")
                # Default query if parsing fails
                await self.process_multi_agent_query("Hello multi-agent system")
        elif msg.type == "init_agent":
            try:
                ic = InitAgentContent(**msg.content)
                await self.configure_multi_agent_system(ic)
            except Exception as e:
                logger.error(f"Failed to parse InitAgentContent: {e}")
                logger.error(f"Message content: {msg.content}")
                # Create a default InitAgentContent with safe defaults
                safe_content = {
                    "model_name": msg.content.get("model_name", "multi-agent-fallback"),
                    "thinking_tokens": msg.content.get("thinking_tokens", 0),
                    "tool_args": msg.content.get("tool_args", {})
                }
                ic = InitAgentContent(**safe_content)
                await self.configure_multi_agent_system(ic)
        else:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.SYSTEM,
                    content={"message": f"Unhandled type: {msg.type}"},
                )
            )

    async def process_multi_agent_query(self, query: str) -> None:
        await self.send_event(
            RealtimeEvent(
                type=EventType.SYSTEM,
                content={"message": f"🔍 Processing: {query[:120]}"},
            )
        )
        strategy_key = self.determine_coordination_strategy(query)
        required = await self.analyze_query_requirements(query)
        agents = self.agent_registry.find_agents_by_capabilities(required)
        if not agents:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.ERROR,
                    content={"message": "No capable agents"},
                )
            )
            return
        pattern = {
            "hierarchical": SwarmsCoordinationPattern.HIERARCHICAL_SWARM,
            "concurrent": SwarmsCoordinationPattern.CONCURRENT_WORKFLOW,
            "sequential": SwarmsCoordinationPattern.SEQUENTIAL_WORKFLOW,
        }.get(strategy_key, SwarmsCoordinationPattern.SEQUENTIAL_WORKFLOW)

        class _Task:
            def __init__(self, content: str):
                self.task_id = str(uuid.uuid4())
                self.content = content

        result = await self.swarms_integration.execute_swarms_coordination(
            _Task(query), pattern
        )
        await self.send_event(
            RealtimeEvent(
                type=EventType.SYSTEM,
                content={
                    "message": f"✅ Completed via {result.strategy}",
                    "metadata": {
                        "strategy": strategy_key,
                        "pattern": result.strategy,
                        "agents_used": [a["agent_id"] for a in agents],
                        "agent_result_count": len(result.agent_results),
                    },
                },
            )
        )

    async def analyze_query_requirements(self, query: str) -> List[str]:
        q = query.lower()
        caps: List[str] = []
        if any(w in q for w in ("analyze", "analysis", "examine")):
            caps.append("code_analysis")
        if any(w in q for w in ("optimize", "improve", "performance")):
            caps.append("performance_optimization")
        if any(w in q for w in ("test", "validate", "verify")):
            caps.append("code_validation")
        if any(w in q for w in ("summarize", "report", "synthesize")):
            caps.append("result_synthesis")
        if not caps:
            caps.append("code_analysis")
        return caps

    def determine_coordination_strategy(self, query: str) -> str:
        q = query.lower()
        if any(k in q for k in ("parallel", "simultaneous", "concurrent")):
            return "concurrent"
        if any(k in q for k in ("hierarchy", "multi-step", "orchestrate")):
            return "hierarchical"
        return "sequential"

    async def configure_multi_agent_system(self, _content: InitAgentContent) -> None:
        await self.send_event(
            RealtimeEvent(
                type=EventType.SYSTEM,
                content={"message": "Configuration updated"},
            )
        )

    async def cleanup_multi_agent_system(self) -> None:
        try:
            await self.swarms_integration.stop()
            await self.task_coordinator.stop()
            await self.agent_registry.stop()
        except Exception as e:  # pragma: no cover
            logger.exception("Cleanup error: %s", e)

    def cleanup(self) -> None:
        """Synchronous cleanup method for compatibility with websocket manager."""
        import asyncio
        try:
            # Try to run cleanup in current event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # Schedule cleanup for later if loop is running
                loop.create_task(self.cleanup_multi_agent_system())
            else:
                # Run cleanup directly if no loop is running
                loop.run_until_complete(self.cleanup_multi_agent_system())
        except Exception as e:  # pragma: no cover
            logger.exception("Cleanup error: %s", e)
        finally:
            # Clear websocket reference
            self.websocket = None

    async def send_event(self, event: RealtimeEvent) -> None:
        if not self.websocket:
            return
        try:
            await self.websocket.send_json(event.model_dump())
        except Exception as e:  # pragma: no cover
            logger.error("Send error: %s", e)

    async def handshake(self) -> None:
        await self.send_event(
            RealtimeEvent(
                type=EventType.CONNECTION_ESTABLISHED if hasattr(EventType, 'CONNECTION_ESTABLISHED') else EventType.SYSTEM,
                content={"message": "✅ Multi-agent session ready"},
            )
        )


ChatSession = MultiAgentChatSession
