import logging
from pathlib import Path
import uuid
from typing import Dict, Optional, TYPE_CHECKING

from fastapi import WebSocket

from ii_agent.core.config.ii_agent_config import IIAgentConfig
from ii_agent.core.storage.files import FileStore

logger = logging.getLogger(__name__)

# Startup diagnostics for import issues
try:
    import sys, os
    logger.info(f"PYTHONPATH={os.getenv('PYTHONPATH')}")
    logger.info(f"sys.path={sys.path}")
    logger.info(f"ii_agent_integration exists at /app/ii_agent_integration: {os.path.exists('/app/ii_agent_integration')}")
    logger.info(f"multi_agent_chat_session.py exists: {os.path.exists('/app/ii_agent_integration/multi_agent_chat_session.py')}")
except Exception as _diag_e:
    logger.warning(f"Startup diagnostics failed: {_diag_e}")

# Import ChatSession with Swarms multi-agent support
try:
    import os
    from ii_agent.integrations.swarms_integration import is_swarms_available

    # Check if multi-agent mode is enabled and Swarms is available
    multi_agent_enabled = os.getenv('II_AGENT_MULTI_AGENT', '').lower() in ('true', '1', 'yes', 'on')

    if multi_agent_enabled and is_swarms_available():
        from ii_agent.server.websocket.swarms_chat_session import SwarmsChatSession as ChatSession
        logger.info("🧠 Multi-agent mode enabled - using Swarms framework")
    else:
        from ii_agent.server.websocket.simple_chat_session import SimpleChatSession as ChatSession
        if multi_agent_enabled:
            logger.warning("⚠️ Multi-agent requested but Swarms not available. Install with: pip install swarms")
        logger.info("🤖 Single-agent mode enabled - using SimpleChatSession")

except ImportError as e:
    logger.warning(f"⚠️ Error importing chat session: {e}")
    from ii_agent.server.websocket.simple_chat_session import SimpleChatSession as ChatSession
    logger.info("🤖 Fallback to SimpleChatSession")

if TYPE_CHECKING:
    from ii_agent.server.websocket.chat_session import ChatSession as ChatSessionType
else:
    ChatSessionType = ChatSession


class ConnectionManager:
    """Manages WebSocket connections and their associated chat sessions."""

    def __init__(
        self,
        file_store: FileStore,
        config: IIAgentConfig,
    ):
        # Active chat sessions mapped by WebSocket
        self.sessions: Dict[WebSocket, ChatSessionType] = {}
        self.file_store = file_store
        self.config = config

    async def connect(self, websocket: WebSocket) -> ChatSessionType:
        """Accept a new WebSocket connection and create a chat session."""
        await websocket.accept()

        # Create workspace for this session if not provided
        session_uuid = websocket.query_params.get("session_uuid")
        if session_uuid is None:
            session_uuid = uuid.uuid4()
        else:
            session_uuid = uuid.UUID(session_uuid)

        # Create a new chat session for this connection
        session = ChatSession(websocket, session_uuid, self.file_store, self.config)
        self.sessions[websocket] = session

        # Quick Fix for upload
        workspace_path = Path(self.config.workspace_root).resolve() / str(session_uuid)
        workspace_path.mkdir(parents=True, exist_ok=True)

        logger.info(
            f"New WebSocket connection and chat session established: {id(websocket)}"
        )
        return session

    def disconnect(self, websocket: WebSocket):
        """Handle WebSocket disconnection and cleanup."""
        logger.info(f"WebSocket disconnecting: {id(websocket)}")

        if websocket in self.sessions:
            session = self.sessions[websocket]
            session.cleanup()
            del self.sessions[websocket]

    def get_session(self, websocket: WebSocket) -> Optional[ChatSessionType]:
        """Get the chat session for a WebSocket connection."""
        return self.sessions.get(websocket)

    def get_connection_count(self) -> int:
        """Get the number of active connections."""
        return len(self.sessions)
