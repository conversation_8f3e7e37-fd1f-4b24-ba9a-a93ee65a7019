# Copy your multi-agent integration into Docker backend
FROM ghcr.io/astral-sh/uv:python3.12-bookworm-slim

# Install the project into `/app`
WORKDIR /app

# Enable bytecode compilation
ENV UV_COMPILE_BYTECODE=1

# Copy from the cache instead of linking since it's a mounted volume
ENV UV_LINK_MODE=copy

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    gnupg \
    lsb-release \
    tmux \
    ffmpeg \
    xvfb \
    git \
    && curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" > /etc/apt/sources.list.d/docker.list \
    && apt-get update \
    && apt-get install -y docker-ce-cli \
    && rm -rf /var/lib/apt/lists/*

# Install code-server
RUN curl -fsSL https://code-server.dev/install.sh | sh

# Install the project's dependencies using the lockfile and settings
COPY uv.lock pyproject.toml ./
RUN uv sync --frozen --no-install-project --no-dev

# Copy the source code but exclude problematic files
COPY src/ ./src/
COPY ws_server.py .
COPY pyproject.toml .
COPY uv.lock .
COPY README.md .

# Copy bootstrap scripts
COPY scripts/ ./scripts/

# Multi-agent integration will be mounted as volumes, not copied
# This avoids build context issues

# Install the project
RUN uv sync --frozen --no-dev

# Set environment variables for multi-agent support
ENV II_AGENT_MULTI_AGENT=${II_AGENT_MULTI_AGENT:-false}
ENV PYTHONPATH="/app/src:/app/ii_agent_integration"

# Default code-server configuration
ENV PASSWORD="password"
ENV HASHED_PASSWORD=""

# Command that starts code-server on the correct port and runs the app
CMD [ "sh", "-c", "\
    if [ -n \"$CODE_SERVER_PORT\" ]; then \
        echo 'Starting code-server on port '$CODE_SERVER_PORT; \
        nohup code-server --host 0.0.0.0 --port $CODE_SERVER_PORT --auth password --disable-telemetry > /dev/null 2>&1 & \
    fi; \
    echo 'Bootstrapping settings (if missing)...'; \
    uv run python scripts/bootstrap_settings.py; \
    echo 'Starting ii-agent WebSocket server...'; \
    echo 'Multi-agent mode: '$II_AGENT_MULTI_AGENT; \
    uv run python ws_server.py \
"]
