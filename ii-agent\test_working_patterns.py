#!/usr/bin/env python3
"""
Test using the WORKING patterns found in the codebase
Based on successful test patterns from test_full_multi_agent.py
"""

import asyncio
import json
import logging
import websockets
import uuid
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_working_multi_agent_pattern():
    """Test using the exact working pattern from test_full_multi_agent.py"""
    
    device_id = str(uuid.uuid4())
    ws_url = f"ws://localhost:8000/ws?device_id={device_id}"
    
    logger.info("🚀 Testing with WORKING multi-agent patterns...")
    
    try:
        async with websockets.connect(ws_url, timeout=15) as websocket:
            logger.info("✅ Connected to WebSocket")
            
            responses = []
            received_handshake = False
            agent_initialized = False
            
            # Listen for initial handshake
            logger.info("👂 Waiting for handshake...")
            
            timeout_start = time.time()
            while time.time() - timeout_start < 30:  # 30 second timeout
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    data = json.loads(message)
                    responses.append(data)
                    
                    msg_type = data.get('type', 'unknown')
                    content = data.get('content', {})
                    
                    logger.info(f"📨 Received: {msg_type}")
                    
                    if msg_type == "connection_established":
                        logger.info(f"✅ Handshake: {content.get('message', 'Connected')}")
                        logger.info(f"🧠 Multi-agent mode: {content.get('multi_agent_mode', False)}")
                        received_handshake = True
                        
                        # Send init_agent message (exact pattern from working test)
                        logger.info("📤 1. Sending init_agent...")
                        init_msg = {
                            "type": "init_agent",
                            "content": {
                                "model_name": "claude-3-5-sonnet-20241022",
                                "tool_args": {
                                    "file_operations": True,
                                    "code_execution": True
                                },
                                "thinking_tokens": 4096
                            }
                        }
                        await websocket.send(json.dumps(init_msg))
                        
                    elif msg_type == "agent_initialized":
                        logger.info(f"🤖 Agent initialized: {content.get('model_name', 'unknown')}")
                        logger.info(f"🛠️ Capabilities: {content.get('multi_agent_capabilities', [])}")
                        agent_initialized = True
                        
                        # Send query message (exact pattern from working test)
                        logger.info("📤 2. Sending query...")
                        query_msg = {
                            "type": "query",
                            "content": {
                                "text": "Hello multi-agent system! Please help me test your capabilities by creating a simple Python function.",
                                "resume": False,
                                "files": []
                            }
                        }
                        await websocket.send(json.dumps(query_msg))
                        
                    elif msg_type == "processing":
                        logger.info(f"⚙️ Processing: {content.get('message', 'Working...')}")
                        
                    elif msg_type == "agent_thinking":
                        agent_id = content.get('agent_id', 'unknown')
                        thinking = content.get('message', '')
                        logger.info(f"🤔 Agent {agent_id} thinking: {thinking[:100]}...")
                        
                    elif msg_type == "agent_response":
                        agent_id = content.get('agent_id', 'unknown')
                        message = content.get('message', 'No message')
                        logger.info(f"💬 Agent {agent_id} response: {message[:100]}...")
                        logger.info(f"📊 Status: {content.get('coordination_status', 'unknown')}")
                        
                    elif msg_type == "coordination_info":
                        logger.info(f"🔄 Coordination: {content}")
                        
                    elif msg_type == "system":
                        logger.info(f"🔧 System: {content.get('message', '')}")
                        
                    elif msg_type == "error":
                        logger.error(f"❌ Error: {content.get('message', 'Unknown error')}")
                        
                    elif msg_type == "stream_complete":
                        logger.info("✅ Stream complete - task finished")
                        break
                        
                except asyncio.TimeoutError:
                    if received_handshake and agent_initialized:
                        logger.info("⏰ Timeout waiting for more responses - test complete")
                        break
                    continue
                except json.JSONDecodeError as e:
                    logger.warning(f"⚠️ JSON decode error: {e}")
                    continue
            
            # Analyze results
            print("\n" + "="*60)
            print("🧪 WORKING PATTERN TEST RESULTS")
            print("="*60)
            
            print(f"📊 Total responses: {len(responses)}")
            print(f"🤝 Handshake received: {received_handshake}")
            print(f"🤖 Agent initialized: {agent_initialized}")
            
            # Count different message types
            message_types = {}
            for response in responses:
                msg_type = response.get('type', 'unknown')
                message_types[msg_type] = message_types.get(msg_type, 0) + 1
            
            print(f"📨 Message types received:")
            for msg_type, count in message_types.items():
                print(f"   {msg_type}: {count}")
            
            # Determine if multi-agent system is working
            multi_agent_indicators = [
                received_handshake,
                agent_initialized,
                'agent_thinking' in message_types,
                'agent_response' in message_types,
                'coordination_info' in message_types,
                len(responses) > 3
            ]
            
            working_indicators = sum(multi_agent_indicators)
            success_rate = (working_indicators / len(multi_agent_indicators)) * 100
            
            print(f"\n🎯 MULTI-AGENT SYSTEM ASSESSMENT:")
            print(f"   Working indicators: {working_indicators}/{len(multi_agent_indicators)} ({success_rate:.1f}%)")
            
            if success_rate >= 80:
                print("   ✅ MULTI-AGENT SYSTEM IS TRULY WORKING!")
                verdict = "working"
            elif success_rate >= 60:
                print("   ⚠️ MULTI-AGENT SYSTEM PARTIALLY WORKING")
                verdict = "partial"
            else:
                print("   ❌ MULTI-AGENT SYSTEM NOT WORKING")
                verdict = "not_working"
            
            print("="*60)
            
            return verdict
            
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return "failed"

async def main():
    """Run the working pattern test"""
    result = await test_working_multi_agent_pattern()
    return 0 if result == "working" else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
