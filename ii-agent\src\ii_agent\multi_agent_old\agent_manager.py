"""AgentManager - High-Level Multi-Agent System Management

Production-grade orchestrator for the entire multi-agent system.
Integrates all components and provides unified management interface.
"""

import asyncio
import logging
import uuid
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timezone
import json

# Import other Phase 2 components
from .base_agent import BaseAgent
from .message_protocol import Message<PERSON>rotocol, MessageType
from .agent_registry import AgentRegistry
from .task_coordinator import TaskCoordinator


class MockWebSocketServer:
    """Mock WebSocket server for testing Phase 1 integration"""
    
    def __init__(self):
        self._running = False
    
    def is_running(self) -> bool:
        """Check if server is running"""
        return self._running
    
    def start(self):
        """Start the mock server"""
        self._running = True
    
    def stop(self):
        """Stop the mock server"""
        self._running = False


class AgentManager:
    """
    High-level manager for the multi-agent system.
    
    Provides:
    - System-wide orchestration
    - Component integration
    - Lifecycle management
    - Monitoring and statistics
    - Event handling and routing
    """
    
    def __init__(self,
                 websocket_host: str = "localhost",
                 websocket_port: int = 8765,
                 registry_heartbeat_timeout: int = 30,
                 coordinator_max_tasks: int = 1000):
        """
        Initialize AgentManager.
        
        Args:
            websocket_host: WebSocket server host
            websocket_port: WebSocket server port
            registry_heartbeat_timeout: Agent heartbeat timeout
            coordinator_max_tasks: Maximum concurrent tasks
        """
        self.websocket_host = websocket_host
        self.websocket_port = websocket_port
        
        # Initialize core components
        self.message_protocol = MessageProtocol()
        self.agent_registry = AgentRegistry(heartbeat_timeout=registry_heartbeat_timeout)
        self.task_coordinator = TaskCoordinator(
            agent_registry=self.agent_registry,
            message_protocol=self.message_protocol,
            max_concurrent_tasks=coordinator_max_tasks
        )
        
        # System state
        self._running = False
        self._system_start_time: Optional[datetime] = None
        
        # WebSocket integration (Phase 1 compatibility)
        self.websocket_server = MockWebSocketServer()  # Mock for testing
        self.connection_manager = None  # Phase 1 ConnectionManager
        self.event_router = None  # Phase 1 EventRouter
        self.client_manager = None  # Phase 1 ClientManager
        
        # Event handlers
        self.event_handlers: Dict[str, List[Callable]] = {}
        
        # Statistics
        self.system_stats = {
            "start_time": None,
            "uptime_seconds": 0,
            "total_messages_processed": 0,
            "total_events_fired": 0
        }
        
        # Logging
        self.logger = logging.getLogger(__name__)
        
        # Setup event handlers for task coordinator
        self.task_coordinator.add_completion_handler(self._handle_task_completion)
        self.task_coordinator.add_failure_handler(self._handle_task_failure)
    
    async def start_system(self) -> bool:
        """
        Start the entire multi-agent system.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            if self._running:
                self.logger.warning("System already running")
                return True
            
            self.logger.info("Starting multi-agent system...")
            
            # Start core components
            await self.agent_registry.start()
            await self.task_coordinator.start()
            
            # Start WebSocket server
            self.websocket_server.start()
            
            # Update system state
            self._running = True
            self._system_start_time = datetime.now(timezone.utc)
            self.system_stats["start_time"] = self._system_start_time.isoformat()
            
            # Fire system started event
            await self._fire_event("system_started", {"timestamp": self._system_start_time.isoformat()})
            
            self.logger.info("Multi-agent system started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start system: {e}")
            return False
    
    async def stop_system(self) -> bool:
        """
        Stop the entire multi-agent system.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self._running:
                self.logger.warning("System not running")
                return True
            
            self.logger.info("Stopping multi-agent system...")
            
            # Fire system stopping event
            await self._fire_event("system_stopping", {"timestamp": datetime.now(timezone.utc).isoformat()})
            
            # Stop core components
            await self.task_coordinator.stop()
            await self.agent_registry.stop()
            
            # Update system state
            self._running = False
            
            self.logger.info("Multi-agent system stopped successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop system: {e}")
            return False
    
    async def create_agent(self,
                          agent_id: str,
                          capabilities: List[str],
                          auto_register: bool = True,
                          auto_connect: bool = True) -> Optional[BaseAgent]:
        """
        Create and optionally register/connect a new agent.
        
        Args:
            agent_id: Unique identifier for the agent
            capabilities: List of agent capabilities
            auto_register: Whether to automatically register the agent
            auto_connect: Whether to automatically connect the agent
            
        Returns:
            BaseAgent instance if successful, None otherwise
        """
        try:
            # Create agent
            agent = BaseAgent(
                agent_id=agent_id,
                capabilities=capabilities,
                websocket_host=self.websocket_host,
                websocket_port=self.websocket_port
            )
            
            # Auto-register if requested
            if auto_register:
                success = await self.register_agent(agent)
                if not success:
                    self.logger.error(f"Failed to register agent {agent_id}")
                    return None
            
            # Auto-connect if requested
            if auto_connect:
                success = await agent.connect()
                if not success:
                    self.logger.error(f"Failed to connect agent {agent_id}")
                    return None
            
            # Fire agent created event
            await self._fire_event("agent_created", {
                "agent_id": agent_id,
                "capabilities": capabilities,
                "auto_registered": auto_register,
                "auto_connected": auto_connect
            })
            
            self.logger.info(f"Agent {agent_id} created successfully")
            return agent
            
        except Exception as e:
            self.logger.error(f"Failed to create agent {agent_id}: {e}")
            return None
    
    async def register_agent(self, agent: BaseAgent) -> bool:
        """
        Register an agent with the system.
        
        Args:
            agent: Agent to register
            
        Returns:
            True if successful, False otherwise
        """
        try:
            success = await self.agent_registry.register_agent(
                agent_id=agent.agent_id,
                capabilities=agent.capabilities,
                connection_id=agent.connection_id
            )
            
            if success:
                # Register agent with itself
                await agent.register()
                
                # Fire agent registered event
                await self._fire_event("agent_registered", {
                    "agent_id": agent.agent_id,
                    "capabilities": agent.capabilities
                })
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to register agent {agent.agent_id}: {e}")
            return False
    
    async def deregister_agent(self, agent_id: str) -> bool:
        """
        Deregister an agent from the system.
        
        Args:
            agent_id: ID of agent to deregister
            
        Returns:
            True if successful, False otherwise
        """
        try:
            success = await self.agent_registry.deregister_agent(agent_id)
            
            if success:
                # Fire agent deregistered event
                await self._fire_event("agent_deregistered", {"agent_id": agent_id})
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to deregister agent {agent_id}: {e}")
            return False
    
    async def submit_task(self,
                         task_type: str,
                         task_data: Dict[str, Any],
                         required_capabilities: List[str],
                         priority: int = 2) -> Optional[str]:
        """
        Submit a task for execution.
        
        Args:
            task_type: Type of task
            task_data: Task data
            required_capabilities: Required capabilities
            priority: Task priority
            
        Returns:
            Task ID if successful, None otherwise
        """
        try:
            task_id = await self.task_coordinator.submit_task(
                task_type=task_type,
                task_data=task_data,
                required_capabilities=required_capabilities,
                priority=priority
            )
            
            # Fire task submitted event
            await self._fire_event("task_submitted", {
                "task_id": task_id,
                "task_type": task_type,
                "required_capabilities": required_capabilities
            })
            
            return task_id
            
        except Exception as e:
            self.logger.error(f"Failed to submit task: {e}")
            return None
    
    async def get_task_status(self, task_id: str) -> Optional[str]:
        """Get status of a specific task"""
        return self.task_coordinator.get_task_status(task_id)
    
    async def get_task_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get result of a specific task"""
        task_info = self.task_coordinator.get_task_info(task_id)
        if task_info and task_info.get("status") == "completed":
            return task_info.get("result")
        return None
    
    def list_agents(self) -> List[Dict[str, Any]]:
        """Get list of all registered agents"""
        return [agent.to_dict() for agent in self.agent_registry.list_all_agents()]
    
    def list_active_agents(self) -> List[Dict[str, Any]]:
        """Get list of active agents only"""
        return [agent.to_dict() for agent in self.agent_registry.list_active_agents()]
    
    def list_active_tasks(self) -> List[Dict[str, Any]]:
        """Get list of all active tasks"""
        return self.task_coordinator.list_active_tasks()
    
    def get_capability_summary(self) -> Dict[str, int]:
        """Get summary of available capabilities"""
        return self.agent_registry.get_capability_summary()
    
    async def broadcast_message(self, message_type: str, data: Dict[str, Any]) -> int:
        """
        Broadcast a message to all active agents.
        
        Args:
            message_type: Type of message to broadcast
            data: Message data
            
        Returns:
            Number of agents the message was sent to
        """
        try:
            # Get all active agents
            active_agents = self.agent_registry.list_active_agents()
            
            sent_count = 0
            for agent_info in active_agents:
                # Create message
                message = self.message_protocol.create_message(
                    message_type=message_type,
                    from_agent="system",
                    to_agent=agent_info.agent_id,
                    data=data
                )
                
                # In full implementation, would send via WebSocket
                # For now, simulate sending
                sent_count += 1
            
            self.system_stats["total_messages_processed"] += sent_count
            
            # Fire broadcast event
            await self._fire_event("message_broadcast", {
                "message_type": message_type,
                "recipients": sent_count
            })
            
            return sent_count
            
        except Exception as e:
            self.logger.error(f"Failed to broadcast message: {e}")
            return 0
    
    def add_event_handler(self, event_type: str, handler: Callable) -> None:
        """
        Add an event handler for a specific event type.
        
        Args:
            event_type: Type of event to handle
            handler: Handler function
        """
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
    
    def remove_event_handler(self, event_type: str, handler: Callable) -> bool:
        """
        Remove an event handler.
        
        Args:
            event_type: Type of event
            handler: Handler function to remove
            
        Returns:
            True if handler was removed, False otherwise
        """
        if event_type in self.event_handlers:
            try:
                self.event_handlers[event_type].remove(handler)
                return True
            except ValueError:
                pass
        return False
    
    async def _fire_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """Fire an event to all registered handlers"""
        self.system_stats["total_events_fired"] += 1
        
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    await handler(event_type, data)
                except Exception as e:
                    self.logger.error(f"Error in event handler for {event_type}: {e}")
    
    async def _handle_task_completion(self, task) -> None:
        """Handle task completion events"""
        await self._fire_event("task_completed", {
            "task_id": task.id,
            "task_type": task.type,
            "agent_id": task.assigned_to,
            "completion_time": task.completed_at
        })
    
    async def _handle_task_failure(self, task, error: str) -> None:
        """Handle task failure events"""
        await self._fire_event("task_failed", {
            "task_id": task.id,
            "task_type": task.type,
            "agent_id": task.assigned_to,
            "error": error,
            "retry_count": task.retry_count
        })
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get comprehensive system statistics"""
        # Update uptime
        if self._system_start_time:
            uptime = (datetime.now(timezone.utc) - self._system_start_time).total_seconds()
            self.system_stats["uptime_seconds"] = uptime
        
        return {
            "system": self.system_stats.copy(),
            "registry": self.agent_registry.get_stats(),
            "coordinator": self.task_coordinator.get_stats(),
            "protocol": self.message_protocol.get_stats(),
            "running": self._running
        }
    
    def is_running(self) -> bool:
        """Check if the system is running"""
        return self._running
    
    async def execute_workflow(self, workflow_task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a multi-step workflow.
        
        Args:
            workflow_task: Workflow definition with steps
            
        Returns:
            Workflow execution result
        """
        try:
            workflow_id = workflow_task.get("id", str(uuid.uuid4()))
            steps = workflow_task.get("steps", [])
            
            results = []
            for step in steps:
                step_num = step.get("step", len(results) + 1)
                required_capabilities = step.get("required_capabilities", [])
                
                # Submit task for this step
                task_id = await self.submit_task(
                    task_type=f"workflow_step_{step_num}",
                    task_data=step,
                    required_capabilities=required_capabilities
                )
                
                if task_id:
                    # Wait for completion (simplified for testing)
                    await asyncio.sleep(0.1)
                    result = await self.get_task_result(task_id)
                    results.append(result)
                else:
                    raise Exception(f"Failed to submit step {step_num}")
            
            return {
                "task_id": workflow_id,  # Add task_id for compatibility
                "workflow_id": workflow_id,
                "status": "completed",
                "steps_completed": len(results),
                "results": results
            }
            
        except Exception as e:
            return {
                "workflow_id": workflow_task.get("id", "unknown"),
                "status": "failed",
                "error": str(e)
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform system health check"""
        health = {
            "status": "healthy" if self._running else "stopped",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "components": {}
        }
        
        try:
            # Check registry
            registry_stats = self.agent_registry.get_stats()
            health["components"]["registry"] = {
                "status": "healthy",
                "agent_count": registry_stats["agent_count"]
            }
            
            # Check coordinator
            coordinator_stats = self.task_coordinator.get_stats()
            health["components"]["coordinator"] = {
                "status": "healthy",
                "active_tasks": coordinator_stats["active_tasks"]
            }
            
            # Check protocol
            protocol_stats = self.message_protocol.get_stats()
            health["components"]["protocol"] = {
                "status": "healthy",
                "pending_acks": protocol_stats["pending_acknowledgments"]
            }
            
        except Exception as e:
            health["status"] = "unhealthy"
            health["error"] = str(e)
        
        return health
