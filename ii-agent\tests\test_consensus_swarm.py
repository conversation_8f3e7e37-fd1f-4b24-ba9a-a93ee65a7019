#!/usr/bin/env python3
import sys
import os
import asyncio
import pytest

# Make the ii-agent directory importable as a namespace package root
THIS_DIR = os.path.dirname(os.path.abspath(__file__))
II_AGENT_ROOT = os.path.abspath(os.path.join(THIS_DIR, ".."))
if II_AGENT_ROOT not in sys.path:
    sys.path.insert(0, II_AGENT_ROOT)

from ii_agent_integration.swarms.swarms_integration import (
    SwarmsFrameworkIntegration,
    SwarmsCoordinationPattern,
)


class DummyTask:
    def __init__(self, task_id, content, agents, deterministic_agent_fn=None, consensus_similarity=0.8):
        self.task_id = task_id
        self.content = content
        self.agents = agents
        self.deterministic_agent_fn = deterministic_agent_fn
        self.consensus_similarity = consensus_similarity


@pytest.mark.asyncio
async def test_consensus_swarm_majority_agreement():
    async def det_fn(agent, task):
        # Two agents agree, one dissents
        outputs = {
            "a1": {"output": "the answer is 42", "confidence": 0.9, "latency_ms": 8.0},
            "a2": {"output": "the answer is 42", "confidence": 0.85, "latency_ms": 12.0},
            "a3": {"output": "different result", "confidence": 0.6, "latency_ms": 15.0},
        }
        base = outputs.get(str(agent), {"output": "the answer is 42", "confidence": 0.8, "latency_ms": 10.0})
        return {"success": True, **base}

    task = DummyTask(
        task_id="t-majority",
        content="compute answer",
        agents=["a1", "a2", "a3"],
        deterministic_agent_fn=det_fn,
    )

    integration = SwarmsFrameworkIntegration(coordination_hub=None)
    result = await integration.execute_swarms_coordination(task, SwarmsCoordinationPattern.CONSENSUS_SWARM)

    assert result.success is True
    assert result.strategy == "consensus_swarm"
    md = result.metadata or {}
    assert md.get("total_participants") == 3
    assert md.get("winning_cluster_size") == 2
    assert md.get("consensus_reached") is True
    assert isinstance(md.get("confidence"), float) and md["confidence"] >= 0.7
    # Exactly one dissenter expected
    assert set(md.get("dissenters") or []) == {"a3"}

    # Ensure agent_results mark consensus membership
    in_consensus = [r for r in result.agent_results if r.get("result", {}).get("in_consensus")]
    assert len(in_consensus) == 2


@pytest.mark.asyncio
async def test_consensus_swarm_all_different_no_consensus():
    async def det_fn(agent, task):
        # All agents produce distinct outputs
        return {
            "success": True,
            "output": f"unique-output-{agent}",
            "confidence": 0.7,
            "latency_ms": 10.0,
        }

    task = DummyTask(
        task_id="t-noconsensus",
        content="compare",
        agents=["x1", "x2", "x3"],
        deterministic_agent_fn=det_fn,
    )

    integration = SwarmsFrameworkIntegration(coordination_hub=None)
    result = await integration.execute_swarms_coordination(task, SwarmsCoordinationPattern.CONSENSUS_SWARM)

    assert result.success is True  # execution succeeded, but consensus may be false
    md = result.metadata or {}
    assert md.get("total_participants") == 3
    assert md.get("winning_cluster_size") == 1
    assert md.get("consensus_reached") is False
    # There should be 2 dissenters (everyone except the chosen singleton)
    assert len(md.get("dissenters") or []) == 2
