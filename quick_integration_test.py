#!/usr/bin/env python3
"""
Quick integration test for ii-agent multi-agent WebSocket functionality.
This test connects to a running ii-agent server and validates the integration.
"""

import asyncio
import websockets
import json
import os
import sys
from pathlib import Path

class QuickIntegrationTest:
    def __init__(self, host="localhost", port=8000):
        self.host = host
        self.port = port
        self.ws_url = f"ws://{host}:{port}/ws"
        
    async def test_basic_connection(self):
        """Test basic WebSocket connection."""
        print("🔌 Testing WebSocket connection...")
        
        try:
            async with websockets.connect(self.ws_url) as websocket:
                print(f"✅ Connected to {self.ws_url}")
                
                # Send a simple ping/test message
                test_msg = {
                    "type": "test",
                    "message": "Hello from integration test",
                    "timestamp": "2025-08-08T19:35:00Z"
                }
                
                await websocket.send(json.dumps(test_msg))
                print(f"📤 Sent test message")
                
                # Try to receive a response (with timeout)
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    print(f"📥 Received response: {response}")
                    return True
                except asyncio.TimeoutError:
                    print("⏰ No response received (this is OK for basic connection test)")
                    return True
                    
        except ConnectionRefusedError:
            print("❌ Connection refused - is the server running?")
            return False
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False
    
    async def test_environment_detection(self):
        """Test if we can detect the server's mode."""
        print("\n🔍 Detecting server mode...")
        
        multi_agent_env = os.getenv("II_AGENT_MULTI_AGENT", "false").lower() == "true"
        mode = "multi-agent" if multi_agent_env else "single-agent"
        
        print(f"📊 Client environment suggests: {mode} mode")
        
        # Try to send a mode detection request
        try:
            async with websockets.connect(self.ws_url) as websocket:
                mode_request = {
                    "type": "info",
                    "request": "server_mode",
                    "message": "What mode is the server running in?"
                }
                
                await websocket.send(json.dumps(mode_request))
                print("📤 Sent mode detection request")
                
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    print(f"📥 Server response: {response}")
                    return True
                except asyncio.TimeoutError:
                    print("⏰ No specific mode response (server may not support this query)")
                    return True
                    
        except Exception as e:
            print(f"❌ Mode detection failed: {e}")
            return False

async def main():
    """Run the quick integration test."""
    print("🧪 II-Agent Multi-Agent Integration - Quick Test")
    print("=" * 55)
    
    # Check current environment
    multi_agent_mode = os.getenv("II_AGENT_MULTI_AGENT", "false").lower() == "true"
    mode_text = "multi-agent" if multi_agent_mode else "single-agent"
    print(f"🔧 Current environment mode: {mode_text}")
    
    tester = QuickIntegrationTest()
    
    # Run tests
    tests = [
        ("Basic WebSocket Connection", tester.test_basic_connection()),
        ("Environment Detection", tester.test_environment_detection()),
    ]
    
    results = []
    for test_name, test_coro in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = await test_coro
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 55)
    print("📊 QUICK TEST RESULTS")
    print("=" * 55)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print(f"\n🎉 Integration test PASSED! Server is responding in {mode_text} mode.")
    else:
        print(f"\n⚠️ Some tests failed. Make sure the server is running:")
        print(f"   python run_ii_agent_server.py")
    
    print(f"\n💡 To test both modes:")
    print(f"   Single-agent: Remove-Item Env:II_AGENT_MULTI_AGENT -ErrorAction SilentlyContinue")
    print(f"   Multi-agent:  $env:II_AGENT_MULTI_AGENT = 'true'")

if __name__ == "__main__":
    asyncio.run(main())
