"""
Direct Phase 2 WASM Integration Test
Tests the integration by directly importing and running the components
"""

import sys
import os
import time

# Add current directory to path for direct imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_phase_components():
    """Test Phase 1 and Phase 2 components directly."""
    print("🧪 Direct Phase Component Test")
    print("=" * 40)
    
    # Test Phase 1 import
    try:
        import phase1_real_neural_networks as p1
        print("✅ Phase 1 import successful")
        phase1_available = True
        
        # Test Phase 1 functionality
        config = p1.NetworkConfig()
        network = p1.SimpleNeuralNetwork(config)
        print(f"  Phase 1 Network: {type(network).__name__}")
        
    except Exception as e:
        print(f"❌ Phase 1 import failed: {e}")
        phase1_available = False
    
    # Test Phase 2 import  
    try:
        import phase2_wasm_integration as p2
        print("✅ Phase 2 import successful")
        phase2_available = True
        
        # Test Phase 2 functionality
        wasm_config = p2.WASMConfig()
        print(f"  Phase 2 Config: {type(wasm_config).__name__}")
        
    except Exception as e:
        print(f"❌ Phase 2 import failed: {e}")
        phase2_available = False
    
    return phase1_available, phase2_available

def test_simple_neural_direct():
    """Test SimpleNeuralAgent directly without relative imports."""
    print("\n🔧 Direct SimpleNeuralAgent Test")
    print("=" * 40)
    
    try:
        # Import simple_neural directly
        import simple_neural
        print("✅ SimpleNeuralAgent import successful")
        
        print(f"Phase 1 Available: {simple_neural.PHASE1_AVAILABLE}")
        print(f"Phase 2 Available: {simple_neural.PHASE2_AVAILABLE}")
        print(f"Neural Available: {simple_neural.NEURAL_AVAILABLE}")
        
        # Test agent creation
        agent = simple_neural.SimpleNeuralAgent()
        print(f"Agent Active Phase: {agent.active_phase}")
        
        # Test analysis
        test_messages = [
            "implement machine learning algorithm",
            "research quantum computing", 
            "analyze data visualization",
            "debug web application"
        ]
        
        results = []
        times = []
        
        for i, message in enumerate(test_messages):
            start_time = time.perf_counter()
            result = agent.get_neural_analysis(message)
            end_time = time.perf_counter()
            
            processing_time = (end_time - start_time) * 1000
            times.append(processing_time)
            results.append(result)
            
            print(f"Test {i+1}: {result['agent_type']} ({result['confidence']:.3f}) - {processing_time:.3f}ms")
        
        avg_time = sum(times) / len(times)
        print(f"Average Processing Time: {avg_time:.3f}ms")
        
        # Test performance info
        perf_info = agent.get_performance_info()
        print(f"\nPerformance Info:")
        print(f"  Active Phase: {perf_info['active_phase']}")
        print(f"  Capabilities: {perf_info['capabilities']}")
        
        return True, results, times
        
    except Exception as e:
        print(f"❌ SimpleNeuralAgent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False, [], []

def main():
    """Run comprehensive direct tests."""
    print("🚀 Phase 2 WASM Integration - Direct Test")
    print("=" * 50)
    
    # Test components
    p1_avail, p2_avail = test_phase_components()
    
    # Test integration
    success, results, times = test_simple_neural_direct()
    
    print(f"\n📊 Test Summary:")
    print(f"  Phase 1 Available: {p1_avail}")
    print(f"  Phase 2 Available: {p2_avail}")
    print(f"  Integration Test: {'✅ PASSED' if success else '❌ FAILED'}")
    
    if success and results:
        # Agent selection accuracy
        expected_agents = ["coder", "researcher", "data_scientist", "coder"]
        correct = sum(1 for i, r in enumerate(results) if r['agent_type'] == expected_agents[i])
        accuracy = (correct / len(expected_agents)) * 100
        print(f"  Agent Selection Accuracy: {accuracy:.1f}% ({correct}/{len(expected_agents)})")
        
        # Performance
        if times:
            avg_time = sum(times) / len(times)
            print(f"  Average Response Time: {avg_time:.3f}ms")
    
    print(f"\n✅ Direct Integration Test Complete!")
    
    return {
        "phase1_available": p1_avail,
        "phase2_available": p2_avail, 
        "integration_success": success,
        "test_passed": success
    }

if __name__ == "__main__":
    try:
        result = main()
        print(f"\nFinal Result: {result}")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
