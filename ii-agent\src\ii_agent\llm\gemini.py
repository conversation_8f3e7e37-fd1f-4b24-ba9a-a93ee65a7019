"""LLM client for Google Gemini models."""

import logging
import os
from typing import Any, Dict, List, Optional, Tuple

from ii_agent.core.config.llm_config import LLMConfig
from ii_agent.llm.base import LLMClient
from ii_agent.llm.message_history import LLMMessages, MessageHistory
from ii_agent.llm.base import TextResult

logger = logging.getLogger(__name__)

try:
    import google.generativeai as genai
    from google.generativeai import types
    from google.generativeai.types import HarmCategory, HarmBlockThreshold
    GEMINI_AVAILABLE = True
except ImportError:
    logger.warning("Google Generative AI library not available. Install with: pip install google-generativeai")
    GEMINI_AVAILABLE = False
    genai = None
    types = None
    HarmCategory = None
    HarmBlockThreshold = None


class GeminiDirectClient(LLMClient):
    """Direct client for Google Gemini models."""

    def __init__(self, llm_config: LLMConfig):
        super().__init__()
        self.llm_config = llm_config
        
        if not GEMINI_AVAILABLE:
            raise ImportError("Google Generative AI library not available. Install with: pip install google-generativeai")
        
        # Configure Gemini
        api_key = os.getenv("GOOGLE_AI_STUDIO_API_KEY") or os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GOOGLE_AI_STUDIO_API_KEY or GEMINI_API_KEY environment variable is required")
        
        genai.configure(api_key=api_key)
        
        # Initialize model - use latest Gemini 2.5 Flash
        self.model_name = getattr(llm_config, 'model_name', None) or "gemini-2.0-flash-exp"
        
        # Safety settings
        self.safety_settings = {
            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
        }
        
        # Generation config
        self.generation_config = {
            "temperature": getattr(llm_config, 'temperature', None) or 0.7,
            "top_p": getattr(llm_config, 'top_p', None) or 0.9,
            "max_output_tokens": getattr(llm_config, 'max_tokens', None) or 8192,
        }
        
        self.model = genai.GenerativeModel(
            model_name=self.model_name,
            generation_config=self.generation_config,
            safety_settings=self.safety_settings
        )
        
        logger.info(f"Initialized Gemini client with model: {self.model_name}")

    def _convert_messages_to_gemini_format(self, messages: LLMMessages) -> List[Dict[str, Any]]:
        """Convert internal LLMMessages (list of turns) to Gemini chat history format.
        Each item in the returned list is a dict with keys: role ('user'|'model') and parts ([{"text": str}]).
        """
        from ii_agent.llm.base import TextPrompt, ToolFormattedResult, ImageBlock, ToolCall, TextResult
        gemini_messages: List[Dict[str, Any]] = []

        for turn in messages:
            if not turn:
                continue
            # Determine role by inspecting the first block type in the turn
            is_user_turn = isinstance(turn[0], (TextPrompt, ToolFormattedResult, ImageBlock))
            role = "user" if is_user_turn else "model"
            # Collect textual content from the turn
            texts: List[str] = []
            for block in turn:
                if isinstance(block, TextPrompt):
                    texts.append(block.text)
                elif isinstance(block, ToolFormattedResult):
                    # Can be str or list of dicts; extract text parts when available
                    if isinstance(block.tool_output, str):
                        texts.append(block.tool_output)
                    elif isinstance(block.tool_output, list):
                        for item in block.tool_output:
                            if isinstance(item, dict) and item.get("type") == "text":
                                texts.append(item.get("text", ""))
                elif isinstance(block, ImageBlock):
                    # Skip binary content; add a placeholder to preserve context
                    texts.append("[Image attached]")
                elif isinstance(block, TextResult):
                    texts.append(block.text)
                elif isinstance(block, ToolCall):
                    # Ignore tool call objects in assistant turn history for Gemini
                    continue
                else:
                    # Fallback to string representation
                    try:
                        texts.append(str(block))
                    except Exception:
                        pass
            combined = "\n".join(t for t in texts if t)
            if combined:
                gemini_messages.append({
                    "role": role,
                    "parts": [{"text": combined}]
                })
        return gemini_messages

    def _extract_system_prompt(self, messages: LLMMessages) -> str:
        """Extract system prompt from messages."""
        system_prompt = ""
        for message in messages:
            if message.role == "system":
                system_prompt += message.content + "\n"
        return system_prompt.strip()

    def _convert_tools_to_gemini_format(self, tools: List[Any]) -> List[Any]:
        """Convert tool definitions to Gemini function format.

        Gemini's FunctionDeclaration schema is more restrictive than full JSON Schema.
        We sanitize tool.input_schema / parameters recursively to remove unsupported keys
        like: minimum, maximum, pattern, additionalProperties, examples, default, title, etc.
        """
        try:
            allowed_schema_keys = {"type", "description", "properties", "required", "items", "enum"}

            def sanitize(schema: Any) -> Any:
                if not isinstance(schema, dict):
                    return schema
                cleaned: Dict[str, Any] = {}
                for k, v in schema.items():
                    if k not in allowed_schema_keys:
                        continue  # drop unsupported key
                    if k == "properties" and isinstance(v, dict):
                        cleaned_props: Dict[str, Any] = {}
                        for pk, pv in v.items():
                            cleaned_props[pk] = sanitize(pv)
                        cleaned[k] = cleaned_props
                    elif k == "items":
                        cleaned[k] = sanitize(v)
                    else:
                        cleaned[k] = v
                return cleaned

            def build_declaration(name: str, description: str, raw_parameters: Any):
                params = sanitize(raw_parameters) if isinstance(raw_parameters, dict) else {}
                return types.FunctionDeclaration(
                    name=name,
                    description=description,
                    parameters=params,
                )

            declarations: List[Any] = []

            # ToolParam objects (preferred / upstream)
            if tools and hasattr(tools[0], 'name'):
                for tool in tools:
                    try:
                        raw_parameters = getattr(tool, 'input_schema', {})
                        declarations.append(
                            build_declaration(tool.name, tool.description, raw_parameters)
                        )
                    except Exception as inner_e:
                        logger.warning(f"Failed to convert tool {getattr(tool,'name','?')}: {inner_e}")
            else:
                # Dict fallback format
                for tool in tools:
                    if not isinstance(tool, dict) or 'function' not in tool:
                        continue
                    func_def = tool['function']
                    name = func_def.get('name', '')
                    description = func_def.get('description', '')
                    raw_parameters = func_def.get('parameters', {})
                    declarations.append(build_declaration(name, description, raw_parameters))

            return [types.Tool(function_declarations=declarations)] if declarations else []
        except Exception as e:
            logger.warning(f"Error converting tools to Gemini format: {e}")
            return []

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: List[Any] = [],  # Accept both ToolParam objects and dicts
        tool_choice: Dict[str, str] | None = None,
        thinking_tokens: int | None = None,
    ) -> Tuple[List[Any], Dict[str, Any]]:
        """Generate a response with optional tool calls using Gemini native function calling.

        Changes vs previous implementation:
        - Pass the full prior conversation as structured contents (not just last turn) so tools have context.
        - Safely parse function_call parts before attempting to coerce to text (prevents part.function_call -> text errors).
        - Return ToolCall objects first when present; avoid duplicating textual summary if function calls exist.
        """
        if not GEMINI_AVAILABLE:
            return [TextResult(text="Gemini API not available. Install google-generativeai")], {"error": "gemini_unavailable"}

        try:
            from ii_agent.llm.base import ToolCall

            gemini_messages = self._convert_messages_to_gemini_format(messages)

            # Inject system prompt into first user message (Gemini lacks dedicated system role)
            if system_prompt:
                if gemini_messages and gemini_messages[0].get("role") == "user":
                    gemini_messages[0]["parts"][0]["text"] = f"{system_prompt}\n\n{gemini_messages[0]['parts'][0]['text']}".strip()
                else:
                    gemini_messages.insert(0, {"role": "user", "parts": [{"text": system_prompt}]})

            # Use chat approach: history + last message
            history = gemini_messages[:-1] if len(gemini_messages) > 1 else []
            last_text = gemini_messages[-1]["parts"][0]["text"] if gemini_messages else "Hello"

            # Prepare generation config
            generation_config = self.generation_config.copy()
            generation_config["temperature"] = temperature
            generation_config["max_output_tokens"] = max_tokens

            gemini_tools = self._convert_tools_to_gemini_format(tools) if tools else None

            if gemini_tools:
                # For tools, use generate_content with full conversation history
                # Build proper conversation format with history
                conversation_contents = []
                for msg in gemini_messages:
                    conversation_contents.append(msg)
                
                response = self.model.generate_content(
                    contents=conversation_contents if conversation_contents else last_text,
                    generation_config=generation_config,
                    tools=gemini_tools,
                )
            else:
                # For non-tool calls, use chat
                chat = self.model.start_chat(history=history)
                response = chat.send_message(last_text, generation_config=generation_config)

            results: List[Any] = []

            # Safe parsing: check candidates and parts without forcing text conversion
            if hasattr(response, "candidates") and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, "content") and hasattr(candidate.content, "parts"):
                    for part in candidate.content.parts:
                        # Function call
                        if hasattr(part, "function_call") and part.function_call:
                            fc = part.function_call
                            try:
                                args_dict = dict(fc.args) if getattr(fc, "args", None) else {}
                            except Exception:
                                args_dict = {}
                            results.append(
                                ToolCall(
                                    tool_call_id=f"call_{fc.name}_{len(results)}",
                                    tool_name=fc.name,
                                    tool_input=args_dict,
                                )
                            )
                        # Text chunk (only if no function_call attribute)
                        elif hasattr(part, "text") and part.text and not hasattr(part, "function_call"):
                            results.append(TextResult(text=part.text))

            # Fallback: if no parts or candidates, try top-level text (safely)
            if not results:
                try:
                    top_text = getattr(response, "text", None) or ""
                    results.append(TextResult(text=top_text))
                except Exception:
                    # If text access fails, provide empty text result
                    results.append(TextResult(text=""))

            metadata = {
                "model": self.model_name,
                "input_tokens_est": self._estimate_tokens(str(gemini_messages)),
                "output_tokens_est": self._estimate_tokens(str(results)),
                "supports_tools": True,
            }
            return results, metadata
        except Exception as e:
            logger.error(f"Error generating response with Gemini: {e}")
            return [TextResult(text=f"Error: {str(e)}")], {"error": str(e)}

    async def generate_response(
        self,
        messages: LLMMessages,
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> Tuple[List[TextResult], Dict[str, Any]]:
        """Generate response using Gemini API."""
        
        try:
            # Extract system prompt and convert messages
            system_prompt = self._extract_system_prompt(messages)
            gemini_messages = self._convert_messages_to_gemini_format(messages)
            
            # Prepare the prompt
            if system_prompt and gemini_messages:
                # Prepend system prompt to first user message
                if gemini_messages[0]["role"] == "user":
                    gemini_messages[0]["parts"][0]["text"] = f"{system_prompt}\n\n{gemini_messages[0]['parts'][0]['text']}"
            elif system_prompt:
                # Only system prompt, create a user message
                gemini_messages = [{
                    "role": "user",
                    "parts": [{"text": system_prompt}]
                }]
            
            # Start chat session
            chat = self.model.start_chat(history=gemini_messages[:-1] if len(gemini_messages) > 1 else [])
            
            # Get the last message content
            last_message_content = gemini_messages[-1]["parts"][0]["text"] if gemini_messages else "Hello"
            
            # Generate response
            response = await self._async_generate_content(chat, last_message_content)
            
            # Process response
            content = response.text if response.text else ""
            
            # Create result
            results = [TextResult(text=content)] if content else [TextResult(text="")]
            
            # Metadata
            metadata = {
                "model": self.model_name,
                "input_tokens": self._estimate_tokens(str(gemini_messages)),
                "output_tokens": self._estimate_tokens(content),
                "raw_response": response
            }
            
            return results, metadata
            
        except Exception as e:
            logger.error(f"Gemini API error: {e}")
            # Return error as text result
            error_text = f"Error generating response: {str(e)}"
            return [TextResult(text=error_text)], {"error": str(e)}

    async def _async_generate_content(self, chat, message: str):
        """Generate content asynchronously."""
        import asyncio
        
        # Run in thread pool since Gemini SDK doesn't have native async support
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, chat.send_message, message)

    def _estimate_tokens(self, text: str) -> int:
        """Estimate token count (rough approximation)."""
        # Rough estimation: 1 token ≈ 4 characters for English text
        return len(text) // 4

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model."""
        return {
            "model_name": self.model_name,
            "provider": "google",
            "max_tokens": self.generation_config.get("max_output_tokens", 8192),
            "temperature": self.generation_config.get("temperature", 0.7),
            "supports_tools": True,  # Now supports native tool calls!
            "supports_vision": "vision" in self.model_name.lower()
        }

    def count_tokens(self, messages: LLMMessages) -> int:
        """Count tokens in messages."""
        total_text = ""
        for message in messages:
            total_text += message.content + " "
        return self._estimate_tokens(total_text)