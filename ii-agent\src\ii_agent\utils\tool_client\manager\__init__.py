"""Toolkit manager public API with safe, optional imports.

This module exposes common manager types while avoiding hard failures on
platforms where optional runtime dependencies (like pexpect) are unavailable.
"""

from .str_replace_manager import StrReplaceManager
from .model import SessionResult, StrReplaceResponse, StrReplaceToolError

# Optional imports: these may fail on Windows environments where pexpect/pty
# are not fully supported. Keep them optional so unit tests that don't require
# terminal sessions can still import successfully.
try:  # pragma: no cover - platform-dependent
    from .terminal_manager import PexpectSessionManager  # type: ignore
except Exception:  # pragma: no cover - safely degrade
    PexpectSessionManager = None  # type: ignore

try:  # pragma: no cover - optional dependency
    from .tmux_terminal_manager import TmuxSessionManager  # type: ignore
except Exception:  # pragma: no cover - safely degrade
    TmuxSessionManager = None  # type: ignore

__all__ = [
    "SessionResult",
    "StrReplaceResponse",
    "StrReplaceToolError",
    "StrReplaceManager",
]

# Expose optional symbols if available
if PexpectSessionManager is not None:
    __all__.append("PexpectSessionManager")
if TmuxSessionManager is not None:
    __all__.append("TmuxSessionManager")
