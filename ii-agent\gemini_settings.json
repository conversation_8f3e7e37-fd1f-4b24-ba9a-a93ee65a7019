{"llm_configs": {"gemini-2.5-pro-preview-05-06": {"model": "gemini-2.5-pro-preview-05-06", "api_key": "AIzaSyCwv3H86UPwZvGVdUpKev0d_XBSCEaSi-0", "base_url": null, "max_retries": 3, "max_message_chars": 30000, "temperature": 0.0, "vertex_region": null, "vertex_project_id": null, "api_type": "gemini", "thinking_tokens": 0, "azure_endpoint": null, "azure_api_version": null, "cot_model": false}, "gemini-2.5-flash": {"model": "gemini-2.5-flash", "api_key": "AIzaSyCwv3H86UPwZvGVdUpKev0d_XBSCEaSi-0", "base_url": null, "max_retries": 3, "max_message_chars": 30000, "temperature": 0.0, "vertex_region": null, "vertex_project_id": null, "api_type": "gemini", "thinking_tokens": 0, "azure_endpoint": null, "azure_api_version": null, "cot_model": false}, "gemini/gemini-2.5-flash": {"model": "gemini-2.5-flash", "api_key": "AIzaSyCwv3H86UPwZvGVdUpKev0d_XBSCEaSi-0", "base_url": null, "max_retries": 3, "max_message_chars": 30000, "temperature": 0.0, "vertex_region": null, "vertex_project_id": null, "api_type": "gemini", "thinking_tokens": 0, "azure_endpoint": null, "azure_api_version": null, "cot_model": false}}, "search_config": {"firecrawl_api_key": null, "serpapi_api_key": null, "tavily_api_key": null, "jina_api_key": null}, "media_config": {"gcp_project_id": null, "gcp_location": null, "gcs_output_bucket": null, "google_ai_studio_api_key": null}, "audio_config": {"openai_api_key": null, "azure_endpoint": null, "azure_api_version": null}, "sandbox_config": {"mode": "local", "template_id": null, "sandbox_api_key": null, "service_port": 17300}, "client_config": null, "third_party_integration_config": {"neon_db_api_key": null, "openai_api_key": null, "vercel_api_key": null}}