"""
Message Router - Phase 2 Multi-Agent Message Routing

Intelligent message routing system that:
- Routes messages based on query analysis
- Manages coordination between single-agent, multi-agent, and swarms modes
- Handles response aggregation and formatting
- Provides real-time coordination feedback
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .query_analyzer import QueryA<PERSON>y<PERSON>, QueryAnalysis, CoordinationStrategy
from .agent_registry import AgentRegistry
from .task_coordinator import TaskCoordinator
from .swarms_integration import SwarmsFrameworkIntegration, SwarmsCoordinationPattern

logger = logging.getLogger(__name__)


class RoutingResult(Enum):
    """Message routing results"""
    SUCCESS = "success"
    PARTIAL_SUCCESS = "partial_success"
    FAILURE = "failure"
    TIMEOUT = "timeout"


@dataclass
class RoutingResponse:
    """Response from message routing"""
    result: RoutingResult
    content: str
    metadata: Dict[str, Any]
    processing_time: float
    agents_used: List[str]
    coordination_pattern: Optional[str] = None


class MessageRouter:
    """Intelligent message routing for multi-agent coordination"""
    
    def __init__(self, agent_registry: AgentRegistry, task_coordinator: TaskCoordinator,
                 swarms_integration: SwarmsFrameworkIntegration):
        self.agent_registry = agent_registry
        self.task_coordinator = task_coordinator
        self.swarms_integration = swarms_integration
        self.query_analyzer = QueryAnalyzer()
        self.logger = logging.getLogger(__name__)
        
        # Routing statistics
        self.routing_stats = {
            "total_queries": 0,
            "single_agent_queries": 0,
            "multi_agent_queries": 0,
            "swarms_queries": 0,
            "successful_routes": 0,
            "failed_routes": 0,
            "average_processing_time": 0.0
        }
    
    async def route_message(self, query_text: str, session_id: str, 
                          context: Optional[Dict[str, Any]] = None) -> RoutingResponse:
        """
        Route message to appropriate coordination strategy.
        
        Args:
            query_text: User's query
            session_id: Session identifier
            context: Optional context information
            
        Returns:
            RoutingResponse with results
        """
        start_time = time.time()
        
        try:
            self.routing_stats["total_queries"] += 1
            
            # Analyze the query
            analysis = await self.query_analyzer.analyze_query(query_text, context)
            
            self.logger.info(f"Query analysis: {analysis.coordination_strategy.value} "
                           f"with {analysis.complexity.value} complexity")
            
            # Route based on coordination strategy
            if analysis.coordination_strategy == CoordinationStrategy.SINGLE_AGENT:
                response = await self._route_single_agent(query_text, session_id, analysis)
                self.routing_stats["single_agent_queries"] += 1
                
            elif analysis.coordination_strategy == CoordinationStrategy.MULTI_AGENT:
                response = await self._route_multi_agent(query_text, session_id, analysis)
                self.routing_stats["multi_agent_queries"] += 1
                
            elif analysis.coordination_strategy == CoordinationStrategy.SWARMS_COORDINATION:
                response = await self._route_swarms(query_text, session_id, analysis)
                self.routing_stats["swarms_queries"] += 1
                
            else:
                # Fallback to single agent
                response = await self._route_single_agent(query_text, session_id, analysis)
                self.routing_stats["single_agent_queries"] += 1
            
            # Update statistics
            processing_time = time.time() - start_time
            response.processing_time = processing_time
            
            if response.result == RoutingResult.SUCCESS:
                self.routing_stats["successful_routes"] += 1
            else:
                self.routing_stats["failed_routes"] += 1
                
            # Update average processing time
            total_queries = self.routing_stats["total_queries"]
            current_avg = self.routing_stats["average_processing_time"]
            self.routing_stats["average_processing_time"] = (
                (current_avg * (total_queries - 1) + processing_time) / total_queries
            )
            
            # Add analysis to response metadata
            response.metadata.update({
                "query_analysis": {
                    "complexity": analysis.complexity.value,
                    "confidence": analysis.confidence_score,
                    "reasoning": analysis.reasoning
                }
            })
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error routing message: {e}")
            processing_time = time.time() - start_time
            self.routing_stats["failed_routes"] += 1
            
            return RoutingResponse(
                result=RoutingResult.FAILURE,
                content=f"Error processing request: {str(e)}",
                metadata={"error": str(e)},
                processing_time=processing_time,
                agents_used=[]
            )
    
    async def _route_single_agent(self, query_text: str, session_id: str, 
                                 analysis: QueryAnalysis) -> RoutingResponse:
        """Route to single agent processing"""
        try:
            self.logger.info(f"Routing to single agent for session {session_id}")
            
            # For now, we'll simulate single agent processing
            # In a real implementation, this would interface with the existing agent
            await asyncio.sleep(0.1)  # Simulate processing
            
            response_content = (
                f"Single agent processing complete for query: '{query_text[:50]}...'\n"
                f"Complexity: {analysis.complexity.value}\n"
                f"Capabilities used: {', '.join(analysis.required_capabilities)}"
            )
            
            return RoutingResponse(
                result=RoutingResult.SUCCESS,
                content=response_content,
                metadata={
                    "strategy": "single_agent",
                    "capabilities": analysis.required_capabilities,
                    "estimated_time": analysis.processing_time_estimate
                },
                processing_time=0.0,  # Will be set by caller
                agents_used=["primary_agent"]
            )
            
        except Exception as e:
            self.logger.error(f"Single agent routing failed: {e}")
            return RoutingResponse(
                result=RoutingResult.FAILURE,
                content=f"Single agent processing failed: {str(e)}",
                metadata={"error": str(e)},
                processing_time=0.0,
                agents_used=[]
            )
    
    async def _route_multi_agent(self, query_text: str, session_id: str,
                                analysis: QueryAnalysis) -> RoutingResponse:
        """Route to multi-agent coordination"""
        try:
            self.logger.info(f"Routing to multi-agent coordination for session {session_id}")
            
            # Find available agents with required capabilities
            available_agents = []
            for capability in analysis.required_capabilities:
                agents = await self.agent_registry.discover_agents([capability])
                available_agents.extend([agent.agent_id for agent in agents])
            
            # Remove duplicates
            available_agents = list(set(available_agents))
            
            if not available_agents:
                # Register temporary agents for the capabilities we need
                for i, capability in enumerate(analysis.required_capabilities):
                    agent_id = f"temp_agent_{capability}_{session_id}"
                    await self.agent_registry.register_agent(
                        agent_id_or_info=agent_id,
                        capabilities=[capability],
                        metadata={"temporary": True, "session": session_id}
                    )
                    available_agents.append(agent_id)
            
            # Create and submit tasks for each capability
            task_ids = []
            for capability in analysis.required_capabilities:
                task_id = await self.task_coordinator.submit_task(
                    task_type=f"multi_agent_{capability}",
                    task_data={"query": query_text, "capability": capability},
                    required_capabilities=[capability],
                    timeout_seconds=int(analysis.processing_time_estimate)
                )
                task_ids.append(task_id)
            
            # Wait for tasks to be assigned (simulate processing)
            await asyncio.sleep(0.2)
            
            # Simulate task results
            results = []
            for i, task_id in enumerate(task_ids):
                capability = analysis.required_capabilities[i]
                results.append(f"{capability}: Processing completed for query segment")
            
            response_content = (
                f"Multi-agent coordination complete:\n"
                f"Agents used: {len(available_agents)}\n"
                f"Capabilities: {', '.join(analysis.required_capabilities)}\n"
                f"Results:\n" + "\n".join(f"- {result}" for result in results)
            )
            
            return RoutingResponse(
                result=RoutingResult.SUCCESS,
                content=response_content,
                metadata={
                    "strategy": "multi_agent",
                    "capabilities": analysis.required_capabilities,
                    "tasks_created": len(task_ids),
                    "task_ids": task_ids
                },
                processing_time=0.0,
                agents_used=available_agents
            )
            
        except Exception as e:
            self.logger.error(f"Multi-agent routing failed: {e}")
            return RoutingResponse(
                result=RoutingResult.FAILURE,
                content=f"Multi-agent coordination failed: {str(e)}",
                metadata={"error": str(e)},
                processing_time=0.0,
                agents_used=[]
            )
    
    async def _route_swarms(self, query_text: str, session_id: str,
                           analysis: QueryAnalysis) -> RoutingResponse:
        """Route to swarms coordination"""
        try:
            self.logger.info(f"Routing to swarms coordination for session {session_id}")
            
            # Configure swarms for this query
            swarms_config = {
                "max_agents": analysis.estimated_agents_needed,
                "coordination_pattern": self._select_coordination_pattern(analysis),
                "timeout": analysis.processing_time_estimate
            }
            
            config_result = await self.swarms_integration.configure_swarms(swarms_config)
            if not config_result:
                raise Exception("Failed to configure swarms")
            
            # Create swarm with required agents
            agent_ids = [f"swarm_agent_{i}_{session_id}" for i in range(analysis.estimated_agents_needed)]
            swarm_id = f"swarm_{session_id}_{int(time.time())}"
            
            coordination_pattern = self._select_coordination_pattern(analysis)
            swarm_result = await self.swarms_integration.create_swarm(
                swarm_id=swarm_id,
                agents=agent_ids,
                coordination_pattern=coordination_pattern
            )
            
            if not swarm_result.success:
                raise Exception(f"Failed to create swarm: {swarm_result.metadata.get('error', 'Unknown error')}")
            
            # Coordinate the task
            task_data = {
                "type": "swarms_coordination",
                "query": query_text,
                "capabilities": analysis.required_capabilities,
                "complexity": analysis.complexity.value
            }
            
            coordination_result = await self.swarms_integration.coordinate_task(
                swarm_id=swarm_id,
                task_data=task_data
            )
            
            if not coordination_result.success:
                raise Exception(f"Swarms coordination failed: {coordination_result.metadata.get('error', 'Unknown error')}")
            
            # Generate response
            response_content = (
                f"Swarms coordination complete:\n"
                f"Swarm ID: {swarm_id}\n"
                f"Coordination Pattern: {coordination_pattern.value}\n"
                f"Agents: {len(agent_ids)}\n"
                f"Capabilities: {', '.join(analysis.required_capabilities)}\n"
                f"Strategy: {coordination_result.strategy}\n"
                f"Agent Results: {len(coordination_result.agent_results)} completed"
            )
            
            # Cleanup swarm
            await self.swarms_integration.cleanup_swarm(swarm_id)
            
            return RoutingResponse(
                result=RoutingResult.SUCCESS,
                content=response_content,
                metadata={
                    "strategy": "swarms_coordination",
                    "swarm_id": swarm_id,
                    "coordination_pattern": coordination_pattern.value,
                    "agent_results": len(coordination_result.agent_results)
                },
                processing_time=0.0,
                agents_used=agent_ids,
                coordination_pattern=coordination_pattern.value
            )
            
        except Exception as e:
            self.logger.error(f"Swarms routing failed: {e}")
            return RoutingResponse(
                result=RoutingResult.FAILURE,
                content=f"Swarms coordination failed: {str(e)}",
                metadata={"error": str(e)},
                processing_time=0.0,
                agents_used=[]
            )
    
    def _select_coordination_pattern(self, analysis: QueryAnalysis) -> SwarmsCoordinationPattern:
        """Select appropriate coordination pattern based on analysis"""
        
        # Map complexity to coordination patterns
        if analysis.complexity.value == "expert":
            if len(analysis.required_capabilities) > 3:
                return SwarmsCoordinationPattern.HIERARCHICAL_SWARM
            else:
                return SwarmsCoordinationPattern.PARALLEL_WORKFLOW
        
        elif analysis.complexity.value == "complex":
            if "coordination" in analysis.required_capabilities:
                return SwarmsCoordinationPattern.AGENT_REARRANGE
            else:
                return SwarmsCoordinationPattern.CONCURRENT_WORKFLOW
        
        else:
            return SwarmsCoordinationPattern.SEQUENTIAL_WORKFLOW
    
    def get_routing_statistics(self) -> Dict[str, Any]:
        """Get routing statistics"""
        return self.routing_stats.copy()
