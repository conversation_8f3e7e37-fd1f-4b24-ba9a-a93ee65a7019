"""TaskCoordinator - Multi-Agent Task Management

Production-grade task coordination and distribution system.
Manages task lifecycle, assignment, and execution tracking.
"""

import asyncio
import logging
import uuid
from typing import Dict, List, Any, Optional, Callable, Set
from datetime import datetime, timezone, timedelta
from enum import Enum
from dataclasses import dataclass, asdict
import heapq


class TaskStatus(Enum):
    """Task status enumeration"""
    PENDING = "pending"
    ASSIGNED = "assigned"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class Task:
    """Task information structure"""
    id: str
    type: str
    data: Dict[str, Any]
    required_capabilities: List[str]
    priority: int
    status: str
    created_at: str
    assigned_to: Optional[str] = None
    assigned_at: Optional[str] = None
    completed_at: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    timeout_seconds: Optional[int] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)


class TaskCoordinator:
    """
    Coordinates task distribution and execution across multiple agents.
    
    Provides:
    - Task queue management with priority support
    - Intelligent agent selection and assignment
    - Task execution tracking and monitoring
    - Retry logic and failure handling
    - Performance optimization
    """
    
    def __init__(self, 
                 agent_registry_or_registry,
                 message_protocol=None,
                 default_timeout: int = 300,
                 max_concurrent_tasks: int = 1000):
        """
        Initialize TaskCoordinator.
        
        Args:
            agent_registry_or_registry: AgentRegistry instance (or just registry for backwards compatibility)
            message_protocol: MessageProtocol instance for communication (optional, will create if not provided)
            default_timeout: Default task timeout in seconds
            max_concurrent_tasks: Maximum concurrent tasks
        """
        # Handle backwards compatibility for tests
        if message_protocol is None:
            from .message_protocol import MessageProtocol
            message_protocol = MessageProtocol()
        
        self.agent_registry = agent_registry_or_registry
        self.message_protocol = message_protocol
        self.default_timeout = default_timeout
        self.max_concurrent_tasks = max_concurrent_tasks
        
        # Task storage
        self.tasks: Dict[str, Task] = {}
        self.task_queue: List[tuple] = []  # Priority queue: (priority, timestamp, task_id)
        self.active_tasks: Dict[str, Task] = {}  # Currently executing tasks
        
        # Agent assignment tracking
        self.agent_tasks: Dict[str, Set[str]] = {}  # agent_id -> set of task_ids
        
        # Performance metrics
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "average_completion_time": 0.0,
            "average_assignment_time": 0.0
        }
        
        # Async safety
        self._lock = asyncio.Lock()
        
        # Event handlers
        self.task_completion_handlers: List[Callable] = []
        self.task_failure_handlers: List[Callable] = []
        
        # Background processing
        self._processing_task: Optional[asyncio.Task] = None
        self._timeout_task: Optional[asyncio.Task] = None
        self._running = False
        
        # Logging
        self.logger = logging.getLogger(__name__)
    
    async def start(self) -> None:
        """Start the task coordinator"""
        self._running = True
        # Optional step executor for high-level coordination
        if not hasattr(self, "_step_executor"):
            self._step_executor = None  # type: ignore[attr-defined]
        self._processing_task = asyncio.create_task(self._process_task_queue())
        self._timeout_task = asyncio.create_task(self._monitor_timeouts())
        self.logger.info("TaskCoordinator started")

    def set_executor(self, executor):
        """Set a coroutine function that executes a single step.
        The executor must be an async callable receiving a payload dict
        with at least {"role": str, "text": str} and returning a string result.
        """
        self._step_executor = executor  # type: ignore[attr-defined]

    async def coordinate_task(
        self,
        task_type: str,
        query: str,
        session_id: str,
        required_capabilities: List[str] | None = None,
        pattern: str | None = None,
        steps: List[str] | None = None,
        step_timeout_s: float = 30.0,
    ) -> Dict[str, Any]:
        """High-level coordination that executes a sequence of role steps.
        Falls back to simple stubs if no step executor is configured.
        """
        roles = steps or ["planner", "researcher", "coder", "reviewer"]
        results: List[Dict[str, Any]] = []
        previous_output = query

        for role in roles:
            payload = {"role": role, "text": previous_output, "session_id": session_id}
            start_t = asyncio.get_event_loop().time()
            status = "completed"
            try:
                if getattr(self, "_step_executor", None):
                    step_result = await asyncio.wait_for(self._step_executor(payload), timeout=step_timeout_s)  # type: ignore[misc]
                else:
                    # Minimal fallback: echo role-tagged result
                    step_result = f"[{role}] {previous_output}"
            except asyncio.TimeoutError:
                status = "timeout"
                step_result = f"[{role}] error: step timed out after {step_timeout_s}s"
            except Exception as e:
                status = "error"
                step_result = f"[{role}] error: {e}"
            duration_ms = (asyncio.get_event_loop().time() - start_t) * 1000.0
            results.append({"role": role, "output": step_result, "status": status, "duration_ms": duration_ms})
            previous_output = step_result

        return {
            "status": "completed",
            "message": results[-1]["output"] if results else "",
            "steps": results,
            "pattern": pattern or "sequential",
        }

    async def coordinate_task_concurrent(
        self,
        query: str,
        session_id: str,
        researcher_count: int = 2,
    ) -> Dict[str, Any]:
        """Concurrent pattern: planner → N researchers (parallel) → coder → reviewer.
        Uses the configured step executor if available, otherwise falls back to deterministic transforms.
        """
        results: List[Dict[str, Any]] = []

        # 1) Planner step (sequential)
        planner_input = query
        planner_payload = {"role": "planner", "text": planner_input, "session_id": session_id}
        try:
            if getattr(self, "_step_executor", None):
                planner_out = await self._step_executor(planner_payload)  # type: ignore[misc]
            else:
                planner_out = f"[planner] {planner_input}"
        except Exception as e:
            planner_out = f"[planner] error: {e}"
        results.append({"role": "planner", "output": planner_out})

        # 2) Researchers in parallel
        async def run_researcher(i: int, text: str) -> Dict[str, Any]:
            role = f"researcher-{i+1}"
            payload = {"role": role, "text": text, "session_id": session_id}
            try:
                # small stagger to reduce LLM burst rate
                await asyncio.sleep(0.2 * i)
                if getattr(self, "_step_executor", None):
                    out = await self._step_executor(payload)  # type: ignore[misc]
                else:
                    out = f"[{role}] {text}"
            except Exception as e:
                out = f"[{role}] error: {e}"
            return {"role": role, "output": out}

        researcher_tasks = [
            run_researcher(i, planner_out) for i in range(max(1, researcher_count))
        ]
        researcher_results = await asyncio.gather(*researcher_tasks)
        results.extend(researcher_results)

        # Aggregate researchers output as input to coder
        agg_text = "\n\n".join([r["output"] for r in researcher_results])

        # 3) Coder step (sequential)
        coder_payload = {"role": "coder", "text": agg_text, "session_id": session_id}
        try:
            if getattr(self, "_step_executor", None):
                coder_out = await self._step_executor(coder_payload)  # type: ignore[misc]
            else:
                coder_out = f"[coder] {agg_text}"
        except Exception as e:
            coder_out = f"[coder] error: {e}"
        results.append({"role": "coder", "output": coder_out})

        # 4) Reviewer step (sequential)
        reviewer_payload = {"role": "reviewer", "text": coder_out, "session_id": session_id}
        try:
            if getattr(self, "_step_executor", None):
                reviewer_out = await self._step_executor(reviewer_payload)  # type: ignore[misc]
            else:
                reviewer_out = f"[reviewer] {coder_out}"
        except Exception as e:
            reviewer_out = f"[reviewer] error: {e}"
        results.append({"role": "reviewer", "output": reviewer_out})

        return {
            "status": "completed",
            "message": reviewer_out,
            "steps": results,
            "pattern": "concurrent",
        }

    async def coordinate_task_hierarchical(
        self,
        query: str,
        session_id: str,
        max_subtasks: int = 3,
        step_timeout_s: float = 30.0,
    ) -> Dict[str, Any]:
        """Hierarchical pattern: planner decomposes → parallel workers → reviewer aggregates.
        
        Args:
            query: User query to process hierarchically
            session_id: Session identifier  
            max_subtasks: Maximum number of subtasks to create
            step_timeout_s: Timeout per step in seconds
            
        Returns:
            Coordination result with hierarchical structure
        """
        results: List[Dict[str, Any]] = []
        
        # 1) Planner step: decompose query into subtasks
        planner_payload = {"role": "planner", "text": query, "session_id": session_id}
        start_t = asyncio.get_event_loop().time()
        status = "completed"
        try:
            if getattr(self, "_step_executor", None):
                planner_result = await asyncio.wait_for(
                    self._step_executor(planner_payload), timeout=step_timeout_s
                )
            else:
                planner_result = f"[planner] Decompose: {query}"
        except asyncio.TimeoutError:
            status = "timeout"
            planner_result = f"[planner] error: step timed out after {step_timeout_s}s"
        except Exception as e:
            status = "error"
            planner_result = f"[planner] error: {e}"
        
        duration_ms = (asyncio.get_event_loop().time() - start_t) * 1000.0
        planner_res = {"role": "planner", "output": planner_result, "status": status, "duration_ms": duration_ms}
        results.append(planner_res)
        
        # 2) Parse subtasks from planner output (look for bullets, numbers, or fallback)
        subtasks = []
        if status == "completed":
            lines = planner_result.split('\n')
            for line in lines:
                line = line.strip()
                # Match bullets (•, -, *) or numbered lists (1., 1), 2.)
                if line and (line.startswith(('•', '-', '*')) or 
                           (len(line) > 2 and line[0].isdigit() and line[1:3] in ['. ', ') ', '.)'])):
                    # Extract the task text after the bullet/number
                    if line.startswith(('•', '-', '*')):
                        task_text = line[1:].strip()
                    else:
                        # Skip the number part
                        task_text = line[line.find(' '):].strip()
                    if task_text:
                        subtasks.append(task_text)
            
            # Fallback: if no bullets found, split planner output into sentences
            if not subtasks:
                sentences = [s.strip() for s in planner_result.split('.') if s.strip()]
                subtasks = sentences[:max_subtasks]
                
            # Limit to max_subtasks
            subtasks = subtasks[:max_subtasks]
        
        if not subtasks:
            subtasks = ["Process query", "Generate response", "Review output"]
        
        # 3) Execute subtasks in parallel via workers (researcher → coder mini-pipeline)
        async def execute_worker(subtask: str, worker_id: int) -> Dict[str, Any]:
            worker_results = []
            current_output = subtask
            
            # Mini-pipeline: researcher → coder
            for role in ["researcher", "coder"]:
                worker_payload = {
                    "role": f"{role}-{worker_id}", 
                    "text": current_output, 
                    "session_id": session_id
                }
                start_t = asyncio.get_event_loop().time()
                status = "completed"
                try:
                    if getattr(self, "_step_executor", None):
                        step_result = await asyncio.wait_for(
                            self._step_executor(worker_payload), timeout=step_timeout_s
                        )
                    else:
                        step_result = f"[{role}-{worker_id}] {current_output}"
                except asyncio.TimeoutError:
                    status = "timeout"
                    step_result = f"[{role}-{worker_id}] error: step timed out after {step_timeout_s}s"
                except Exception as e:
                    status = "error"
                    step_result = f"[{role}-{worker_id}] error: {e}"
                
                duration_ms = (asyncio.get_event_loop().time() - start_t) * 1000.0
                worker_results.append({
                    "role": f"{role}-{worker_id}", 
                    "output": step_result, 
                    "status": status, 
                    "duration_ms": duration_ms
                })
                current_output = step_result
            
            return {
                "worker_id": worker_id,
                "subtask": subtask,
                "steps": worker_results,
                "final_output": current_output,
                "status": "completed" if all(s["status"] == "completed" for s in worker_results) else "partial"
            }
        
        # Execute all workers in parallel
        worker_tasks = [execute_worker(subtask, i+1) for i, subtask in enumerate(subtasks)]
        worker_outputs = await asyncio.gather(*worker_tasks, return_exceptions=True)
        
        # Process worker results
        worker_results = []
        aggregated_outputs = []
        for i, worker_output in enumerate(worker_outputs):
            if isinstance(worker_output, Exception):
                worker_result = {
                    "worker_id": i+1,
                    "subtask": subtasks[i] if i < len(subtasks) else "unknown",
                    "steps": [],
                    "final_output": f"Worker {i+1} failed: {worker_output}",
                    "status": "error"
                }
            else:
                worker_result = worker_output
            
            worker_results.append(worker_result)
            aggregated_outputs.append(worker_result["final_output"])
        
        # Add worker results to main results
        results.extend(worker_results)
        
        # 4) Reviewer step: aggregate all worker outputs
        aggregated_text = "\n\n".join([f"Subtask {i+1}: {output}" for i, output in enumerate(aggregated_outputs)])
        reviewer_payload = {"role": "reviewer", "text": aggregated_text, "session_id": session_id}
        start_t = asyncio.get_event_loop().time()
        status = "completed"
        try:
            if getattr(self, "_step_executor", None):
                reviewer_result = await asyncio.wait_for(
                    self._step_executor(reviewer_payload), timeout=step_timeout_s
                )
            else:
                reviewer_result = f"[reviewer] Aggregated {len(subtasks)} subtasks successfully"
        except asyncio.TimeoutError:
            status = "timeout"
            reviewer_result = f"[reviewer] error: step timed out after {step_timeout_s}s"
        except Exception as e:
            status = "error"
            reviewer_result = f"[reviewer] error: {e}"
        
        duration_ms = (asyncio.get_event_loop().time() - start_t) * 1000.0
        reviewer_res = {"role": "reviewer", "output": reviewer_result, "status": status, "duration_ms": duration_ms}
        results.append(reviewer_res)
        
        return {
            "status": "completed",
            "message": reviewer_result,
            "steps": results,
            "pattern": "hierarchical",
            "subtasks": subtasks,
        }
    
    async def stop(self) -> None:
        """Stop the task coordinator"""
        self._running = False
        
        # Cancel background tasks
        for task in [self._processing_task, self._timeout_task]:
            if task:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        self.logger.info("TaskCoordinator stopped")
    
    async def submit_task(self,
                         task_type: str,
                         task_data: Dict[str, Any],
                         required_capabilities: List[str],
                         priority: int = TaskPriority.NORMAL.value,
                         timeout_seconds: Optional[int] = None,
                         max_retries: int = 3) -> str:
        """
        Submit a new task for execution.
        
        Args:
            task_type: Type of task to execute
            task_data: Task-specific data
            required_capabilities: Required agent capabilities
            priority: Task priority level
            timeout_seconds: Task timeout (uses default if None)
            max_retries: Maximum retry attempts
            
        Returns:
            Task ID
        """
        task_id = str(uuid.uuid4())
        
        try:
            async with self._lock:
                # Check concurrent task limit
                if len(self.active_tasks) >= self.max_concurrent_tasks:
                    raise ValueError("Maximum concurrent tasks reached")
                
                # Create task
                task = Task(
                    id=task_id,
                    type=task_type,
                    data=task_data,
                    required_capabilities=required_capabilities,
                    priority=priority,
                    status=TaskStatus.PENDING.value,
                    created_at=datetime.now(timezone.utc).isoformat(),
                    timeout_seconds=timeout_seconds or self.default_timeout,
                    max_retries=max_retries
                )
                
                # Store task
                self.tasks[task_id] = task
                
                # Add to priority queue (negative priority for max-heap behavior)
                heapq.heappush(
                    self.task_queue,
                    (-priority, datetime.now().timestamp(), task_id)
                )
                
                # Update statistics
                self.stats["total_tasks"] += 1
                
                self.logger.info(f"Task {task_id} submitted with priority {priority}")
                return task_id
                
        except Exception as e:
            self.logger.error(f"Failed to submit task: {e}")
            raise
    
    async def _process_task_queue(self) -> None:
        """Background task to process the task queue"""
        while self._running:
            try:
                await self._assign_next_task()
                await asyncio.sleep(0.01)  # Small delay to prevent busy waiting
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error processing task queue: {e}")
                await asyncio.sleep(1)  # Wait before retrying
    
    async def _assign_next_task(self) -> bool:
        """
        Assign the next task from the queue to an available agent.
        
        Returns:
            True if task was assigned, False otherwise
        """
        assignment_start = datetime.now()
        
        try:
            async with self._lock:
                if not self.task_queue:
                    return False
                
                # Get highest priority task
                _, _, task_id = heapq.heappop(self.task_queue)
                
                if task_id not in self.tasks:
                    return False
                
                task = self.tasks[task_id]
                
                # Find suitable agents
                suitable_agents = self.agent_registry.find_agents_by_capabilities(
                    task.required_capabilities
                )
                
                if not suitable_agents:
                    # No suitable agents available, put task back in queue
                    heapq.heappush(
                        self.task_queue,
                        (-task.priority, datetime.now().timestamp(), task_id)
                    )
                    return False
                
                # Select best agent (least busy)
                selected_agent = self._select_best_agent(suitable_agents)
                
                if not selected_agent:
                    # No available agents, put task back in queue
                    heapq.heappush(
                        self.task_queue,
                        (-task.priority, datetime.now().timestamp(), task_id)
                    )
                    return False
                
                # Assign task to agent
                await self._assign_task_to_agent(task, selected_agent["agent_id"])
                
                # Update assignment time statistics
                assignment_time = (datetime.now() - assignment_start).total_seconds() * 1000
                self._update_average_assignment_time(assignment_time)
                
                return True
                
        except Exception as e:
            self.logger.error(f"Error assigning task: {e}")
            return False
    
    def _select_best_agent(self, suitable_agents: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Select the best agent for task assignment.
        
        Args:
            suitable_agents: List of suitable agent dictionaries
            
        Returns:
            Selected agent dictionary or None
        """
        # Simple selection: choose agent with fewest assigned tasks
        best_agent = None
        min_tasks = float('inf')
        
        for agent in suitable_agents:
            agent_id = agent["agent_id"]
            task_count = len(self.agent_tasks.get(agent_id, set()))
            
            if task_count < min_tasks:
                min_tasks = task_count
                best_agent = agent
        
        return best_agent
    
    async def _assign_task_to_agent(self, task: Task, agent_id: str) -> None:
        """
        Assign a specific task to a specific agent.
        
        Args:
            task: Task to assign
            agent_id: Target agent ID
        """
        try:
            # Update task status
            task.status = TaskStatus.ASSIGNED.value
            task.assigned_to = agent_id
            task.assigned_at = datetime.now(timezone.utc).isoformat()
            
            # Move to active tasks
            self.active_tasks[task.id] = task
            
            # Track agent assignment
            if agent_id not in self.agent_tasks:
                self.agent_tasks[agent_id] = set()
            self.agent_tasks[agent_id].add(task.id)
            
            # Create assignment message
            message = self.message_protocol.create_task_assignment(
                from_agent="coordinator",
                to_agent=agent_id,
                task_data={
                    "id": task.id,
                    "type": task.type,
                    "data": task.data,
                    "timeout": task.timeout_seconds
                },
                priority=task.priority
            )
            
            # In full implementation, would send via WebSocket
            # For now, simulate assignment
            self.logger.info(f"Task {task.id} assigned to agent {agent_id}")
            
            # Simulate task execution start
            task.status = TaskStatus.IN_PROGRESS.value
            
        except Exception as e:
            self.logger.error(f"Failed to assign task {task.id} to agent {agent_id}: {e}")
            # Put task back in queue
        async with self._lock:
                heapq.heappush(
                    self.task_queue,
                    (-task.priority, datetime.now().timestamp(), task.id)
                )
    
    async def complete_task(self, 
                          task_id: str, 
                          result: Dict[str, Any],
                          agent_id: Optional[str] = None) -> bool:
        """
        Mark a task as completed.
        
        Args:
            task_id: ID of completed task
            result: Task execution result
            agent_id: ID of agent that completed the task
            
        Returns:
            True if successful, False otherwise
        """
        completion_start = datetime.now()
        
        try:
            async with self._lock:
                if task_id not in self.active_tasks:
                    self.logger.warning(f"Task {task_id} not found in active tasks")
                    return False
                
                task = self.active_tasks[task_id]
                
                # Update task
                task.status = TaskStatus.COMPLETED.value
                task.completed_at = datetime.now(timezone.utc).isoformat()
                task.result = result
                
                # Remove from active tasks
                del self.active_tasks[task_id]
                
                # Remove from agent assignment
                if task.assigned_to and task.assigned_to in self.agent_tasks:
                    self.agent_tasks[task.assigned_to].discard(task_id)
                
                # Update statistics
                self.stats["completed_tasks"] += 1
                
                # Calculate completion time
                if task.created_at:
                    created_time = datetime.fromisoformat(task.created_at.replace('Z', '+00:00'))
                    completion_time = (completion_start - created_time).total_seconds() * 1000
                    self._update_average_completion_time(completion_time)
                
                self.logger.info(f"Task {task_id} completed successfully")
                
                # Notify completion handlers
                for handler in self.task_completion_handlers:
                    try:
                        await handler(task)
                    except Exception as e:
                        self.logger.error(f"Error in completion handler: {e}")
                
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to complete task {task_id}: {e}")
            return False
    
    async def fail_task(self, 
                       task_id: str, 
                       error: str,
                       retry: bool = True) -> bool:
        """
        Mark a task as failed and optionally retry.
        
        Args:
            task_id: ID of failed task
            error: Error description
            retry: Whether to retry the task
            
        Returns:
            True if successful, False otherwise
        """
        try:
        async with self._lock:
                if task_id not in self.active_tasks:
                    return False
                
                task = self.active_tasks[task_id]
                
                # Check if we should retry
                if retry and task.retry_count < task.max_retries:
                    task.retry_count += 1
                    task.status = TaskStatus.PENDING.value
                    task.assigned_to = None
                    task.assigned_at = None
                    
                    # Put back in queue with higher priority
                    heapq.heappush(
                        self.task_queue,
                        (-(task.priority + 1), datetime.now().timestamp(), task_id)
                    )
                    
                    self.logger.info(f"Task {task_id} queued for retry {task.retry_count}/{task.max_retries}")
                    return True
                
                # Mark as permanently failed
                task.status = TaskStatus.FAILED.value
                task.result = {"error": error}
                
                # Remove from active tasks
                del self.active_tasks[task_id]
                
                # Remove from agent assignment
                if task.assigned_to and task.assigned_to in self.agent_tasks:
                    self.agent_tasks[task.assigned_to].discard(task_id)
                
                # Update statistics
                self.stats["failed_tasks"] += 1
                
                self.logger.warning(f"Task {task_id} failed permanently: {error}")
                
                # Notify failure handlers
                for handler in self.task_failure_handlers:
                    try:
                        await handler(task, error)
                    except Exception as e:
                        self.logger.error(f"Error in failure handler: {e}")
                
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to fail task {task_id}: {e}")
            return False
    
    async def _monitor_timeouts(self) -> None:
        """Monitor and handle task timeouts"""
        while self._running:
            try:
                await self._check_task_timeouts()
                await asyncio.sleep(10)  # Check every 10 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error monitoring timeouts: {e}")
    
    async def _check_task_timeouts(self) -> None:
        """Check for timed out tasks"""
        now = datetime.now(timezone.utc)
        timed_out_tasks = []
        
        async with self._lock:
            for task_id, task in self.active_tasks.items():
                if task.timeout_seconds and task.assigned_at:
                    assigned_time = datetime.fromisoformat(task.assigned_at.replace('Z', '+00:00'))
                    timeout_threshold = assigned_time + timedelta(seconds=task.timeout_seconds)
                    
                    if now > timeout_threshold:
                        timed_out_tasks.append(task_id)
        
        # Handle timeouts
        for task_id in timed_out_tasks:
            await self.fail_task(task_id, "Task timeout", retry=True)
    
    def get_task_status(self, task_id: str) -> Optional[str]:
        """Get status of a specific task"""
        async with self._lock:
            if task_id in self.tasks:
                return self.tasks[task_id].status
            return None
    
    def get_task_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get complete information about a task"""
        async with self._lock:
            if task_id in self.tasks:
                return self.tasks[task_id].to_dict()
            return None
    
    def list_active_tasks(self) -> List[Dict[str, Any]]:
        """Get list of all active tasks"""
        async with self._lock:
            return [task.to_dict() for task in self.active_tasks.values()]
    
    def list_pending_tasks(self) -> List[str]:
        """Get list of pending task IDs"""
        async with self._lock:
            return [task_id for _, _, task_id in self.task_queue]
    
    def add_completion_handler(self, handler: Callable) -> None:
        """Add task completion handler"""
        self.task_completion_handlers.append(handler)
    
    def add_failure_handler(self, handler: Callable) -> None:
        """Add task failure handler"""
        self.task_failure_handlers.append(handler)
    
    def _update_average_completion_time(self, new_time: float) -> None:
        """Update average completion time statistics"""
        current_avg = self.stats["average_completion_time"]
        completed_tasks = self.stats["completed_tasks"]
        
        if completed_tasks == 1:
            self.stats["average_completion_time"] = new_time
        else:
            self.stats["average_completion_time"] = (
                (current_avg * (completed_tasks - 1) + new_time) / completed_tasks
            )
    
    def _update_average_assignment_time(self, new_time: float) -> None:
        """Update average assignment time statistics"""
        current_avg = self.stats["average_assignment_time"]
        total_tasks = self.stats["total_tasks"]
        
        if total_tasks == 1:
            self.stats["average_assignment_time"] = new_time
        else:
            self.stats["average_assignment_time"] = (
                (current_avg * (total_tasks - 1) + new_time) / total_tasks
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get task coordinator statistics"""
        async with self._lock:
            return {
                "stats": self.stats.copy(),
                "active_tasks": len(self.active_tasks),
                "pending_tasks": len(self.task_queue),
                "total_tasks": len(self.tasks),
                "agent_utilization": {
                    agent_id: len(task_ids)
                    for agent_id, task_ids in self.agent_tasks.items()
                }
            }
