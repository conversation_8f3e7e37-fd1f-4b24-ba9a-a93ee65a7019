#!/usr/bin/env python3
"""
Quick fix for infinite loop issue in ii-agent
This script will create a simple safety configuration
"""

import os

def fix_infinite_loop():
    """Add safety configurations to prevent infinite loops."""
    
    print("🔧 Applying infinite loop fixes...")
    
    # Create a safety configuration file
    safety_config = """
# Safety Configuration for ii-agent
# This prevents infinite loops and recursive thinking

# Limit thinking tokens
MAX_THINKING_TOKENS=1000

# Limit response iterations
MAX_RESPONSE_ITERATIONS=3

# Enable safety checks
ENABLE_SAFETY_CHECKS=true

# Prevent recursive responses
PREVENT_RECURSIVE_RESPONSES=true
"""
    
    safety_file = "c:/Users/<USER>/OneDrive/Desktop/ph/ii-agent/.env.safety"
    with open(safety_file, "w") as f:
        f.write(safety_config)
    
    print(f"✅ Created safety configuration: {safety_file}")
    
    # Also update the main .env file to include safety settings
    env_file = "c:/Users/<USER>/OneDrive/Desktop/ph/ii-agent/.env"
    
    with open(env_file, "a") as f:
        f.write("\n# Safety Settings to Prevent Infinite Loops\n")
        f.write("MAX_THINKING_TOKENS=1000\n")
        f.write("MAX_RESPONSE_ITERATIONS=3\n")
        f.write("ENABLE_SAFETY_CHECKS=true\n")
        f.write("PREVENT_RECURSIVE_RESPONSES=true\n")
    
    print("✅ Added safety settings to .env file")
    
    return True

if __name__ == "__main__":
    success = fix_infinite_loop()
    if success:
        print("\n🎯 Infinite loop fixes applied!")
        print("🚀 Ready to restart containers safely")
    else:
        print("\n❌ Failed to apply fixes")
