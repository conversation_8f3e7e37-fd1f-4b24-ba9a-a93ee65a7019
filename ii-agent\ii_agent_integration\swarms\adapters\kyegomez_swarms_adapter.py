"""
Kyegomez Swarms External Backend Adapter

This adapter bridges our internal SwarmsBackendAdapter contract to the
kyegomez/swarms library when enabled via a feature flag.

Feature flag:
  - SWARMS_BACKEND=kyegomez  -> enable this adapter

Behavior:
  - If the external "swarms" package is unavailable, adapter initialization
    raises an ImportError which is caught by the integration layer, and the
    system cleanly falls back to local implementations.
  - If present, selected patterns may be executed via the external backend.
    For now, we conservatively handle only a small subset and otherwise
    return None to allow local handling.
"""

from __future__ import annotations

from typing import Any, Optional

from ..backend_adapter import SwarmsBackendAdapter
from ..swarms_integration import SwarmsCoordinationPattern, CoordinationResult


class KyegomezSwarmsAdapter(SwarmsBackendAdapter):
    """Adapter for kyegomez/swarms.

    Notes:
      - We import on init so that missing dependency is surfaced early and
        integration can record a backend failure and fall back locally.
      - Minimal pattern coverage is provided initially; expand as needed.
    """

    def __init__(self) -> None:
        try:
            # Import the external library. Module name on PyPI is expected to be "swarms".
            import swarms as _swarms  # type: ignore
        except Exception as e:  # ImportError or others
            raise ImportError(
                "External backend 'kyegomez/swarms' is not installed. Install 'swarms' to enable the adapter."
            ) from e

        self._swarms = _swarms

    async def execute(self, pattern: SwarmsCoordinationPattern, task: Any, agents: list) -> Optional[CoordinationResult]:
        # Example: handle a simple round-robin or concurrent pattern via external lib if available.
        # Without making assumptions about the external API surface, only route a known-safe no-op.
        # Return None for unhandled patterns to allow local execution.

        # As a conservative default until deeper integration is implemented, do not intercept by default.
        if pattern not in (SwarmsCoordinationPattern.ROUND_ROBIN, SwarmsCoordinationPattern.CONCURRENT_WORKFLOW):
            return None

        # Try to call into external library if it exposes an obvious orchestrator. If not, return None.
        # We avoid guessing API details; expand this when the external API contract is verified.
        try:
            # If the external lib exposes a simple Swarm/Coordinator run API, wire it here.
            # Example pseudocode (intentionally guarded):
            run_fn = getattr(self._swarms, "run_round_robin", None)
            if pattern == SwarmsCoordinationPattern.CONCURRENT_WORKFLOW:
                run_fn = run_fn or getattr(self._swarms, "run_concurrent", None)

            if callable(run_fn):
                ext_result = await run_fn(task=task, agents=agents)  # type: ignore[misc]
                # Expect ext_result to be a dict-like; normalize to CoordinationResult
                return CoordinationResult(
                    task_id=getattr(task, "task_id", "unknown"),
                    success=bool(ext_result.get("success", True)),
                    strategy=f"external:{pattern.value}",
                    agent_results=ext_result.get("agent_results", []),
                    metadata={"backend": "kyegomez"},
                )
        except Exception as e:
            # Surface errors to integration (it will record and fall back)
            raise

        # No supported external entrypoint detected
        return None
