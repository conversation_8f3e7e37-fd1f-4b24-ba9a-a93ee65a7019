#!/usr/bin/env python3
"""
Test GPU Acceleration
====================

Test script to verify GPU acceleration works and provides real performance improvements.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def test_gpu_manager():
    """Test the real GPU manager"""
    logger.info("🔧 Testing Real GPU Manager...")
    
    try:
        from ii_agent_integration.gpu.real_gpu_acceleration import RealGPUManager, GPUConfig, is_gpu_available
        
        # Check basic GPU availability
        gpu_available = is_gpu_available()
        logger.info(f"   CUDA Available: {gpu_available}")
        
        # Initialize GPU manager
        config = GPUConfig(device_id=0, enable_mixed_precision=True)
        gpu_manager = RealGPUManager(config)
        
        logger.info(f"✅ GPU Manager Status:")
        logger.info(f"   GPU Available: {gpu_manager.is_gpu_available()}")
        logger.info(f"   Device: {gpu_manager.get_device()}")
        
        if gpu_manager.is_gpu_available():
            logger.info(f"   GPU Info: {gpu_manager.gpu_info.name}")
            logger.info(f"   Memory: {gpu_manager.gpu_info.total_memory_gb:.1f}GB")
            logger.info(f"   Compute Capability: {gpu_manager.gpu_info.compute_capability}")
        
        # Benchmark GPU performance
        benchmark_result = gpu_manager.benchmark_gpu_performance(matrix_size=1024, iterations=5)
        
        if benchmark_result["speedup"] > 0:
            logger.info(f"🚀 GPU Benchmark Results:")
            logger.info(f"   Speedup: {benchmark_result['speedup']:.2f}x")
            logger.info(f"   GPU Time: {benchmark_result['avg_gpu_time_ms']:.2f}ms")
            logger.info(f"   CPU Time: {benchmark_result['avg_cpu_time_ms']:.2f}ms")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ GPU manager test failed: {e}")
        return False


async def test_gpu_neural_networks():
    """Test GPU-accelerated neural networks"""
    logger.info("🧠 Testing GPU-Accelerated Neural Networks...")
    
    try:
        from ii_agent_integration.neural.gpu_neural_networks import GPUAcceleratedAgent, GPUNeuralConfig
        from ii_agent_integration.neural.real_neural_networks import AgentSpecialization
        
        # Create GPU-accelerated agent
        config = GPUNeuralConfig(
            input_size=512,
            hidden_sizes=[256, 128],
            output_size=32,
            enable_gpu=True,
            enable_mixed_precision=True,
            max_batch_size=32
        )
        
        agent = GPUAcceleratedAgent("test_gpu_agent", AgentSpecialization.RESEARCHER, config)
        
        # Test GPU analysis
        test_task = "Optimize machine learning model performance using GPU acceleration"
        result = await agent.analyze_task_gpu(test_task)
        
        logger.info(f"✅ GPU Neural Network Results:")
        logger.info(f"   Agent ID: {result['agent_id']}")
        logger.info(f"   GPU Accelerated: {result.get('gpu_accelerated', False)}")
        logger.info(f"   Device: {result.get('device', 'unknown')}")
        logger.info(f"   Execution Time: {result['execution_time_ms']:.2f}ms")
        logger.info(f"   Confidence: {result['confidence']:.3f}")
        logger.info(f"   Mixed Precision: {result['metadata'].get('mixed_precision', False)}")
        
        # Benchmark GPU vs CPU performance
        benchmark_result = agent.benchmark_gpu_vs_cpu(test_task, iterations=5)
        
        if benchmark_result.get("speedup", 0) > 0:
            logger.info(f"🚀 Agent GPU Speedup: {benchmark_result['speedup']:.2f}x")
            logger.info(f"   GPU Time: {benchmark_result['avg_gpu_time_ms']:.2f}ms")
            logger.info(f"   CPU Time: {benchmark_result['avg_cpu_time_ms']:.2f}ms")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ GPU neural networks test failed: {e}")
        return False


async def test_gpu_multi_agent_system():
    """Test GPU-accelerated multi-agent system"""
    logger.info("🚀 Testing GPU-Accelerated Multi-Agent System...")
    
    try:
        from ii_agent_integration.swarms.real_multi_agent_system import RealMultiAgentSystem, SwarmConfig
        
        # Create GPU-accelerated swarm
        config = SwarmConfig(
            max_agents=5,
            enable_gpu=True,
            gpu_device_id=0,
            enable_mixed_precision=True,
            parallel_inference=True
        )
        
        swarm = RealMultiAgentSystem(config)
        
        # Get GPU status
        gpu_status = swarm.get_gpu_status()
        logger.info(f"✅ GPU Swarm Status:")
        logger.info(f"   GPU Available: {gpu_status['gpu_available']}")
        logger.info(f"   Agents Using GPU: {gpu_status['agents_using_gpu']}/{gpu_status['total_agents']}")
        
        if gpu_status['gpu_available']:
            logger.info(f"   GPU Device: {gpu_status.get('current_device', 'unknown')}")
        
        # Test GPU-accelerated coordination
        test_task = "Design a high-performance distributed computing system with GPU acceleration"
        
        start_time = time.perf_counter()
        result = await swarm.coordinate_task(test_task, "mixture_of_agents")
        coordination_time = (time.perf_counter() - start_time) * 1000
        
        logger.info(f"✅ GPU Coordination Results:")
        logger.info(f"   Success: {result['success']}")
        logger.info(f"   Coordination Time: {coordination_time:.2f}ms")
        logger.info(f"   Participating Agents: {result['participating_agents']}")
        
        if result['success']:
            coordination_result = result['result']
            logger.info(f"   Consensus Score: {coordination_result.get('consensus_score', 0):.3f}")
        
        # Benchmark GPU performance across all agents
        if gpu_status['gpu_available']:
            benchmark_result = await swarm.benchmark_gpu_performance(test_task, iterations=3)
            
            if benchmark_result.get("overall_speedup", 0) > 0:
                logger.info(f"🚀 Swarm GPU Performance:")
                logger.info(f"   Overall Speedup: {benchmark_result['overall_speedup']:.2f}x")
                logger.info(f"   Range: {benchmark_result.get('min_speedup', 0):.2f}x - {benchmark_result.get('max_speedup', 0):.2f}x")
                logger.info(f"   Total GPU Time: {benchmark_result['total_gpu_time_ms']:.2f}ms")
                logger.info(f"   Total CPU Time: {benchmark_result['total_cpu_time_ms']:.2f}ms")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ GPU multi-agent system test failed: {e}")
        return False


async def test_performance_comparison():
    """Compare GPU vs CPU performance in real scenarios"""
    logger.info("⚡ Testing Performance Comparison...")
    
    try:
        from ii_agent_integration.swarms.real_multi_agent_system import RealMultiAgentSystem, SwarmConfig
        
        # Test both GPU and CPU configurations
        test_task = "Analyze and optimize database query performance for high-throughput applications"
        
        # GPU configuration
        gpu_config = SwarmConfig(enable_gpu=True, max_agents=3)
        gpu_swarm = RealMultiAgentSystem(gpu_config)
        
        # CPU configuration  
        cpu_config = SwarmConfig(enable_gpu=False, max_agents=3)
        cpu_swarm = RealMultiAgentSystem(cpu_config)
        
        # Test GPU performance
        gpu_start = time.perf_counter()
        gpu_result = await gpu_swarm.coordinate_task(test_task, "mixture_of_agents")
        gpu_time = (time.perf_counter() - gpu_start) * 1000
        
        # Test CPU performance
        cpu_start = time.perf_counter()
        cpu_result = await cpu_swarm.coordinate_task(test_task, "mixture_of_agents")
        cpu_time = (time.perf_counter() - cpu_start) * 1000
        
        # Calculate speedup
        speedup = cpu_time / gpu_time if gpu_time > 0 else 0
        
        logger.info(f"✅ Performance Comparison Results:")
        logger.info(f"   GPU Coordination Time: {gpu_time:.2f}ms")
        logger.info(f"   CPU Coordination Time: {cpu_time:.2f}ms")
        logger.info(f"   Overall Speedup: {speedup:.2f}x")
        
        logger.info(f"   GPU Success: {gpu_result['success']}")
        logger.info(f"   CPU Success: {cpu_result['success']}")
        
        # Verify both produce valid results
        if gpu_result['success'] and cpu_result['success']:
            logger.info("✅ Both GPU and CPU systems produced valid results")
            return True
        else:
            logger.warning("⚠️ One or both systems failed to produce valid results")
            return False
        
    except Exception as e:
        logger.error(f"❌ Performance comparison test failed: {e}")
        return False


async def main():
    """Run all GPU acceleration tests"""
    logger.info("🎯 Starting GPU Acceleration Tests")
    logger.info("=" * 60)
    
    tests = [
        ("GPU Manager", test_gpu_manager),
        ("GPU Neural Networks", test_gpu_neural_networks),
        ("GPU Multi-Agent System", test_gpu_multi_agent_system),
        ("Performance Comparison", test_performance_comparison)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running {test_name} Test...")
        try:
            success = await test_func()
            results[test_name] = success
            if success:
                logger.info(f"✅ {test_name} Test PASSED")
            else:
                logger.error(f"❌ {test_name} Test FAILED")
        except Exception as e:
            logger.error(f"💥 {test_name} Test CRASHED: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 GPU ACCELERATION TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"   {test_name}: {status}")
    
    logger.info(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL GPU TESTS PASSED! Real GPU acceleration is working!")
        return True
    else:
        logger.error(f"💔 {total - passed} tests failed. GPU acceleration needs fixes.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
