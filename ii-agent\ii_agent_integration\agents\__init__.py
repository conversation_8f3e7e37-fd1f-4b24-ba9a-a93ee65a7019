"""Agents Package

Multi-agent base classes and coordination infrastructure.
Built upon Phase 1 WebSocket infrastructure for production-ready multi-agent systems.
"""

from .base_agent import BaseAgent, AgentStatus
from .message_protocol import Message<PERSON>rotocol, MessageType, MessagePriority
from .agent_registry import Agent<PERSON><PERSON><PERSON><PERSON>, AgentInfo, RegistrationStatus
from .task_coordinator import TaskCoordinator, Task, TaskStatus, TaskPriority
from .agent_manager import Agent<PERSON>anager

__all__ = [
    # Core classes
    'BaseAgent',
    'MessageProtocol', 
    'AgentRegistry',
    'TaskCoordinator',
    'AgentManager',
    
    # Data classes
    'AgentInfo',
    'Task',
    
    # Enums
    'AgentStatus',
    'MessageType',
    'MessagePriority',
    'RegistrationStatus',
    'TaskStatus',
    'TaskPriority'
]
