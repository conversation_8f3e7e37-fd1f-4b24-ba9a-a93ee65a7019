# ii-agent Multi-Agent Integration

This directory contains the integration of the multi-agent system with the ii-agent WebSocket server.

## Integration Overview

The multi-agent chat session (`MultiAgentChatSession`) is designed as a drop-in replacement for the original `ChatSession` class. It maintains the same interface while adding multi-agent coordination capabilities.

## Features

- **Drop-in Replacement**: Compatible with the existing ii-agent WebSocket infrastructure
- **Multi-Agent Coordination**: Uses AgentRegistry, TaskCoordinator, and SwarmsFrameworkIntegration
- **Environment Toggle**: Can be enabled/disabled via environment variable
- **Fallback Support**: Gracefully falls back to single-agent mode if unavailable

## Usage

### Enable Multi-Agent Mode

Set the environment variable to enable multi-agent functionality:

```bash
export II_AGENT_MULTI_AGENT=true
```

Or on Windows:
```cmd
set II_AGENT_MULTI_AGENT=true
```

### Disable Multi-Agent Mode (Default)

Leave the environment variable unset or set it to false:

```bash
export II_AGENT_MULTI_AGENT=false
```

## Architecture

### Components

1. **MultiAgentChatSession**: Main session handler with multi-agent coordination
2. **AgentRegistry**: Manages available agents and their capabilities  
3. **TaskCoordinator**: Coordinates task execution across multiple agents
4. **SwarmsFrameworkIntegration**: Provides coordination patterns and swarms functionality

### Integration Points

- `ii-agent/src/ii_agent/server/websocket/manager.py`: WebSocket connection manager updated to conditionally import multi-agent session
- Environment-based switching ensures backward compatibility
- Interface compatibility maintained for seamless integration

## Testing

Run the integration test:
```bash
python test_ii_agent_integration.py
```

Run the full validation suite:
```bash
python real_implementation_validator.py
```

## Coordination Strategies

The system automatically determines coordination strategy based on query analysis:

- **Sequential**: Default for most queries
- **Concurrent**: For queries mentioning "parallel", "simultaneous", or "concurrent"  
- **Hierarchical**: For queries mentioning "hierarchy", "multi-step", or "orchestrate"

## Agent Capabilities

Default agents are registered with specific capabilities:

- **neural-analyzer**: code_analysis, pattern_recognition
- **neural-optimizer**: performance_optimization, refactoring
- **neural-validator**: code_validation, quality_assessment  
- **neural-synthesizer**: result_synthesis, report_generation

## Fallback Behavior

If multi-agent components are unavailable:
1. Falls back to original single-agent ChatSession
2. Logs appropriate warnings
3. Maintains full functionality without multi-agent features

## Logs

Multi-agent mode status is logged on startup:
- 🧠 Multi-agent mode enabled
- 🤖 Single-agent mode enabled  
- ⚠️ Multi-agent mode requested but not available
