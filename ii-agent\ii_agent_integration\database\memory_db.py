"""
In-Memory Database Implementation
Provides a working database layer without external SQLite dependencies.
"""

import asyncio
import json
import logging
import time
import uuid
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class Session:
    """Session data structure"""
    id: str
    workspace_dir: str
    created_at: str
    device_id: Optional[str] = None
    session_uuid: Optional[str] = None
    workspace_path: Optional[str] = None

class MemoryDatabase:
    """In-memory database implementation for development and testing"""
    
    def __init__(self):
        self.sessions: Dict[str, Session] = {}
        self.agents: Dict[str, Dict[str, Any]] = {}
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self.coordination_history: List[Dict[str, Any]] = []
        self._lock = asyncio.Lock()
    
    async def create_session(self, session_id: str, workspace_dir: str, device_id: str = None) -> Session:
        """Create a new session"""
        async with self._lock:
            session = Session(
                id=session_id,
                workspace_dir=workspace_dir,
                created_at=datetime.now().isoformat(),
                device_id=device_id,
                session_uuid=session_id,
                workspace_path=workspace_dir
            )
            self.sessions[session_id] = session
            logger.info(f"Created session {session_id}")
            return session
    
    async def get_session(self, session_id: str) -> Optional[Session]:
        """Get session by ID"""
        return self.sessions.get(session_id)
    
    async def register_agent(self, agent_id: str, agent_data: Dict[str, Any]) -> bool:
        """Register an agent"""
        async with self._lock:
            self.agents[agent_id] = {
                **agent_data,
                "registered_at": datetime.now().isoformat(),
                "status": "active"
            }
            logger.info(f"Registered agent {agent_id}")
            return True
    
    async def get_agent(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get agent by ID"""
        return self.agents.get(agent_id)
    
    async def list_agents(self) -> List[Dict[str, Any]]:
        """List all registered agents"""
        return list(self.agents.values())
    
    async def create_task(self, task_id: str, task_data: Dict[str, Any]) -> bool:
        """Create a new task"""
        async with self._lock:
            self.tasks[task_id] = {
                **task_data,
                "created_at": datetime.now().isoformat(),
                "status": "pending"
            }
            logger.info(f"Created task {task_id}")
            return True
    
    async def update_task(self, task_id: str, updates: Dict[str, Any]) -> bool:
        """Update task data"""
        async with self._lock:
            if task_id in self.tasks:
                self.tasks[task_id].update(updates)
                self.tasks[task_id]["updated_at"] = datetime.now().isoformat()
                return True
            return False
    
    async def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task by ID"""
        return self.tasks.get(task_id)
    
    async def record_coordination(self, coordination_data: Dict[str, Any]) -> bool:
        """Record coordination event"""
        async with self._lock:
            coordination_record = {
                **coordination_data,
                "recorded_at": datetime.now().isoformat(),
                "id": str(uuid.uuid4())
            }
            self.coordination_history.append(coordination_record)
            logger.info(f"Recorded coordination event")
            return True
    
    async def get_coordination_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get coordination history"""
        return self.coordination_history[-limit:]
    
    async def cleanup(self):
        """Cleanup database resources"""
        async with self._lock:
            # Clear old data (keep last 1000 records)
            if len(self.coordination_history) > 1000:
                self.coordination_history = self.coordination_history[-1000:]
            
            logger.info("Database cleanup completed")

# Global database instance
_memory_db: Optional[MemoryDatabase] = None

def get_memory_database() -> MemoryDatabase:
    """Get or create global memory database instance"""
    global _memory_db
    if _memory_db is None:
        _memory_db = MemoryDatabase()
    return _memory_db

class Sessions:
    """Mock Sessions class to replace the broken SQLite one"""
    
    @staticmethod
    def get_session_by_id(session_id: str) -> Optional[Session]:
        """Get session by ID (synchronous wrapper)"""
        db = get_memory_database()
        # Direct access for sync compatibility
        return db.sessions.get(session_id)
    
    @staticmethod
    def create_session(session_id: str, workspace_dir: str, device_id: str = None) -> Session:
        """Create session (synchronous wrapper)"""
        db = get_memory_database()
        # Create directly for sync compatibility
        session = Session(
            id=session_id,
            workspace_dir=workspace_dir,
            created_at=datetime.now().isoformat(),
            device_id=device_id,
            session_uuid=session_id,
            workspace_path=workspace_dir
        )
        db.sessions[session_id] = session
        logger.info(f"Created session {session_id}")
        return session
