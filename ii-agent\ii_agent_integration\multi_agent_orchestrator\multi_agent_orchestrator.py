from abc import ABC, abstractmethod
import threading
from typing import Dict, Any

import logging
import re
from .exceptions import AuthRequiredError, InvalidAgentTypeError, InvalidAgentNameError, AgentInternalError

# Configure the module's logger
logger = logging.getLogger(__name__)
# Prevent messages from propagating to the root logger, allowing tests to mock effectively
logger.propagate = False
if not logger.handlers:
    # Add a NullHandler to prevent "No handlers could be found for logger" messages
    logger.addHandler(logging.NullHandler())

class AgentCommunicationProtocol(ABC):
    """
    Abstract Base Class for defining the interface of an agent communication protocol.
    Concrete implementations will handle the actual transmission of messages between agents.
    """
    @abstractmethod
    def send_message(self, sender: str, receiver: str, content: Dict[str, Any]) -> bool:
        """
        Sends a message from a sender agent to a receiver agent.

        Args:
            sender (str): The name of the sending agent.
            receiver (str): The name of the receiving agent.
            content (Dict[str, Any]): The message content.

        Returns:
            bool: True if the message was successfully handed off for transmission, False otherwise.
        """
        pass

class BaseAgent(ABC):
    """
    Defines the fundamental interface for all agents that can be registered with
    and managed by the MultiAgentOrchestrator. Any class intended to function
    as an agent within this system must inherit from BaseAgent and implement
    its abstract methods.
    """
    @abstractmethod
    def receive_task(self, task_description: str):
        """
        Abstract method for agents to receive a task description.
        """
        pass

class MultiAgentOrchestrator:
    """
    The central component responsible for managing the lifecycle, communication,
    task assignment, and knowledge integration of single and multi-agent systems.
    """
    def __init__(self, auth_enabled: bool = False, communication_protocol: AgentCommunicationProtocol = None):
        """
        Initializes the MultiAgentOrchestrator.

        Args:
            auth_enabled (bool): If True, authentication is enabled for sensitive operations.
            communication_protocol (AgentCommunicationProtocol): An instance of the communication protocol.
                                                                 If None, a default (or mock in tests) should be used.
        """
        self.registered_agents: Dict[str, BaseAgent] = {}
        self.registered_agents_lock = threading.Lock()
        self._auth_enabled: bool = auth_enabled
        # Placeholder for authentication context, to be replaced by a more robust solution (ADR-007)
        self._is_authenticated_context: bool = False
        self.communication_protocol = communication_protocol

    def is_authenticated(self) -> bool:
        """
        Placeholder method to check if the current context is authenticated.
        In a real scenario, this would check a proper authentication context.
        """
        return self._is_authenticated_context

    def register_agent(self, agent_name: str, agent_instance: BaseAgent, requires_auth: bool = False) -> bool:
        """
        Registers an agent with the orchestrator.

        Args:
            agent_name (str): The unique name for the agent.
            agent_instance (BaseAgent): The instance of the agent to register.
            requires_auth (bool): If True, authentication is required for this registration.

        Returns:
            bool: True if successfully registered, False if agent_name already exists.

        Raises:
            AuthRequiredError: If authentication is required but not provided.
            InvalidAgentTypeError: If agent_instance does not inherit from BaseAgent.
            InvalidAgentNameError: If the agent_name does not meet validation criteria.
        """
        # 1. Input Validation: agent_name format (EC-1.1)
        self._validate_agent_name(agent_name)

        # 2. Authentication Check (FR-1.4, NFR-3.1)
        if requires_auth and self._auth_enabled:
            if not self.is_authenticated():
                raise AuthRequiredError("Authentication is required for agent registration.")

        # 3. Agent Type Validation (FR-1.3)
        if not isinstance(agent_instance, BaseAgent):
            raise InvalidAgentTypeError("Agent instance must inherit from BaseAgent.")

        # 4. Concurrency Control and Unique Agent Name Enforcement (FR-1.2, EC-1.2)
        with self.registered_agents_lock:
            # Check if an agent with the given name already exists.
            if agent_name in self.registered_agents:
                return False # Agent with this name already exists, registration failed

            # 5. Agent Registration (FR-1.1)
            # Add the agent instance to the registered agents dictionary.
            self.registered_agents[agent_name] = agent_instance

        # If all checks pass and registration is successful.
        return True

    def _validate_agent_name(self, agent_name: str):
        """
        Validates the format and content of the agent_name.
        Raises InvalidAgentNameError if the name is invalid.
        """
        if not agent_name:
            raise InvalidAgentNameError("Agent name cannot be empty.")
        if not isinstance(agent_name, str):
            raise InvalidAgentNameError("Agent name must be a string.")
        if len(agent_name) < 3 or len(agent_name) > 50:
            raise InvalidAgentNameError("Agent name must be between 3 and 50 characters long.")
        if not re.fullmatch(r"^[a-zA-Z0-9_-]+$", agent_name):
            raise InvalidAgentNameError("Agent name can only contain alphanumeric characters, hyphens, and underscores.")

    def _sanitize_task_description(self, task_description: str) -> str:
        """
        A private helper method that sanitizes an input string to remove potentially
        malicious characters, normalize spacing, and limit its length.
        """
        # Remove potentially malicious characters and ASCII control characters in one pass.
        # Characters to remove: < > & | ; ` $ ( ) { } [ ] \ " ' # / and ASCII control characters (\x00-\x1F\x7F)
        sanitized_text = re.sub(r'[<>&|;`$(){}[\]\\"\'#/\x00-\x1F\x7F]', '', task_description)

        # 3. Replace multiple whitespace characters with a single space and remove leading/trailing spaces.
        sanitized_text = re.sub(r'\s+', ' ', sanitized_text).strip()

        # 4. Truncate the resulting string to a maximum length of 256 characters.
        MAX_LENGTH = 256
        if len(sanitized_text) > MAX_LENGTH:
            sanitized_text = sanitized_text[:MAX_LENGTH]

        return sanitized_text

    def assign_task(self, agent_name: str, task_description: str, requires_auth: bool = False) -> bool:
        """
        Assigns a task to a previously registered agent.
        """
        # 1. Authentication Check (FR-2.6, NFR-3.1)
        if requires_auth and self._auth_enabled:
            if not self.is_authenticated():
                raise AuthRequiredError("Authentication is required for task assignment.")

        # 2. Agent Lookup (FR-2.5)
        agent = self.registered_agents.get(agent_name)
        if agent is None:
            logger.warning(f"Attempted to assign task to non-existent agent: {agent_name}")
            return False

        # 3. Task Description Sanitization (FR-2.1)
        sanitized_description = self._sanitize_task_description(task_description)

        # 4. Task Assignment to Agent (FR-2.1)
        try:
            agent.receive_task(sanitized_description)
            logger.info(f"Task successfully assigned to agent: {agent_name}")
            return True
        except AgentInternalError as e:
            logger.error(f"Agent {agent_name} reported an internal failure during task reception: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"An unexpected error occurred while assigning task to agent {agent_name}: {str(e)}. This implies agent internal failure.")
            return False

    def send_agent_message(self, sender_agent_name: str, receiver_agent_name: str, message_content: Dict[str, Any], requires_auth: bool = False) -> bool:
        """
        Facilitates sending a message from one agent to another using the configured AgentCommunicationProtocol.
        This method handles authentication checks, message content validation, and delegates the actual
        message transmission to the internal communication protocol instance. It ensures that messages
        are handed off successfully or reports failure, including handling protocol-level errors.

        Inputs:
            sender_agent_name (str): The name of the agent initiating the message.
            receiver_agent_name (str): The name of the agent intended to receive the message.
            message_content (Dict[str, Any]): The content of the message.
            requires_auth (bool): Flag indicating if authentication is required for this operation.
                               Defaults to FALSE.

        Outputs:
            bool: TRUE if the message was successfully handed off to the AgentCommunicationProtocol;
                 FALSE otherwise.

        Raises:
            AuthRequiredError: If authentication is enabled for the orchestrator and required for this
                               operation, but the operation is not authenticated.
        """

        # Step 1: Authentication Check (FR-3.3, NFR-3.1, EC-5.1)
        if self._auth_enabled and requires_auth:
            if not self.is_authenticated():
                raise AuthRequiredError("Authentication is required for this message sending operation.")

        # Step 2: Message Content Validation (EC-1.3)
        if not isinstance(message_content, dict):
            logger.error(f"Malformed message content received: Expected a dictionary, got {type(message_content)}")
            return False

        # Step 3: Hand-off to AgentCommunicationProtocol (FR-3.1, EC-3.1, EC-3.2, EC-3.3)
        try:
            was_sent_successfully = self.communication_protocol.send_message(
                sender=sender_agent_name,
                receiver=receiver_agent_name,
                content=message_content
            )

            if was_sent_successfully:
                logger.info(f"Message successfully handed off from {sender_agent_name} to {receiver_agent_name}")
                return True
            else:
                logger.warning(f"AgentCommunicationProtocol failed to send message from {sender_agent_name} to {receiver_agent_name}")
                return False

        except Exception as protocol_error:
            logger.error(f"An unexpected error occurred during message transmission via AgentCommunicationProtocol: {protocol_error}")
            return False