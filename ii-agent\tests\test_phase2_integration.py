"""
Test Phase 2 Integration in SimpleNeuralAgent
This test verifies that Phase 2 WASM acceleration is properly integrated
and falls back gracefully to Phase 1 and then to SimpleNeuralCore.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from neural.simple_neural import SimpleNeural<PERSON><PERSON>, PHASE1_A<PERSON><PERSON><PERSON>LE, PHASE2_AVAILABLE

def test_phase2_integration():
    """Test Phase 2 WASM integration with fallback hierarchy."""
    print("🧪 Testing Phase 2 Integration...")
    print(f"Phase 1 Available: {PHASE1_AVAILABLE}")
    print(f"Phase 2 Available: {PHASE2_AVAILABLE}")
    
    # Test with WASM enabled (should try Phase 2 first)
    agent = SimpleNeuralAgent(enable_neural=True, enable_wasm=True)
    print(f"Active Phase: {agent.active_phase}")
    
    # Test neural analysis with a coding request
    coding_message = "I need help implementing a machine learning algorithm"
    result = agent.get_neural_analysis(coding_message)
    
    print(f"\n🔍 Analysis Results for: '{coding_message}'")
    print(f"Agent Type: {result['agent_type']}")
    print(f"Confidence: {result['confidence']:.3f}")
    print(f"Strategy: {result['strategy']}")
    print(f"Reasoning: {result['reasoning']}")
    
    if 'performance' in result:
        perf = result['performance']
        print(f"Performance:")
        print(f"  - Phase: {perf['phase']}")
        print(f"  - Processing Time: {perf.get('processing_time_ms', 'N/A')} ms")
        print(f"  - WASM Accelerated: {perf.get('wasm_accelerated', False)}")
        print(f"  - Neural Networks: {perf.get('neural_networks', False)}")
    
    # Test performance info
    perf_info = agent.get_performance_info()
    print(f"\n📊 Performance Info:")
    print(f"Active Phase: {perf_info['active_phase']}")
    print(f"Capabilities: {perf_info['capabilities']}")
    
    # Test research request
    research_message = "Research quantum computing applications in cryptography"
    result2 = agent.get_neural_analysis(research_message)
    
    print(f"\n🔍 Analysis Results for: '{research_message}'")
    print(f"Agent Type: {result2['agent_type']}")
    print(f"Confidence: {result2['confidence']:.3f}")
    print(f"Strategy: {result2['strategy']}")
    
    # Test with WASM disabled (should use Phase 1)
    print(f"\n🔄 Testing Phase 1 Only (WASM disabled)...")
    agent_phase1 = SimpleNeuralAgent(enable_neural=True, enable_wasm=False)
    print(f"Active Phase: {agent_phase1.active_phase}")
    
    result3 = agent_phase1.get_neural_analysis(coding_message)
    print(f"Agent Type: {result3['agent_type']}")
    print(f"Strategy: {result3['strategy']}")
    
    # Test with neural disabled (should use fallback)
    print(f"\n🔄 Testing Fallback Only (Neural disabled)...")
    agent_fallback = SimpleNeuralAgent(enable_neural=False)
    print(f"Active Phase: {agent_fallback.active_phase}")
    
    result4 = agent_fallback.get_neural_analysis(coding_message)
    print(f"Agent Type: {result4['agent_type']}")
    print(f"Strategy: {result4['strategy']}")
    
    print(f"\n✅ Phase 2 Integration Test Complete!")
    return True

if __name__ == "__main__":
    try:
        test_phase2_integration()
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
