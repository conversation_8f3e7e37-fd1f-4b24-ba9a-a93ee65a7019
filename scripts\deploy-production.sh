#!/bin/bash

# Production Deployment Script for ii-agent Multi-Agent System
# This script deploys the complete production stack with GPU acceleration,
# monitoring, and health checks.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.yaml"
MULTI_AGENT_COMPOSE_FILE="docker-compose.multi-agent.yaml"
ENV_FILE=".env"
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"

echo -e "${BLUE}🚀 ii-agent Production Deployment${NC}"
echo "=================================="

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check NVIDIA Docker (for GPU support)
    if command -v nvidia-docker &> /dev/null || docker info | grep -q nvidia; then
        print_status "NVIDIA Docker support detected"
        export GPU_SUPPORT=true
    else
        print_warning "NVIDIA Docker not detected - GPU acceleration will be disabled"
        export GPU_SUPPORT=false
    fi
    
    print_status "Prerequisites check completed"
}

# Create environment file if it doesn't exist
create_env_file() {
    if [ ! -f "$ENV_FILE" ]; then
        print_info "Creating environment file..."
        cat > "$ENV_FILE" << EOF
# ii-agent Production Configuration

# 🚀 Multi-Agent Configuration
II_AGENT_MULTI_AGENT=true
II_AGENT_GPU_ENABLED=${GPU_SUPPORT:-false}
II_AGENT_GPU_DEVICE_ID=0
II_AGENT_MIXED_PRECISION=true
II_AGENT_GPU_MEMORY_STRATEGY=balanced
II_AGENT_MAX_AGENTS=10
II_AGENT_COORDINATION_TIMEOUT=30000
II_AGENT_ENABLE_LEARNING=true

# 🧠 Neural Network Configuration
II_AGENT_NEURAL_INPUT_SIZE=512
II_AGENT_NEURAL_HIDDEN_SIZES=256,128,64
II_AGENT_NEURAL_OUTPUT_SIZE=32
II_AGENT_LEARNING_RATE=0.001
II_AGENT_BATCH_SIZE=64

# 📊 Monitoring Configuration
II_AGENT_ENABLE_METRICS=true
II_AGENT_METRICS_PORT=9090
II_AGENT_LOG_LEVEL=INFO
II_AGENT_PERFORMANCE_TRACKING=true

# 🔐 Security Configuration
GRAFANA_PASSWORD=admin123

# 🌐 Network Configuration
BASE_URL=http://localhost:8000
STATIC_FILE_BASE_URL=http://localhost:8000
CODE_SERVER_PORT=9000

# 🔧 System Configuration
COMPOSE_PROJECT_NAME=ii-agent-production
CUDA_VISIBLE_DEVICES=0
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
EOF
        print_status "Environment file created"
    else
        print_info "Environment file already exists"
    fi
}

# Create monitoring directories
create_monitoring_dirs() {
    print_info "Creating monitoring directories..."
    
    mkdir -p monitoring/grafana/dashboards
    mkdir -p monitoring/grafana/datasources
    mkdir -p logs
    mkdir -p metrics
    mkdir -p models
    
    print_status "Monitoring directories created"
}

# Backup existing deployment
backup_existing() {
    if docker-compose -f "$COMPOSE_FILE" -f "$MULTI_AGENT_COMPOSE_FILE" ps | grep -q "Up"; then
        print_info "Backing up existing deployment..."
        
        mkdir -p "$BACKUP_DIR"
        
        # Export current containers
        docker-compose -f "$COMPOSE_FILE" -f "$MULTI_AGENT_COMPOSE_FILE" config > "$BACKUP_DIR/docker-compose-backup.yaml"
        
        # Backup volumes
        docker run --rm -v ii_agent_models:/data -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/models.tar.gz -C /data .
        docker run --rm -v ii_agent_logs:/data -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/logs.tar.gz -C /data .
        
        print_status "Backup completed: $BACKUP_DIR"
    fi
}

# Pull latest images
pull_images() {
    print_info "Pulling latest Docker images..."
    
    docker-compose -f "$COMPOSE_FILE" -f "$MULTI_AGENT_COMPOSE_FILE" pull
    
    print_status "Images pulled successfully"
}

# Deploy the stack
deploy_stack() {
    print_info "Deploying production stack..."
    
    # Stop existing containers
    docker-compose -f "$COMPOSE_FILE" -f "$MULTI_AGENT_COMPOSE_FILE" down
    
    # Start the stack
    docker-compose -f "$COMPOSE_FILE" -f "$MULTI_AGENT_COMPOSE_FILE" up -d
    
    print_status "Production stack deployed"
}

# Wait for services to be ready
wait_for_services() {
    print_info "Waiting for services to be ready..."
    
    # Wait for backend
    print_info "Waiting for backend service..."
    timeout 120 bash -c 'until curl -f http://localhost:8000/health &>/dev/null; do sleep 2; done'
    
    # Wait for Prometheus
    print_info "Waiting for Prometheus..."
    timeout 60 bash -c 'until curl -f http://localhost:9090/-/ready &>/dev/null; do sleep 2; done'
    
    # Wait for Grafana
    print_info "Waiting for Grafana..."
    timeout 60 bash -c 'until curl -f http://localhost:3000/api/health &>/dev/null; do sleep 2; done'
    
    print_status "All services are ready"
}

# Run health checks
run_health_checks() {
    print_info "Running health checks..."
    
    # Check backend health
    if curl -f http://localhost:8000/health &>/dev/null; then
        print_status "Backend health check passed"
    else
        print_error "Backend health check failed"
        return 1
    fi
    
    # Check multi-agent system
    if curl -f http://localhost:8000/api/multi-agent/status &>/dev/null; then
        print_status "Multi-agent system health check passed"
    else
        print_warning "Multi-agent system health check failed"
    fi
    
    # Check GPU status (if enabled)
    if [ "$GPU_SUPPORT" = "true" ]; then
        if curl -f http://localhost:8000/api/gpu/status &>/dev/null; then
            print_status "GPU system health check passed"
        else
            print_warning "GPU system health check failed"
        fi
    fi
    
    print_status "Health checks completed"
}

# Display deployment information
show_deployment_info() {
    echo ""
    echo -e "${GREEN}🎉 Deployment Completed Successfully!${NC}"
    echo "====================================="
    echo ""
    echo -e "${BLUE}📊 Service URLs:${NC}"
    echo "  • ii-agent Backend:    http://localhost:8000"
    echo "  • Grafana Dashboard:   http://localhost:3000 (admin/admin123)"
    echo "  • Prometheus:          http://localhost:9090"
    echo "  • Node Exporter:       http://localhost:9100"
    echo "  • cAdvisor:            http://localhost:8080"
    echo ""
    echo -e "${BLUE}🔧 Management Commands:${NC}"
    echo "  • View logs:           docker-compose -f $COMPOSE_FILE -f $MULTI_AGENT_COMPOSE_FILE logs -f"
    echo "  • Stop services:       docker-compose -f $COMPOSE_FILE -f $MULTI_AGENT_COMPOSE_FILE down"
    echo "  • Restart services:    docker-compose -f $COMPOSE_FILE -f $MULTI_AGENT_COMPOSE_FILE restart"
    echo "  • View status:         docker-compose -f $COMPOSE_FILE -f $MULTI_AGENT_COMPOSE_FILE ps"
    echo ""
    echo -e "${BLUE}📊 Monitoring:${NC}"
    echo "  • GPU Support:         $GPU_SUPPORT"
    echo "  • Multi-Agent Mode:    Enabled"
    echo "  • Neural Networks:     Real PyTorch implementation"
    echo "  • Coordination:        Real consensus algorithms"
    echo ""
    echo -e "${YELLOW}⚠️  Important Notes:${NC}"
    echo "  • Change Grafana password after first login"
    echo "  • Monitor GPU memory usage if GPU is enabled"
    echo "  • Check logs regularly for any issues"
    echo "  • Backup models and data regularly"
}

# Main deployment flow
main() {
    check_prerequisites
    create_env_file
    create_monitoring_dirs
    backup_existing
    pull_images
    deploy_stack
    wait_for_services
    run_health_checks
    show_deployment_info
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        print_info "Stopping production stack..."
        docker-compose -f "$COMPOSE_FILE" -f "$MULTI_AGENT_COMPOSE_FILE" down
        print_status "Production stack stopped"
        ;;
    "restart")
        print_info "Restarting production stack..."
        docker-compose -f "$COMPOSE_FILE" -f "$MULTI_AGENT_COMPOSE_FILE" restart
        print_status "Production stack restarted"
        ;;
    "logs")
        docker-compose -f "$COMPOSE_FILE" -f "$MULTI_AGENT_COMPOSE_FILE" logs -f
        ;;
    "status")
        docker-compose -f "$COMPOSE_FILE" -f "$MULTI_AGENT_COMPOSE_FILE" ps
        ;;
    "health")
        run_health_checks
        ;;
    *)
        echo "Usage: $0 {deploy|stop|restart|logs|status|health}"
        echo ""
        echo "Commands:"
        echo "  deploy  - Deploy the complete production stack (default)"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services"
        echo "  logs    - Show logs from all services"
        echo "  status  - Show status of all services"
        echo "  health  - Run health checks"
        exit 1
        ;;
esac
