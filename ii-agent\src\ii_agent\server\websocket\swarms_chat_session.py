"""
Swarms-powered chat session for ii-agent WebSocket connections.

This replaces the broken multi-agent chat session with a production-ready
implementation using the Swarms framework.
"""

import logging
import uuid
from pathlib import Path
from typing import Optional, Dict, Any
import json
import asyncio

from fastapi import WebSocket

from ii_agent.core.config.ii_agent_config import IIAgentConfig
from ii_agent.core.storage.files import FileStore
from ii_agent.integrations.swarms_integration import get_swarms_manager, is_swarms_available

logger = logging.getLogger(__name__)


class SwarmsChatSession:
    """
    WebSocket chat session powered by Swarms framework.
    
    Provides multi-agent capabilities through Swarms while maintaining
    compatibility with ii-agent's WebSocket interface.
    """
    
    def __init__(
        self,
        websocket: WebSocket,
        session_uuid: uuid.UUID,
        file_store: FileStore,
        config: IIAgentConfig,
    ):
        self.websocket = websocket
        self.session_uuid = session_uuid
        self.file_store = file_store
        self.config = config
        
        # Initialize Swarms manager
        if not is_swarms_available():
            raise ImportError("Swarms framework is required but not available")
        
        self.swarms_manager = get_swarms_manager()
        
        # Session state
        self.is_active = True
        self.message_count = 0
        
        # Default agents for this session
        self._setup_default_agents()
        
        logger.info(f"✅ SwarmsChatSession initialized for session {session_uuid}")
    
    def _setup_default_agents(self):
        """Set up default agents for the chat session."""
        try:
            # Create a primary assistant agent
            self.swarms_manager.create_agent(
                name=f"assistant_{self.session_uuid}",
                model_name="gpt-4o-mini",
                system_prompt="You are a helpful AI assistant. Provide clear, accurate, and helpful responses.",
            )
            
            # Create a researcher agent for complex queries
            self.swarms_manager.create_agent(
                name=f"researcher_{self.session_uuid}",
                model_name="gpt-4o-mini", 
                system_prompt="You are a research specialist. Analyze questions thoroughly and provide detailed, well-researched responses.",
            )
            
            # Create a code assistant for programming tasks
            self.swarms_manager.create_agent(
                name=f"coder_{self.session_uuid}",
                model_name="gpt-4o-mini",
                system_prompt="You are a programming expert. Help with code analysis, debugging, and development tasks.",
            )
            
            logger.info(f"✅ Default agents created for session {self.session_uuid}")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup default agents: {e}")
            # Continue without multi-agent - fallback to single agent
    
    async def start_chat_loop(self):
        """Start the main chat loop for handling WebSocket messages."""
        try:
            await self._send_welcome_message()
            
            while self.is_active:
                try:
                    # Receive message from WebSocket
                    message = await self.websocket.receive_text()
                    await self._handle_message(message)
                    
                except Exception as e:
                    logger.error(f"❌ Error in chat loop: {e}")
                    await self._send_error_message(str(e))
                    break
                    
        except Exception as e:
            logger.error(f"❌ Fatal error in chat session: {e}")
        finally:
            self.cleanup()
    
    async def _send_welcome_message(self):
        """Send welcome message to the client."""
        welcome_msg = {
            "type": "system",
            "content": "🤖 Welcome! Multi-agent system powered by Swarms is ready.",
            "session_id": str(self.session_uuid),
            "agents_available": self.swarms_manager.list_agents()
        }
        await self.websocket.send_text(json.dumps(welcome_msg))
    
    async def _handle_message(self, message: str):
        """Handle incoming message from WebSocket."""
        try:
            # Parse message
            if message.startswith('{'):
                # JSON message
                msg_data = json.loads(message)
                content = msg_data.get('content', '')
                msg_type = msg_data.get('type', 'user')
                agent_name = msg_data.get('agent', None)
                workflow_type = msg_data.get('workflow', 'single')
            else:
                # Plain text message
                content = message
                msg_type = 'user'
                agent_name = None
                workflow_type = 'single'
            
            if not content.strip():
                return
            
            self.message_count += 1
            
            # Send typing indicator
            await self._send_typing_indicator(True)
            
            # Process message based on type
            if workflow_type == 'sequential':
                response = await self._handle_sequential_workflow(content)
            elif workflow_type == 'concurrent':
                response = await self._handle_concurrent_workflow(content)
            elif agent_name:
                response = await self._handle_single_agent(content, agent_name)
            else:
                response = await self._handle_auto_agent_selection(content)
            
            # Send response
            await self._send_response(response)
            
        except json.JSONDecodeError:
            await self._send_error_message("Invalid JSON message format")
        except Exception as e:
            logger.error(f"❌ Error handling message: {e}")
            await self._send_error_message(f"Error processing message: {str(e)}")
        finally:
            await self._send_typing_indicator(False)
    
    async def _handle_single_agent(self, content: str, agent_name: str) -> str:
        """Handle message with a specific agent."""
        try:
            # Use session-specific agent name
            full_agent_name = f"{agent_name}_{self.session_uuid}"
            
            if full_agent_name not in self.swarms_manager.list_agents():
                return f"❌ Agent '{agent_name}' not found. Available agents: {', '.join(self.swarms_manager.list_agents())}"
            
            # Run agent asynchronously
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                self.swarms_manager.run_agent,
                full_agent_name,
                content
            )
            
            return response
            
        except Exception as e:
            logger.error(f"❌ Error with agent {agent_name}: {e}")
            return f"❌ Error with agent {agent_name}: {str(e)}"
    
    async def _handle_sequential_workflow(self, content: str) -> str:
        """Handle message with sequential workflow."""
        try:
            workflow_name = f"sequential_{self.session_uuid}"
            agent_names = [
                f"researcher_{self.session_uuid}",
                f"assistant_{self.session_uuid}"
            ]
            
            # Create workflow if it doesn't exist
            if workflow_name not in self.swarms_manager.list_workflows():
                self.swarms_manager.create_sequential_workflow(workflow_name, agent_names)
            
            # Run workflow asynchronously
            response = await self.swarms_manager.run_workflow_async(workflow_name, content)
            return response
            
        except Exception as e:
            logger.error(f"❌ Error with sequential workflow: {e}")
            return f"❌ Error with sequential workflow: {str(e)}"
    
    async def _handle_concurrent_workflow(self, content: str) -> str:
        """Handle message with concurrent workflow."""
        try:
            workflow_name = f"concurrent_{self.session_uuid}"
            agent_names = [
                f"assistant_{self.session_uuid}",
                f"researcher_{self.session_uuid}",
                f"coder_{self.session_uuid}"
            ]
            
            # Create workflow if it doesn't exist
            if workflow_name not in self.swarms_manager.list_workflows():
                self.swarms_manager.create_concurrent_workflow(workflow_name, agent_names)
            
            # Run workflow asynchronously
            response = await self.swarms_manager.run_workflow_async(workflow_name, content)
            return response
            
        except Exception as e:
            logger.error(f"❌ Error with concurrent workflow: {e}")
            return f"❌ Error with concurrent workflow: {str(e)}"
    
    async def _handle_auto_agent_selection(self, content: str) -> str:
        """Automatically select the best agent based on content."""
        try:
            # Simple heuristics for agent selection
            content_lower = content.lower()
            
            if any(keyword in content_lower for keyword in ['code', 'program', 'debug', 'function', 'class']):
                agent_name = f"coder_{self.session_uuid}"
            elif any(keyword in content_lower for keyword in ['research', 'analyze', 'study', 'investigate']):
                agent_name = f"researcher_{self.session_uuid}"
            else:
                agent_name = f"assistant_{self.session_uuid}"
            
            return await self._handle_single_agent(content, agent_name.split('_')[0])
            
        except Exception as e:
            logger.error(f"❌ Error with auto agent selection: {e}")
            return f"❌ Error with auto agent selection: {str(e)}"
    
    async def _send_response(self, content: str):
        """Send response to WebSocket client."""
        response_msg = {
            "type": "assistant",
            "content": content,
            "message_id": self.message_count,
            "session_id": str(self.session_uuid)
        }
        await self.websocket.send_text(json.dumps(response_msg))
    
    async def _send_error_message(self, error: str):
        """Send error message to WebSocket client."""
        error_msg = {
            "type": "error",
            "content": f"❌ {error}",
            "session_id": str(self.session_uuid)
        }
        await self.websocket.send_text(json.dumps(error_msg))
    
    async def _send_typing_indicator(self, is_typing: bool):
        """Send typing indicator to WebSocket client."""
        typing_msg = {
            "type": "typing",
            "is_typing": is_typing,
            "session_id": str(self.session_uuid)
        }
        await self.websocket.send_text(json.dumps(typing_msg))
    
    def cleanup(self):
        """Clean up session resources."""
        try:
            self.is_active = False
            logger.info(f"✅ SwarmsChatSession {self.session_uuid} cleaned up")
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
