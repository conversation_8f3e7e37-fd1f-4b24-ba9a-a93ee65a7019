#!/usr/bin/env python3
"""
RUV-FANN True Functionality Verification
=======================================

This script performs deep validation to ensure RUV-FANN is truly working
at maximum potential and not just simulating functionality.
"""

import sys
import os
import time
import traceback
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def deep_functionality_test():
    """Perform comprehensive validation of RUV-FANN true functionality"""
    print("🔍 RUV-FANN Deep Functionality Verification")
    print("=" * 70)
    
    verification_results = {
        "tests_passed": 0,
        "tests_failed": 0,
        "critical_issues": [],
        "performance_metrics": {},
        "system_integrity": "UNKNOWN"
    }
    
    try:
        # Test 1: Import validation with error detection
        print("\n1. Testing Import Integrity...")
        from ii_agent.neural.simple_neural import RuvFANNNeuralAgent, unlock_ruv_fann_potential
        print("   ✅ Core imports successful")
        verification_results["tests_passed"] += 1
        
        # Test 2: System initialization validation
        print("\n2. Testing System Initialization Integrity...")
        agent = RuvFANNNeuralAgent(enable_gpu=True, enable_wasm=True, enable_simple_models=True)
        
        # Validate that coordinators are actually initialized
        if hasattr(agent, 'wasm_coordinator') and agent.wasm_coordinator is not None:
            print("   ✅ WASM coordinator truly initialized")
            verification_results["tests_passed"] += 1
        else:
            print("   ❌ WASM coordinator not properly initialized")
            verification_results["tests_failed"] += 1
            verification_results["critical_issues"].append("WASM coordinator missing")
        
        if hasattr(agent, 'gpu_coordinator'):
            if agent.gpu_coordinator is not None:
                print("   ✅ GPU coordinator initialized")
            else:
                print("   ⚠️  GPU coordinator not initialized (expected on systems without GPU)")
            verification_results["tests_passed"] += 1
        
        # Test 3: Actual neural processing validation
        print("\n3. Testing Real Neural Processing...")
        test_inputs = [
            "Complex algorithm optimization analysis",
            "Real-time data processing requirements", 
            "Neural network architecture design",
            "Performance optimization strategies"
        ]
        
        processing_times = []
        agent_selections = []
        
        for i, test_input in enumerate(test_inputs):
            start_time = time.time()
            result = agent.analyze_input(test_input)
            processing_time = time.time() - start_time
            processing_times.append(processing_time * 1000)  # Convert to ms
            
            # Validate result structure
            required_fields = ["ruv_fann_path", "execution_time_ms", "agent_type", "utilization"]
            missing_fields = [field for field in required_fields if field not in result]
            
            if not missing_fields:
                print(f"   ✅ Test {i+1}: {result['ruv_fann_path']} in {processing_time*1000:.2f}ms")
                agent_selections.append(result['agent_type'])
                verification_results["tests_passed"] += 1
            else:
                print(f"   ❌ Test {i+1}: Missing fields {missing_fields}")
                verification_results["tests_failed"] += 1
                verification_results["critical_issues"].append(f"Incomplete result structure: {missing_fields}")
        
        # Test 4: Performance consistency validation
        print("\n4. Testing Performance Consistency...")
        avg_time = sum(processing_times) / len(processing_times)
        max_time = max(processing_times)
        min_time = min(processing_times)
        
        if max_time < 100:  # Under 100ms is reasonable
            print(f"   ✅ Performance within bounds: avg={avg_time:.2f}ms, max={max_time:.2f}ms")
            verification_results["tests_passed"] += 1
        else:
            print(f"   ❌ Performance degraded: avg={avg_time:.2f}ms, max={max_time:.2f}ms")
            verification_results["tests_failed"] += 1
            verification_results["critical_issues"].append(f"Performance degradation: {max_time:.2f}ms")
        
        verification_results["performance_metrics"] = {
            "average_response_ms": avg_time,
            "max_response_ms": max_time,
            "min_response_ms": min_time,
            "agent_diversity": len(set(agent_selections))
        }
        
        # Test 5: Agent diversity validation
        print("\n5. Testing Agent Selection Diversity...")
        unique_agents = set(agent_selections)
        if len(unique_agents) > 1:
            print(f"   ✅ Agent diversity confirmed: {len(unique_agents)} different agents selected")
            print(f"   Agent types: {', '.join(unique_agents)}")
            verification_results["tests_passed"] += 1
        else:
            print(f"   ⚠️  Limited agent diversity: only {unique_agents}")
            # This might be normal behavior, not necessarily a failure
        
        # Test 6: System status validation
        print("\n6. Testing System Status Integrity...")
        status = agent.get_system_status()
        
        if status["system_status"] == "OPERATIONAL":
            print("   ✅ System reports operational status")
            verification_results["tests_passed"] += 1
        else:
            print(f"   ❌ System status: {status['system_status']}")
            verification_results["tests_failed"] += 1
            verification_results["critical_issues"].append(f"System not operational: {status['system_status']}")
        
        utilization = status.get("system_utilization", 0)
        if utilization > 0:
            print(f"   ✅ System utilization: {utilization:.1f}%")
            verification_results["tests_passed"] += 1
        else:
            print(f"   ❌ Zero system utilization detected")
            verification_results["tests_failed"] += 1
            verification_results["critical_issues"].append("Zero utilization indicates non-functional system")
        
        # Test 7: Full potential unlock validation
        print("\n7. Testing Full Potential Unlock...")
        potential_start = time.time()
        potential_result = unlock_ruv_fann_potential()
        potential_time = time.time() - potential_start
        
        if potential_result and "system_name" in potential_result:
            print(f"   ✅ Full potential unlocked in {potential_time:.2f}s")
            print(f"   System: {potential_result.get('system_name', 'Unknown')} v{potential_result.get('version', 'Unknown')}")
            print(f"   Average response: {potential_result.get('average_response_time_ms', 0):.2f}ms")
            verification_results["tests_passed"] += 1
        else:
            print(f"   ❌ Full potential unlock failed")
            verification_results["tests_failed"] += 1
            verification_results["critical_issues"].append("Potential unlock mechanism failed")
        
        # Final assessment
        total_tests = verification_results["tests_passed"] + verification_results["tests_failed"]
        success_rate = (verification_results["tests_passed"] / total_tests * 100) if total_tests > 0 else 0
        
        if success_rate >= 85 and len(verification_results["critical_issues"]) == 0:
            verification_results["system_integrity"] = "EXCELLENT"
        elif success_rate >= 70 and len(verification_results["critical_issues"]) <= 1:
            verification_results["system_integrity"] = "GOOD"
        elif success_rate >= 50:
            verification_results["system_integrity"] = "FUNCTIONAL"
        else:
            verification_results["system_integrity"] = "COMPROMISED"
        
        print("\n" + "=" * 70)
        print("🎯 VERIFICATION RESULTS:")
        print(f"   Tests Passed: {verification_results['tests_passed']}")
        print(f"   Tests Failed: {verification_results['tests_failed']}")
        print(f"   Success Rate: {success_rate:.1f}%")
        print(f"   System Integrity: {verification_results['system_integrity']}")
        print(f"   Critical Issues: {len(verification_results['critical_issues'])}")
        
        if verification_results["critical_issues"]:
            print("\n❌ Critical Issues Found:")
            for issue in verification_results["critical_issues"]:
                print(f"   - {issue}")
        
        print(f"\n📊 Performance Metrics:")
        for key, value in verification_results["performance_metrics"].items():
            if isinstance(value, float):
                print(f"   {key}: {value:.2f}")
            else:
                print(f"   {key}: {value}")
        
        if verification_results["system_integrity"] in ["EXCELLENT", "GOOD"]:
            print("\n🎉 RUV-FANN IS TRULY WORKING AT MAXIMUM POTENTIAL!")
            print("   System demonstrates real neural processing capabilities")
            print("   Performance metrics within acceptable bounds")
            print("   All core functionalities operational")
        elif verification_results["system_integrity"] == "FUNCTIONAL":
            print("\n⚠️  RUV-FANN is functional but has some limitations")
            print("   Core functionality works but optimization needed")
        else:
            print("\n❌ RUV-FANN has significant functionality issues")
            print("   System may be simulating rather than truly processing")
        
        return verification_results
        
    except Exception as e:
        verification_results["tests_failed"] += 1
        verification_results["critical_issues"].append(f"System failure: {str(e)}")
        verification_results["system_integrity"] = "FAILED"
        
        print(f"\n💥 CRITICAL SYSTEM FAILURE: {e}")
        print("\nFull traceback:")
        traceback.print_exc()
        return verification_results

if __name__ == "__main__":
    results = deep_functionality_test()
    
    # Exit with appropriate code
    if results["system_integrity"] in ["EXCELLENT", "GOOD"]:
        exit(0)  # Success
    elif results["system_integrity"] == "FUNCTIONAL":
        exit(1)  # Functional but with issues
    else:
        exit(2)  # Critical failure
