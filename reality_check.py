#!/usr/bin/env python3
"""
REALITY CHECK - Direct test to see if the system is real or fake
"""

import sys
import asyncio
import os

sys.path.insert(0, 'ii-agent')

print('🔍 REALITY CHECK - Is the system REALLY working?')
print('=' * 60)

# Test 1: Check for mock patterns in the code
print('\n1. Scanning Code for Mock/Fake Patterns...')
try:
    mock_files = []
    real_files = []
    
    # Check key files for mock patterns
    files_to_check = [
        'ii-agent/ii_agent_integration/swarms/swarms_integration.py',
        'ii-agent/ii_agent_integration/swarms/real_multi_agent_system.py',
        'ii-agent/ii_agent_integration/neural/real_neural_networks.py'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # Count mock indicators
                mock_indicators = [
                    'mock', 'Mock', 'fake', 'Fake', 'NotImplementedError',
                    'placeholder', 'stub', 'dummy', 'TODO', 'FIXME'
                ]
                
                mock_count = sum(content.lower().count(indicator.lower()) for indicator in mock_indicators)
                
                # Count real implementation indicators
                real_indicators = [
                    'torch.', 'nn.', 'asyncio.', 'await ', 'real', 'actual',
                    'implementation', 'execute', 'process'
                ]
                
                real_count = sum(content.count(indicator) for indicator in real_indicators)
                
                print(f'   {os.path.basename(file_path)}:')
                print(f'     Mock indicators: {mock_count}')
                print(f'     Real indicators: {real_count}')
                
                if mock_count > real_count:
                    mock_files.append(file_path)
                else:
                    real_files.append(file_path)
    
    print(f'\n   Files with more MOCK indicators: {len(mock_files)}')
    print(f'   Files with more REAL indicators: {len(real_files)}')
    
except Exception as e:
    print(f'   ❌ Code scan failed: {e}')

# Test 2: Test actual coordination
print('\n2. Testing Multi-Agent Coordination...')
try:
    from ii_agent_integration.swarms.real_multi_agent_system import RealMultiAgentSystem
    
    async def test_coordination():
        swarm = RealMultiAgentSystem()
        agent_count = len(swarm.agents)
        print(f'   Created swarm with {agent_count} agents')
        
        # Test coordination multiple times to check for variation
        results = []
        for i in range(3):
            result = await swarm.coordinate_task(f'Test task {i}')
            success = result.get("success", False)
            exec_time = result.get("execution_time_ms", 0)
            results.append((success, exec_time))
            print(f'   Test {i+1}: Success={success}, Time={exec_time:.2f}ms')
        
        # Check for variation (real) vs identical results (mock)
        success_values = [r[0] for r in results]
        time_values = [r[1] for r in results]
        
        if len(set(success_values)) == 1 and all(success_values):
            print('   ⚠️ SUSPICIOUS: All tests identical success')
        
        if len(set(time_values)) == 1:
            print('   ⚠️ SUSPICIOUS: Identical execution times')
        else:
            print('   ✅ GOOD: Execution times vary (suggests real processing)')
        
        await swarm.cleanup_agents()
        return results
    
    coordination_results = asyncio.run(test_coordination())
    
except Exception as e:
    print(f'   ❌ Coordination test failed: {e}')

# Test 3: Test neural networks
print('\n3. Testing Neural Networks...')
try:
    from ii_agent_integration.neural.real_neural_networks import RealNeuralAgent, AgentSpecialization
    
    async def test_neural():
        agent = RealNeuralAgent('test_agent', AgentSpecialization.RESEARCHER)
        param_count = sum(p.numel() for p in agent.network.parameters())
        print(f'   Neural network has {param_count} parameters')
        
        # Test multiple inferences
        confidences = []
        for i in range(3):
            result = await agent.analyze_task(f'Neural test {i}')
            confidence = result.get('confidence', 0)
            confidences.append(confidence)
            print(f'   Test {i+1}: Confidence={confidence:.3f}')
        
        # Check for variation
        if len(set(confidences)) == 1:
            print('   ⚠️ SUSPICIOUS: Identical confidence scores')
        else:
            print('   ✅ GOOD: Confidence scores vary (suggests real neural processing)')
        
        return confidences
    
    neural_results = asyncio.run(test_neural())
    
except Exception as e:
    print(f'   ❌ Neural test failed: {e}')

# Test 4: Check imports and dependencies
print('\n4. Testing Critical Imports...')
try:
    import torch
    print(f'   ✅ PyTorch available: {torch.__version__}')
    
    import prometheus_client
    print('   ✅ Prometheus client available')
    
    import psutil
    print('   ✅ System monitoring available')
    
    # Test if CUDA is available
    if torch.cuda.is_available():
        print(f'   ✅ CUDA available: {torch.cuda.device_count()} devices')
    else:
        print('   ⚠️ CUDA not available (CPU-only mode)')
    
except Exception as e:
    print(f'   ❌ Import test failed: {e}')

print('\n' + '=' * 60)
print('🎯 REALITY CHECK SUMMARY:')
print('   If you see mostly ✅ GOOD indicators, the system is likely real')
print('   If you see mostly ⚠️ SUSPICIOUS indicators, the system is likely fake')
print('   Multiple identical results suggest mock/fake implementations')
print('=' * 60)
