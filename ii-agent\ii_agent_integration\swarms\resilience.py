"""
Resilience utilities for backend calls: retries, backoff, jitter, timeouts, circuit breaker, idempotency.
"""
import asyncio
import os
import random
import string
import time
from typing import Any, Awaitable, Callable, Optional


def _env_int(name: str, default: int) -> int:
    try:
        return int(os.getenv(name, str(default)))
    except Exception:
        return default


def generate_idempotency_key(prefix: str = "swarm") -> str:
    rand = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
    return f"{prefix}_{int(time.time()*1000)}_{rand}"


async def with_timeout(coro: Awaitable, timeout_ms: int):
    return await asyncio.wait_for(coro, timeout=timeout_ms / 1000.0)


async def async_retry(fn: Callable[[], Awaitable[Any]], *, retries: int, base_delay_ms: int, max_delay_ms: int, jitter_ms: int) -> Any:
    attempt = 0
    delay = base_delay_ms
    last_exc = None
    while attempt <= retries:
        try:
            return await fn()
        except Exception as e:
            last_exc = e
            if attempt == retries:
                break
            # exponential backoff with jitter
            sleep_ms = min(delay, max_delay_ms) + random.randint(0, max(0, jitter_ms))
            await asyncio.sleep(sleep_ms / 1000.0)
            delay = min(delay * 2, max_delay_ms)
            attempt += 1
    raise last_exc  # type: ignore


class CircuitBreaker:
    def __init__(self, failure_threshold: int, reset_timeout_ms: int):
        self.failure_threshold = max(1, failure_threshold)
        self.reset_timeout_ms = reset_timeout_ms
        self._failures = 0
        self._state = "closed"  # closed | open | half_open
        self._opened_at = 0.0
        self._last_error: Optional[str] = None

    @property
    def state(self) -> str:
        # auto transition from open -> half_open when timeout passes
        if self._state == "open" and (time.time() * 1000 - self._opened_at) >= self.reset_timeout_ms:
            self._state = "half_open"
        return self._state

    @property
    def last_error(self) -> Optional[str]:
        return self._last_error

    def allow(self) -> bool:
        return self.state in ("closed", "half_open")

    def on_success(self):
        self._failures = 0
        self._state = "closed"
        self._last_error = None

    def on_failure(self, err: Exception):
        self._last_error = str(err)
        self._failures += 1
        if self._failures >= self.failure_threshold:
            self._state = "open"
            self._opened_at = time.time() * 1000


# Defaults (overridable via env)
DEFAULT_TIMEOUT_MS = _env_int("SWARMS_BACKEND_TIMEOUT_MS", 2500)
DEFAULT_RETRIES = _env_int("SWARMS_BACKEND_RETRIES", 2)
DEFAULT_BASE_DELAY_MS = _env_int("SWARMS_BACKEND_BASE_DELAY_MS", 100)
DEFAULT_MAX_DELAY_MS = _env_int("SWARMS_BACKEND_MAX_DELAY_MS", 1000)
DEFAULT_JITTER_MS = _env_int("SWARMS_BACKEND_JITTER_MS", 100)
DEFAULT_CB_THRESHOLD = _env_int("SWARMS_CB_THRESHOLD", 5)
DEFAULT_CB_RESET_MS = _env_int("SWARMS_CB_RESET_MS", 10000)
