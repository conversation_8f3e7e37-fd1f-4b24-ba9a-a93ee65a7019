"""
Lightweight in-memory execution history and audit log for swarms coordination.

Provides:
- HistoryStore: append events, get recent entries, compute stats, last_error.

Kept simple and dependency-free; can be extended to persistent storage later.
"""
from collections import deque
from dataclasses import dataclass, asdict
from typing import Any, Deque, Dict, List, Optional
import time


@dataclass
class HistoryEntry:
    timestamp_ms: int
    task_id: str
    pattern: str
    success: bool
    duration_ms: float
    agent_results_count: int
    cb_state: str
    backend_failed: bool
    error: Optional[str] = None
    extra: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        d = asdict(self)
        return d


class HistoryStore:
    def __init__(self, maxlen: int = 200):
        self._entries: Deque[HistoryEntry] = deque(maxlen=maxlen)

    def append(self, entry: HistoryEntry) -> None:
        self._entries.append(entry)

    def get_recent(self, n: int = 10) -> List[Dict[str, Any]]:
        # return newest first
        return [e.to_dict() for e in list(self._entries)[-n:]][::-1]

    def stats(self) -> Dict[str, Any]:
        total = len(self._entries)
        successes = sum(1 for e in self._entries if e.success)
        failures = total - successes
        last_10 = list(self._entries)[-10:]
        avg_ms = sum(e.duration_ms for e in last_10) / max(1, len(last_10)) if last_10 else 0.0
        return {
            "total": total,
            "successes": successes,
            "failures": failures,
            "avg_last10_ms": avg_ms,
        }

    def last_error(self) -> Optional[str]:
        for e in reversed(self._entries):
            if not e.success and e.error:
                return e.error
        return None
