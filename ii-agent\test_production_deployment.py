#!/usr/bin/env python3
"""
Production Deployment Test Suite
===============================

Comprehensive test suite for production deployment including:
- GPU acceleration verification
- Multi-agent coordination testing
- Monitoring system validation
- Health check verification
- Performance benchmarking
"""

import asyncio
import logging
import sys
import time
import requests
import json
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def test_production_health_checks():
    """Test production health monitoring system"""
    logger.info("🏥 Testing Production Health Checks...")
    
    try:
        from ii_agent_integration.monitoring.health_checks import get_health_monitor, quick_health_check
        
        # Test health monitor initialization
        health_monitor = get_health_monitor()
        logger.info(f"   Health monitor initialized: {health_monitor is not None}")
        
        # Run comprehensive health check
        health_result = await health_monitor.run_comprehensive_health_check()
        
        logger.info(f"✅ Health Check Results:")
        logger.info(f"   Overall Status: {health_result['overall_status']}")
        logger.info(f"   Total Check Time: {health_result['total_check_time_ms']:.2f}ms")
        logger.info(f"   Healthy Components: {health_result['summary']['healthy_components']}")
        logger.info(f"   Warning Components: {health_result['summary']['warning_components']}")
        logger.info(f"   Critical Components: {health_result['summary']['critical_components']}")
        
        # Test individual components
        for component, status in health_result['components'].items():
            status_emoji = "✅" if status['status'] == 'healthy' else "⚠️" if status['status'] == 'warning' else "❌"
            logger.info(f"   {status_emoji} {component}: {status['message']}")
        
        # Test quick health check
        quick_result = await quick_health_check()
        logger.info(f"   Quick health check: {quick_result['overall_status']}")
        
        return health_result['overall_status'] in ['healthy', 'warning']
        
    except Exception as e:
        logger.error(f"❌ Health checks test failed: {e}")
        return False


async def test_production_metrics():
    """Test production metrics collection"""
    logger.info("📊 Testing Production Metrics...")
    
    try:
        from ii_agent_integration.monitoring.production_metrics import get_metrics_collector, start_metrics_collection, MetricsConfig
        
        # Test metrics collector initialization
        config = MetricsConfig(
            enable_gpu_metrics=True,
            enable_system_metrics=True,
            enable_coordination_metrics=True,
            enable_neural_metrics=True,
            collection_interval=1.0
        )
        
        metrics_collector = get_metrics_collector(config)
        logger.info(f"   Metrics collector initialized: {metrics_collector is not None}")
        
        # Start metrics collection
        metrics_collector.start_collection()
        logger.info("   Metrics collection started")
        
        # Wait for some metrics to be collected
        await asyncio.sleep(2)
        
        # Test metrics recording
        metrics_collector.record_coordination_metrics(
            strategy="test_strategy",
            duration=0.1,
            success=True,
            consensus_score=0.85,
            agent_count=5
        )
        
        metrics_collector.record_neural_metrics(
            agent_id="test_agent",
            inference_time=0.05,
            confidence=0.9,
            training_loss=0.1,
            device="cpu"
        )
        
        # Get metrics summary
        summary = metrics_collector.get_metrics_summary()
        
        logger.info(f"✅ Metrics Collection Results:")
        logger.info(f"   Collection Active: {summary['collection_active']}")
        logger.info(f"   GPU Available: {summary['gpu_available']}")
        logger.info(f"   System CPU: {summary['system']['cpu_usage']:.1f}%")
        logger.info(f"   System Memory: {summary['system']['memory_usage']:.1f}%")
        
        # Stop collection
        metrics_collector.stop_collection()
        logger.info("   Metrics collection stopped")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Metrics test failed: {e}")
        return False


async def test_production_multi_agent_system():
    """Test production multi-agent system with all features"""
    logger.info("🤖 Testing Production Multi-Agent System...")
    
    try:
        from ii_agent_integration.swarms.real_multi_agent_system import RealMultiAgentSystem, SwarmConfig
        
        # Create production configuration
        config = SwarmConfig(
            max_agents=5,
            enable_gpu=True,  # Will fallback to CPU if GPU not available
            enable_learning=True,
            coordination_timeout_ms=30000,
            consensus_threshold=0.7
        )
        
        # Initialize production swarm
        swarm = RealMultiAgentSystem(config)
        
        logger.info(f"✅ Production Swarm Initialized:")
        logger.info(f"   Total Agents: {len(swarm.agents)}")
        logger.info(f"   GPU Available: {swarm.gpu_available}")
        logger.info(f"   Swarm State: {swarm.state.value}")
        
        # Test all coordination strategies
        test_tasks = [
            ("mixture_of_agents", "Design a scalable microservices architecture with load balancing"),
            ("consensus_swarm", "Analyze security vulnerabilities in distributed systems"),
            ("hierarchical_swarm", "Optimize database performance for high-traffic applications")
        ]
        
        coordination_results = []
        
        for strategy, task in test_tasks:
            logger.info(f"   Testing {strategy}...")
            
            start_time = time.perf_counter()
            result = await swarm.coordinate_task(task, strategy)
            execution_time = (time.perf_counter() - start_time) * 1000
            
            coordination_results.append({
                "strategy": strategy,
                "success": result.get("success", False),
                "execution_time_ms": execution_time,
                "consensus_score": result.get("result", {}).get("consensus_score", 0)
            })
            
            logger.info(f"     Success: {result.get('success', False)}")
            logger.info(f"     Time: {execution_time:.2f}ms")
            logger.info(f"     Consensus: {result.get('result', {}).get('consensus_score', 0):.3f}")
        
        # Test GPU benchmarking if available
        if swarm.gpu_available:
            logger.info("   Running GPU performance benchmark...")
            benchmark_result = await swarm.benchmark_gpu_performance(
                "Benchmark GPU performance across neural agents", iterations=3
            )
            
            if benchmark_result.get("overall_speedup", 0) > 0:
                logger.info(f"   🚀 GPU Speedup: {benchmark_result['overall_speedup']:.2f}x")
        
        # Get comprehensive swarm status
        swarm_status = swarm.get_swarm_status()
        
        logger.info(f"✅ Production Swarm Status:")
        logger.info(f"   Performance Metrics: {swarm_status['performance_metrics']}")
        logger.info(f"   Recent Coordinations: {len(swarm_status['recent_coordinations'])}")
        
        # Calculate success rate
        successful_coordinations = sum(1 for r in coordination_results if r["success"])
        success_rate = successful_coordinations / len(coordination_results) * 100
        
        logger.info(f"   Coordination Success Rate: {success_rate:.1f}%")
        
        return success_rate >= 80  # Require 80% success rate
        
    except Exception as e:
        logger.error(f"❌ Production multi-agent system test failed: {e}")
        return False


async def test_production_integration():
    """Test production integration with swarms framework"""
    logger.info("🔗 Testing Production Integration...")
    
    try:
        from ii_agent_integration.swarms.swarms_integration import SwarmsFrameworkIntegration
        
        # Initialize integration
        integration = SwarmsFrameworkIntegration(coordination_hub=None)
        
        # Test real neural coordination
        test_task = "Implement a production-ready caching strategy for distributed applications"
        
        if hasattr(integration, '_real_swarm') and integration._real_swarm:
            logger.info("   Testing real neural coordination...")
            
            result = await integration.execute_real_neural_coordination(test_task, "mixture_of_agents")
            
            logger.info(f"✅ Integration Results:")
            logger.info(f"   Success: {result.success}")
            logger.info(f"   Strategy: {result.strategy}")
            logger.info(f"   Neural Coordination: {result.metadata.get('neural_coordination', False)}")
            logger.info(f"   Execution Time: {result.metadata.get('execution_time_ms', 0):.2f}ms")
            logger.info(f"   Consensus Score: {result.metadata.get('consensus_score', 0):.3f}")
            
            # Test swarm status
            swarm_status = await integration.get_real_swarm_status()
            logger.info(f"   Swarm State: {swarm_status.get('swarm_state', 'unknown')}")
            logger.info(f"   Total Agents: {swarm_status.get('total_agents', 0)}")
            
            return result.success
        else:
            logger.warning("   Real neural swarm not available in integration")
            return False
        
    except Exception as e:
        logger.error(f"❌ Production integration test failed: {e}")
        return False


async def test_production_performance():
    """Test production performance benchmarks"""
    logger.info("⚡ Testing Production Performance...")
    
    try:
        # Test neural network performance
        from ii_agent_integration.neural.real_neural_networks import RealNeuralAgent, AgentSpecialization, NeuralConfig
        
        config = NeuralConfig(input_size=512, hidden_sizes=[256, 128, 64], output_size=32)
        agent = RealNeuralAgent("perf_test_agent", AgentSpecialization.OPTIMIZER, config)
        
        # Performance test
        test_tasks = [
            "Optimize system performance",
            "Analyze bottlenecks",
            "Implement caching strategy",
            "Scale database operations",
            "Monitor application health"
        ]
        
        total_time = 0
        inference_times = []
        
        for task in test_tasks:
            start_time = time.perf_counter()
            result = await agent.analyze_task(task)
            execution_time = (time.perf_counter() - start_time) * 1000
            
            total_time += execution_time
            inference_times.append(execution_time)
        
        avg_inference_time = total_time / len(test_tasks)
        min_inference_time = min(inference_times)
        max_inference_time = max(inference_times)
        
        logger.info(f"✅ Performance Results:")
        logger.info(f"   Total Tasks: {len(test_tasks)}")
        logger.info(f"   Total Time: {total_time:.2f}ms")
        logger.info(f"   Average Inference: {avg_inference_time:.2f}ms")
        logger.info(f"   Min Inference: {min_inference_time:.2f}ms")
        logger.info(f"   Max Inference: {max_inference_time:.2f}ms")
        logger.info(f"   Throughput: {len(test_tasks) / (total_time / 1000):.2f} tasks/sec")
        
        # Performance criteria: average inference should be under 100ms
        return avg_inference_time < 100
        
    except Exception as e:
        logger.error(f"❌ Performance test failed: {e}")
        return False


async def test_production_error_handling():
    """Test production error handling and recovery"""
    logger.info("🛡️ Testing Production Error Handling...")
    
    try:
        from ii_agent_integration.swarms.real_multi_agent_system import RealMultiAgentSystem, SwarmConfig
        
        config = SwarmConfig(max_agents=3, enable_gpu=False)
        swarm = RealMultiAgentSystem(config)
        
        # Test error scenarios
        error_scenarios = [
            ("empty_task", ""),
            ("invalid_strategy", "Test task"),
            ("malformed_input", None)
        ]
        
        error_handling_results = []
        
        for scenario_name, test_input in error_scenarios:
            try:
                if scenario_name == "invalid_strategy":
                    result = await swarm.coordinate_task(test_input, "invalid_strategy_name")
                elif scenario_name == "empty_task":
                    result = await swarm.coordinate_task(test_input, "mixture_of_agents")
                else:
                    result = await swarm.coordinate_task(test_input, "mixture_of_agents")
                
                # Should handle errors gracefully
                error_handling_results.append({
                    "scenario": scenario_name,
                    "handled_gracefully": not result.get("success", True),  # Should fail gracefully
                    "error_message": result.get("error", "No error message")
                })
                
            except Exception as e:
                # Exceptions should be caught and handled
                error_handling_results.append({
                    "scenario": scenario_name,
                    "handled_gracefully": True,
                    "error_message": str(e)
                })
        
        logger.info(f"✅ Error Handling Results:")
        for result in error_handling_results:
            status = "✅" if result["handled_gracefully"] else "❌"
            logger.info(f"   {status} {result['scenario']}: {result['error_message'][:50]}...")
        
        # All error scenarios should be handled gracefully
        all_handled = all(r["handled_gracefully"] for r in error_handling_results)
        return all_handled
        
    except Exception as e:
        logger.error(f"❌ Error handling test failed: {e}")
        return False


async def main():
    """Run all production deployment tests"""
    logger.info("🎯 Starting Production Deployment Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Health Checks", test_production_health_checks),
        ("Metrics Collection", test_production_metrics),
        ("Multi-Agent System", test_production_multi_agent_system),
        ("Integration", test_production_integration),
        ("Performance", test_production_performance),
        ("Error Handling", test_production_error_handling)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running {test_name} Test...")
        try:
            success = await test_func()
            results[test_name] = success
            if success:
                logger.info(f"✅ {test_name} Test PASSED")
            else:
                logger.error(f"❌ {test_name} Test FAILED")
        except Exception as e:
            logger.error(f"💥 {test_name} Test CRASHED: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 PRODUCTION DEPLOYMENT TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"   {test_name}: {status}")
    
    logger.info(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL PRODUCTION TESTS PASSED! System is production-ready!")
        logger.info("\n🚀 Ready for deployment with:")
        logger.info("   • Real GPU acceleration")
        logger.info("   • Production monitoring")
        logger.info("   • Comprehensive health checks")
        logger.info("   • Error handling & recovery")
        logger.info("   • Performance optimization")
        return True
    else:
        logger.error(f"💔 {total - passed} tests failed. System needs fixes before production.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
