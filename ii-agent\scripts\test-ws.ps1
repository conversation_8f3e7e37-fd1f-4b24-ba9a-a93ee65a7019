param()
$ErrorActionPreference = 'Stop'

Add-Type -AssemblyName System.Net.Http
Add-Type -AssemblyName System.Runtime

$ws = [System.Net.WebSockets.ClientWebSocket]::new()
$deviceId = [guid]::NewGuid().ToString()
$uri = [Uri]("ws://localhost:8000/ws?device_id=$deviceId")
$cts = New-Object System.Threading.CancellationTokenSource

Write-Host "Connecting to $($uri.AbsoluteUri) ..."
$ws.ConnectAsync($uri, $cts.Token).GetAwaiter().GetResult()
Write-Host "Connected"

function Receive-Message([int]$timeoutSec=10){
  $buffer = New-Object byte[] 65536
  $segment = New-Object System.ArraySegment[byte] -ArgumentList (, $buffer)
  $recvCts = New-Object System.Threading.CancellationTokenSource
  $recvCts.CancelAfter($timeoutSec*1000)
  $sb = New-Object System.Text.StringBuilder
  while($true){
    $result = $ws.ReceiveAsync($segment, $recvCts.Token).GetAwaiter().GetResult()
    if($result.Count -gt 0){
      $text = [System.Text.Encoding]::UTF8.GetString($buffer, 0, $result.Count)
      [void]$sb.Append($text)
    }
    if($result.EndOfMessage){ break }
  }
  return $sb.ToString()
}

function Send-Text([string]$text){
  $bytes = [System.Text.Encoding]::UTF8.GetBytes($text)
  $segment = New-Object System.ArraySegment[byte] -ArgumentList (, $bytes)
  $ws.SendAsync($segment, [System.Net.WebSockets.WebSocketMessageType]::Text, $true, $cts.Token).GetAwaiter().GetResult()
}

# Wait for handshake
$handshake = Receive-Message -timeoutSec 10
if($handshake){ Write-Host "RX:" $handshake }

# init_agent message
$init = @{ type='init_agent'; content=@{ model_name='gemini/gemini-2.0-flash'; tool_args=@{ sequential_thinking=$false; deep_research=$false; pdf=$false; media_generation=$false; audio_generation=$false; browser=$false }; thinking_tokens=0 } } | ConvertTo-Json -Depth 6
Send-Text $init
Write-Host 'Sent init_agent'

# Read a few messages waiting for agent initialized
$initialized=$false
$end=(Get-Date).AddSeconds(15)
while((Get-Date) -lt $end){
  $msg = Receive-Message -timeoutSec 5
  if(-not [string]::IsNullOrWhiteSpace($msg)){
    Write-Host "RX2:" $msg
    if($msg -match 'agent_initialized' -or $msg -match 'AGENT_INITIALIZED'){ $initialized=$true; break }
  }
}

# Send query
$query = @{ type='query'; content=@{ text='Start a new web app project and set up the scaffolding.'; resume=$false; files=@() } } | ConvertTo-Json -Depth 6
Send-Text $query
Write-Host 'Sent query'

$toolCode=$false; $notFound=$false
$end=(Get-Date).AddSeconds(30)
while((Get-Date) -lt $end){
  $msg = Receive-Message -timeoutSec 5
  if(-not [string]::IsNullOrWhiteSpace($msg)){
    Write-Host "EVT:" $msg
    if($msg -match '```tool_code'){ $toolCode=$true }
    if($msg -match 'Tool with name .* not found'){ $notFound=$true }
  }
}

Write-Host ("Summary: tool_code_seen={0} tool_name_not_found={1}" -f $toolCode,$notFound)

$ws.Dispose()

