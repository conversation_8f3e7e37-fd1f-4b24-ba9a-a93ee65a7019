"""
Swarms Backend Adapter Interface

Define a minimal contract to integrate external Swarms backends without
changing the internal coordination API.
"""

from typing import Any, Dict, Optional
from .swarms_integration import SwarmsCoordinationPattern, CoordinationResult


class SwarmsBackendAdapter:
    """Abstract adapter for external swarms backends."""

    async def execute(self, pattern: SwarmsCoordinationPattern, task: Any, agents: list) -> Optional[CoordinationResult]:
        """Execute coordination via an external backend.

        Return a CoordinationResult if handled; return None to let local
        implementation handle it.
        """
        try:
            # REAL IMPLEMENTATION: Use local multi-agent system as backend
            from .real_multi_agent_system import RealMultiAgentSystem
            import asyncio
            import time

            start_time = time.time()

            # Create real multi-agent system instance
            backend_system = RealMultiAgentSystem()

            # Extract task text
            if hasattr(task, 'content'):
                task_text = task.content.get("text", str(task))
            else:
                task_text = str(task)

            # Map pattern to coordination strategy
            strategy_mapping = {
                SwarmsCoordinationPattern.MIXTURE_OF_AGENTS: "mixture_of_agents",
                SwarmsCoordinationPattern.HIERARCHICAL_SWARM: "hierarchical_swarm",
                SwarmsCoordinationPattern.CONSENSUS_SWARM: "consensus_swarm"
            }

            strategy = strategy_mapping.get(pattern, "mixture_of_agents")

            # Execute real coordination
            result = await backend_system.coordinate_task(task_text, strategy)

            execution_time = (time.time() - start_time) * 1000

            # Convert to CoordinationResult format
            if result.get("success", False):
                return CoordinationResult(
                    task_id=getattr(task, 'task_id', str(hash(str(task)))),
                    success=True,
                    strategy=f"backend_{strategy}",
                    agent_results=result.get("result", {}).get("agent_responses", []),
                    metadata={
                        "backend_execution_time_ms": execution_time,
                        "original_execution_time_ms": result.get("execution_time_ms", 0),
                        "participating_agents": result.get("participating_agents", 0),
                        "consensus_score": result.get("result", {}).get("consensus_score", 0.0)
                    }
                )
            else:
                # Return None to let local implementation handle it
                return None

        except Exception as e:
            # Log error but don't fail - let local implementation handle it
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Backend adapter failed, falling back to local: {e}")
            return None
