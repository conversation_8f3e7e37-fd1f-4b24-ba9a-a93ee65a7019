# Production Multi-Agent Docker Compose with Security Hardening
# Usage: docker-compose -f docker-compose.yaml -f docker-compose.multi-agent.yaml up

services:
  # Main backend with GPU acceleration and security hardening
  backend:
    environment:
      # 🚀 REAL MULTI-AGENT CONFIGURATION
      II_AGENT_MULTI_AGENT: "true"
      II_AGENT_GPU_ENABLED: "${II_AGENT_GPU_ENABLED:-true}"
      II_AGENT_GPU_DEVICE_ID: "${II_AGENT_GPU_DEVICE_ID:-0}"
      II_AGENT_MIXED_PRECISION: "${II_AGENT_MIXED_PRECISION:-true}"
      II_AGENT_GPU_MEMORY_STRATEGY: "${II_AGENT_GPU_MEMORY_STRATEGY:-balanced}"
      II_AGENT_MAX_AGENTS: "${II_AGENT_MAX_AGENTS:-10}"
      II_AGENT_COORDINATION_TIMEOUT: "${II_AGENT_COORDINATION_TIMEOUT:-30000}"
      II_AGENT_ENABLE_LEARNING: "${II_AGENT_ENABLE_LEARNING:-true}"

      # 🧠 NEURAL NETWORK CONFIGURATION
      II_AGENT_NEURAL_INPUT_SIZE: "${II_AGENT_NEURAL_INPUT_SIZE:-512}"
      II_AGENT_NEURAL_HIDDEN_SIZES: "${II_AGENT_NEURAL_HIDDEN_SIZES:-256,128,64}"
      II_AGENT_NEURAL_OUTPUT_SIZE: "${II_AGENT_NEURAL_OUTPUT_SIZE:-32}"
      II_AGENT_LEARNING_RATE: "${II_AGENT_LEARNING_RATE:-0.001}"
      II_AGENT_BATCH_SIZE: "${II_AGENT_BATCH_SIZE:-64}"

      # 📊 MONITORING & PERFORMANCE
      II_AGENT_ENABLE_METRICS: "${II_AGENT_ENABLE_METRICS:-true}"
      II_AGENT_METRICS_PORT: "${II_AGENT_METRICS_PORT:-9090}"
      II_AGENT_LOG_LEVEL: "${II_AGENT_LOG_LEVEL:-INFO}"
      II_AGENT_PERFORMANCE_TRACKING: "${II_AGENT_PERFORMANCE_TRACKING:-true}"

      # 🔐 SECURITY CONFIGURATION
      II_AGENT_METRICS_AUTH_USER: "${II_AGENT_METRICS_AUTH_USER:-admin}"
      II_AGENT_METRICS_AUTH_PASS: "${II_AGENT_METRICS_AUTH_PASS:-secure_metrics_2024}"

      # Existing environment variables
      GOOGLE_APPLICATION_CREDENTIALS: /app/google-application-credentials.json
      STATIC_FILE_BASE_URL: ${STATIC_FILE_BASE_URL:-http://localhost:8000}
      BASE_URL: ${BASE_URL}
      COMPOSE_PROJECT_NAME: ${COMPOSE_PROJECT_NAME}
      HOST_WORKSPACE_PATH: ${HOME}/.ii_agent/workspace
      CODE_SERVER_PORT: ${CODE_SERVER_PORT:-9000}
      GOOGLE_AI_STUDIO_API_KEY: ${GOOGLE_AI_STUDIO_API_KEY}
      II_DEFAULT_MODEL: gemini-2.0-flash

      # 🐍 PYTHON CONFIGURATION
      PYTHONPATH: /app/src:/app/ii_agent_integration:/app
      PYTORCH_CUDA_ALLOC_CONF: "max_split_size_mb:512"
      CUDA_VISIBLE_DEVICES: "${CUDA_VISIBLE_DEVICES:-0}"

    # 🚀 GPU SUPPORT (when available)
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

    # 🔐 SECURITY HARDENING
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - DAC_OVERRIDE
      - FOWNER
      - SETGID
      - SETUID

    volumes:
      # 🔧 Multi-agent integration
      - ./ii_agent_integration:/app/ii_agent_integration

      # 💾 Model persistence
      - ii_agent_models:/app/models
      - ii_agent_cache:/app/.cache

      # 📊 Logs and metrics
      - ii_agent_logs:/app/logs
      - ii_agent_metrics:/app/metrics

      # Existing volumes
      - ${GOOGLE_APPLICATION_CREDENTIALS:-./docker/.dummy-credentials.json}:/app/google-application-credentials.json
      - ~/.ii_agent:/.ii_agent
      - /var/run/docker.sock:/var/run/docker.sock

    # 🌐 NETWORKING
    networks:
      - ii_production

    # 🔍 HEALTH CHECK
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    # 🔄 RESTART POLICY
    restart: unless-stopped

    # 📊 LOGGING
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  # 📊 MONITORING STACK
  prometheus:
    image: prom/prometheus:latest
    container_name: ii_agent_prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - ii_production
    # 🔐 SECURITY HARDENING
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL

  # 📈 GRAFANA DASHBOARD
  grafana:
    image: grafana/grafana:latest
    container_name: ii_agent_grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SECURITY_DISABLE_GRAVATAR=true
      - GF_SECURITY_COOKIE_SECURE=true
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - ii_production
    # 🔐 SECURITY HARDENING
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL

  # 🔍 NODE EXPORTER (System Metrics)
  node_exporter:
    image: prom/node-exporter:latest
    container_name: ii_agent_node_exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - ii_production
    # 🔐 SECURITY HARDENING
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - SYS_TIME  # Required for time-related metrics

  # 🐳 CADVISOR (Container Metrics) - SECURITY HARDENED
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: ii_agent_cadvisor
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    networks:
      - ii_production
    # 🔐 SECURITY HARDENING - REMOVED PRIVILEGED ACCESS
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - SYS_ADMIN  # Minimal required capability for container monitoring
      - DAC_OVERRIDE  # Required for reading container stats
    # REMOVED: privileged: true (SECURITY FIX)
    # REMOVED: devices access to /dev/kmsg (SECURITY FIX)

# 💾 PERSISTENT VOLUMES
volumes:
  ii_agent_models:
    driver: local
  ii_agent_cache:
    driver: local
  ii_agent_logs:
    driver: local
  ii_agent_metrics:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# 🌐 NETWORKS
networks:
  ii_production:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
