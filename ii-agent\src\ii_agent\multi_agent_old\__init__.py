"""
ii-agent Multi-Agent Components

This module provides multi-agent capabilities for ii-agent, including:
- AgentRegistry: Multi-agent registration and discovery
- TaskCoordinator: Task coordination and distribution  
- SwarmsIntegration: Advanced Swarms framework integration
- NeuralSwarmsAgent: RUV-FANN neural-powered agents
"""

# Core multi-agent components
from .agent_registry import Agent<PERSON><PERSON><PERSON><PERSON>, RegistrationStatus, AgentInfo
from .task_coordinator import TaskCoordinator, Task, TaskStatus, TaskPriority

# Swarms framework integration
from .swarms_integration import SwarmsFrameworkIntegration, SwarmsCoordinationPattern
from .coordination_patterns import CoordinationPatterns

# Phase 2: Enhanced routing and analysis
from .query_analyzer import QueryAnalyzer, QueryAnalysis, QueryComplexity, CoordinationStrategy
from .message_router import MessageRouter, RoutingResponse, RoutingResult
from .neural_swarms_agent import NeuralSwarmsAgent
from .enterprise_agent_pool import EnterpriseAgentPool
from .swarms_coordinator import SwarmsCoordinator

__all__ = [
    # Core components
    "AgentRegistry",
    "RegistrationStatus", 
    "AgentInfo",
    "TaskCoordinator",
    "Task",
    "TaskStatus",
    "TaskPriority",
    # Swarms components
    "SwarmsFrameworkIntegration",
    "SwarmsCoordinationPattern",
    "CoordinationPatterns",
    "NeuralSwarmsAgent",
    "EnterpriseAgentPool",
    "SwarmsCoordinator",
    # Phase 2: Enhanced routing
    "QueryAnalyzer",
    "QueryAnalysis", 
    "QueryComplexity",
    "CoordinationStrategy",
    "MessageRouter",
    "RoutingResponse",
    "RoutingResult"
]

__version__ = "1.0.0"
