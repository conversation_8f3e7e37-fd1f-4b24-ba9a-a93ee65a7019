"""
Quick Phase 2 Integration Test
Tests the current simple_neural.py integration without relative import issues
"""

import sys
import os
import time

# Test direct import from current directory
try:
    import simple_neural
    from simple_neural import Simple<PERSON>euralA<PERSON>, PHASE1_AVAILABLE, PHASE2_AVAILABLE
    print("✅ Direct import successful")
except ImportError as e:
    print(f"❌ Direct import failed: {e}")

def quick_test():
    """Quick test of Phase 2 integration."""
    print("\n🧪 Quick Phase 2 Integration Test")
    print("=" * 40)
    
    print(f"Phase 1 Available: {PHASE1_AVAILABLE}")
    print(f"Phase 2 Available: {PHASE2_AVAILABLE}")
    print(f"Neural Available: {simple_neural.NEURAL_AVAILABLE}")
    
    # Test agent creation with different configurations
    print(f"\n🔧 Testing Agent Configurations:")
    
    # Test 1: Full WASM enabled
    agent1 = SimpleNeuralAgent(enable_neural=True, enable_wasm=True)
    print(f"  WASM + Neural: {agent1.active_phase}")
    
    # Test 2: Neural only (no WASM)
    agent2 = SimpleNeuralAgent(enable_neural=True, enable_wasm=False)
    print(f"  Neural Only: {agent2.active_phase}")
    
    # Test 3: Fallback only
    agent3 = SimpleNeuralAgent(enable_neural=False)
    print(f"  Fallback Only: {agent3.active_phase}")
    
    # Test analysis
    test_message = "I need help implementing a machine learning algorithm"
    
    print(f"\n🔍 Testing Analysis: '{test_message}'")
    
    for i, agent in enumerate([agent1, agent2, agent3], 1):
        start_time = time.perf_counter()
        result = agent.get_neural_analysis(test_message)
        end_time = time.perf_counter()
        
        processing_time = (end_time - start_time) * 1000
        
        print(f"\n  Agent {i} ({agent.active_phase}):")
        print(f"    Agent Type: {result['agent_type']}")
        print(f"    Confidence: {result['confidence']:.3f}")
        print(f"    Strategy: {result['strategy']}")
        print(f"    Processing Time: {processing_time:.3f}ms")
        
        if 'performance' in result:
            perf = result['performance']
            print(f"    Phase: {perf['phase']}")
            print(f"    WASM Accelerated: {perf.get('wasm_accelerated', False)}")
            print(f"    Neural Networks: {perf.get('neural_networks', False)}")
    
    # Test performance info
    perf_info = agent1.get_performance_info()
    print(f"\n📊 Performance Info:")
    print(f"  Active Phase: {perf_info['active_phase']}")
    print(f"  Capabilities: {perf_info['capabilities']}")
    
    print(f"\n✅ Quick Integration Test Complete!")
    
    return {
        "phase1_available": PHASE1_AVAILABLE,
        "phase2_available": PHASE2_AVAILABLE,
        "neural_available": simple_neural.NEURAL_AVAILABLE,
        "agents_tested": 3,
        "test_passed": True
    }

if __name__ == "__main__":
    try:
        results = quick_test()
        print(f"\n📋 Test Results: {results}")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
